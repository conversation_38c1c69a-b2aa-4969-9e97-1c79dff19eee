{"files.exclude": {"**/db-data": true, "**/redis-data": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}, "search.exclude": {"**/db-data": true, "**/redis-data": true, "**/output": true, "**/package-lock.json": true, "**/client/package-lock.json": true}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 4, "typescript.tsdk": "node_modules/typescript/lib", "js/ts.implicitProjectConfig.experimentalDecorators": true}