// InstanceSet -
type InstanceSet = {
    instanceSpec: any;
    count: number;
};
// CreateClusterConfig
interface CreateClusterConfig {
    accountID: string;
    userID: string;
    // 集群最大节点数最低值（推荐）
    recommendNodeScale: boolean;
    // 推荐的单节点最低值（推荐）
    recommendPodScale: number;
    clusterSpec: any;
    masterSpecs: InstanceSet[];
    nodeSpecs: InstanceSet[];
}

// UpgradeMasterK8SVersionWorkflowConfig - K8S Master 版本升级参数
export interface UpgradeMasterK8SVersionWorkflowConfig {
    targetK8SVersion: string;
    masterPasswords: Record<string, string>;
}

export interface UpgradeClusterK8SVersionWorkflowConfig {
    targetK8SVersion: string;
    masterPasswords: Record<string, string>;
    /** 并行升级 Node 数, 默认 = 1 串行升级 */
    nodeUpgradeBatchSize: number;
    /** 默认true */
    drainNodeBeforeUpgrade: boolean;
}
export interface UpgradeNodesK8SVersionWorkflowTaskConfig {
    cceInstanceIDList: string[];
    drainNodeBeforeUpgrade: boolean;
    targetK8SVersion: string;
}
export interface UpgradeNodesK8SVersionWorkflowConfig
    extends UpgradeNodesK8SVersionWorkflowTaskConfig {
    // 升级全量节点, upgradeAllNodes=true 覆盖 CCEInstanceIDList
    upgradeAllNodes: boolean;
    // 并行升级 Node 数, 默认 = 1 串行升级
    nodeUpgradeBatchSize: number;
}

export interface UpgradeMasterK8SVersionPreCheckConfig {
    fromK8SVersion: string;
    toK8SVersion: string;
    masterPasswords: Record<string, string>;
}

export interface WorkflowConfig {
    masterContainerizedWorkflowConfig?: string;

    upgradeClusterK8SVersionWorkflowConfig?: UpgradeClusterK8SVersionWorkflowConfig;

    upgradeMasterK8SVersionWorkflowConfig?: UpgradeMasterK8SVersionWorkflowConfig;
    // nodes 升级
    upgradeNodesK8SVersionWorkflowConfig?: UpgradeNodesK8SVersionWorkflowConfig;

    createClusterPrecheckWorkflowConfig?: CreateClusterConfig;
    upgradeMasterK8SVersionPreCheckConfig: UpgradeMasterK8SVersionPreCheckConfig;
}

// WorkflowSpec - Workflow WorkflowSpec, 每种操作 Workflow 包含两部分信息:
// 1. 要做什么变更: WorkflowConfig
// 2. 如何执行变更: WorkflowTaskList
export interface WorkflowSpec {
    handler: string;
    workflowID: string;
    clusterID: string;
    accountID: string;
    userID: string;
    paused: boolean;

    workflowType: string;
    config: WorkflowConfig;

    // 变更中监测关联集群状态
    watchDogConfig: {
        unhealthyPodsPercent: number;
    };
}

export interface RawMessage {
    description?: string;
    message?: string;
    result?: 'passed';
    cceInstanceIDList?: string[];
}

export interface WorkflowTask {
    taskName: string;
    workflowTaskType: string;
    workflowTaskPhase: WorkflowPhase;
    taskConfig: RawMessage | UpgradeNodesK8SVersionWorkflowTaskConfig;
    taskExecuteResult: RawMessage;
    startTime: string;
    finishedTime: string;
}

export type WorkflowPhase =
    | 'Failed'
    | 'Succeeded'
    | 'Upgrading'
    | 'Unknown'
    | 'Pending'
    | 'Paused'
    | 'deleting'
    | 'deleted';

export interface TaskGroup {
    taskGroupName: string;
    taskList: WorkflowTask[];
    taskGroupPhase: WorkflowPhase;
    pause: boolean;
}

export interface WatchDogStatus {
    healthy: boolean;
    totalPodCount: number;
    /** "2022-03-21T09:07:58Z" */
    updateTime: string;
    unhealthyPodCount: number;
    pausedReason: string;
}

export interface WorkflowStatus {
    finishedTaskCount: number;
    totalTaskCount: number;
    finishedTime: string;
    taskGroupList: TaskGroup[];
    errorMessage: string;
    pausedReason: string;
    phase: WorkflowPhase;
    watchDogStatus: WatchDogStatus;
}

export interface Workflow {
    spec: WorkflowSpec;
    status: WorkflowStatus;
}
