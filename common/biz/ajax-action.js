/**
 * @file inf-ui/x/biz/ajaxAction.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Toast, ConfirmDialog} from '@baiducloud/bce-ui/san';

import {createPayload, asPromise} from './helper';

export function ajaxAction(config, payload) {
    const {api, $payloadFields, $extraPayload} = config;

    const {confirmText, confirmTitle, $toastMessage, skin, foot} = config;

    const {$onRequest, $before, $onResponse, $done, $onError, $error} = config;
    const onRequest = $onRequest || $before;
    const onResponse = $onResponse || $done;
    const onError = $onError || $error;

    const doRequest = (api, data, options) => {
        if (_.isString(api)) {
            return this.$http[api](data, options);
        }
        return api.then(request => request(data, options));
    };

    const sendRequest = () => {
        const requestPayload = createPayload(payload, $payloadFields, $extraPayload);
        if (typeof onRequest === 'function') {
            onRequest.call(this, requestPayload);
        }

        return doRequest(api, requestPayload)
            .then(response => {
                if ($toastMessage) {
                    Toast.success($toastMessage, 3000);
                }
                if (typeof onResponse === 'function') {
                    return onResponse.call(this, response, requestPayload);
                }
                return this.refreshTable();
            })
            .catch(error => {
                if (typeof onError === 'function') {
                    onError.call(this, error, requestPayload);
                }

                if (error.global) {
                    Toast.error(error.global);
                }
                this.data.set('error', error);
            });
    };

    if (confirmText) {
        const message = _.template(confirmText)(payload);
        const title = _.template(confirmTitle)(payload);
        const dialog = new ConfirmDialog({data: {message, skin, title, foot}});
        dialog.attach(document.body);
        this.$childs.push(dialog);
        return asPromise(dialog).then(sendRequest);
    }
    return sendRequest();
}
