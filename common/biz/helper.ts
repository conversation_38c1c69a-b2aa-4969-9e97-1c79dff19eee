/**
 * @file inf-ui/x/biz/helper.js
 * <AUTHOR>
 */

import _ from 'lodash';
import moment from 'moment';
import {defineComponent} from 'san';
import {isSanCmpt, html} from '@baiducloud/runtime';
import * as SanUI from '@baiducloud/bce-ui/san';
import {Button, Tooltip} from '@baidu/sui';
import {TipButton} from '@baidu/sui-biz';

export function createPayload(payload, fields, extra) {
    // fields: ['a', 'b', 'c'] -> _.pick(payload, fields);
    // fields: ['a', ['id', 'userId'], 'c'] ->
    const requestPayload = fields ? {} : _.extend({}, payload);
    _.each(fields, key => {
        if (_.isArray(key)) {
            const [a, b] = key;
            requestPayload[b] = payload[a];
        } else if (_.isString(key)) {
            requestPayload[key] = payload[key];
        }
    });
    return _.extend(requestPayload, extra);
}

export function createToolbar(toolbar) {
    return _.map(toolbar, item => {
        if (item.type === 'button') {
            const btn = _.clone(item);
            if (btn.primary) {
                btn.skin = 'primary';
            }
            return btn;
        } else if (item.type === 'button-group') {
            const btnGroup = {
                type: item.type,
                value: item.$value || item.buttons[0].$value,
                datasource: _.map(item.buttons, btn => {
                    const {label, $value} = btn;
                    const props = _.omit(btn, 'label', '$value');
                    return _.extend({text: label, value: $value}, props);
                }),
            };
            return btnGroup;
        }
        return item;
    });
}

export function matchAll(compProxy, when) {
    const keys = _.keys(when);
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = when[key];
        if (compProxy.data.get(key) !== value) {
            return false;
        }
    }
    return true;
}

export function valueTransform(formData) {
    const transformedData = {};
    const keyMap = formData.__s_key || [];
    _.each(formData, (v, k) => {
        if (/^__key_(.*)$/.test(k)) {
            const config = keyMap[+RegExp.$1];
            if (!config) {
                return;
            }
            if (config.type === 'p') {
                // 没有对应的 key，把 v 直接合并到 transformedData 里面去
                _.extend(transformedData, v);
            } else if (config.type === 'j') {
                // 对应的 key 是 JSON，重新处理恢复一下
                //
                // 针对 type: rangecalendar 的特殊情况
                // name: {
                //   begin: 'beginTime',
                //   end: 'endTime'
                // },
                // value: {
                //   begin: ...,
                //   end: ...
                // }
                _.each(config.value, (name, valueKey) => {
                    const value = v[valueKey];
                    if (value != null) {
                        transformedData[name] = value;
                    }
                });
            }
        } else if (k !== '__s_key') {
            transformedData[k] = v;
        }
    });

    _.each(transformedData, (v, k) => {
        if (_.isDate(v)) {
            transformedData[k] = moment(v).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        }
    });

    return transformedData;
}

/**
 * asDialog 兼容弹窗，舍弃er的url方式，通过传入component渲染弹窗，兼容bce-ui
 *
 * @export asDialg
 * @param {Object} Klass san component
 * @param {Object} options 弹窗属性
 * @param {boolean} options.needFoot 是否需要foot @default true
 * @param {string} options.title 弹窗标题
 * @param {any} options.width 弹窗宽度 @default auto
 * @param {Object} data 父组件需要传的参数
 * @return {Object} san 对象
 */
export function asDialog(Klass, options, data) {
    const dataTypes = _.keys(data || {});
    const klassTemplate =
        dataTypes.length <= 0
            ? '<x-biz s-ref="biz" on-confirmDisabled="confirmDisabled" on-confirmDisabledTip="confirmDisabledTip" on-close="onClose" data="{{data}}" />'
            : '<x-biz s-ref="biz" on-confirmDisabled="confirmDisabled" on-confirmDisabledTip="confirmDisabledTip" on-close="onClose" ' +
              _.map(dataTypes, prop => `${prop}="{{data.${prop}}}"`) +
              ' />';

    // 插入所有ui组件，兼容以继承Component的形式定义的组件
    const allComps = [];
    _.map(SanUI, (Com, name) => {
        let comp = {};
        if (isSanCmpt(Com)) {
            comp[`ui-${_.kebabCase(name)}`] = Com;
            allComps.push(comp);
        }
    });

    const WrappedComponent = defineComponent({
        template: html`<template>
            <ui-dialog
                s-if="open"
                open="{{open}}"
                width="{{options.width}}"
                height="{{options.height}}"
                head="{{options.head}}"
                class="{{options.class}}"
                s-ref="dialog"
                foot="{{options.needFoot}}"
                on-close="onCancel"
            >
                <span slot="head">{{options.title}}</span>
                ${klassTemplate}
                <div slot="foot">
                    <ui-loading size="small" s-if="loading" />
                    <s-button on-click="onCancel" width="46">
                        {{options.cancelText || '取 消'}}
                    </s-button>
                    <s-tooltip
                        class="ml10 mb10"
                        trigger="{{options.confirmDisabled && options.confirmDisabledTip ? 'hover' : 'none'}}"
                    >
                        <div slot="content">{{options.confirmDisabledTip | raw | empty}}</div>
                        <s-button
                            width="46"
                            on-click="onConfirmDialog"
                            skin="primary"
                            disabled="{{options.confirmDisabled}}"
                        >
                            {{options.confirmText || '确 定'}}
                        </s-button>
                    </s-tooltip>
                </div>
            </ui-dialog>
        </template>`,
        components: _.extend(
            {
                'x-biz': Klass,
                's-button': Button,
                's-tooltip': Tooltip,
            },
            ...allComps,
        ),
        initData() {
            return {
                options: options,
                open: true,
                data: data,
            };
        },
        messages: {
            resize() {
                this.ref('dialog').__resize();
            },
        },
        onConfirmDialog() {
            const bizComponent = this.ref('biz');
            this.fire('confirm', bizComponent);
            this.data.set('options.confirmDisabled', true);
            bizComponent &&
                bizComponent.doSubmit &&
                bizComponent
                    .doSubmit()
                    .then(res => {
                        // const needAdjustCASpecInstanceGroupsIDs =
                        //     res?.result?.needAdjustCASpecInstanceGroupsIDs;
                        // if (needAdjustCASpecInstanceGroupsIDs) {
                        //     this.fire('success', needAdjustCASpecInstanceGroupsIDs);
                        // } else {
                        //     this.fire('success');
                        // }
                        this.fire('success');
                        this.dispose && this.dispose();
                    })
                    .catch(err => {
                        this.fire('fail', err);
                        this.data.set('options.confirmDisabled', false);
                    });
        },
        onCancel() {
            this.onClose();
        },
        onClose() {
            this.fire('close');
            this.data.set('open', false);
            this.dispose && this.dispose();
        },
        // 根据自组件触发的事件修改dialog确认按钮是否置灰
        confirmDisabled(val) {
            this.data.set('options.confirmDisabled', !!val);
        },
        confirmDisabledTip(val) {
            this.data.set('options.confirmDisabledTip', val);
        },
    });

    return WrappedComponent;
}

// 兼容弹窗，包装没有dialog包裹的组件
export function waitActionDialog(component, dialogOptions = {}, actionOptions = {}) {
    const myOptions = _.extend(
        {
            head: true,
            width: 'auto',
            title: 'Dialog Title',
            confirmText: '确定',
            cancelText: '取消',
            confirmDisabled: false,
        },
        dialogOptions,
    );

    const DialogComponent = asDialog(component, myOptions, actionOptions);
    const dialog = new DialogComponent();
    dialog.attach(document.body);
    return dialog;
}

export function asPromise(dialog) {
    return new Promise((resolve, reject) => {
        dialog.on('confirm', () => {
            resolve();
            dialog.dispose();
        });
        dialog.on('close', () => {
            reject();
            dialog.dispose();
        });
    });
}

export const getDeleteConfirmList = ({confirmTip, tableColumns}) => {
    const thead = [];
    const tbody = [];
    _.each(tableColumns, ({label, field}) => {
        thead.push(`<th>${label}</th>`);
        tbody.push(`<td><%= items[0].${field} %></td>`);
    });

    return html`
        <div class="cce-delete-warm-tip cce-tip-warn">温馨提示：危险操作，请确认是否继续操作！</div>
        <div class="cce-delete-text">${confirmTip}</div>
        <div>
            <table class="cce-delete-table">
                <thead>
                    ${thead.join('')}
                </thead>
                <tbody>
                    <tr>
                        ${tbody.join('')}
                    </tr>
                </tbody>
            </table>
        </div>
    `;
};

export const getDeleteToolbarOpt = conf =>
    _.assign(conf, {
        type: 'button',
        label: '删除',
        width: '550',
        actionType: 'ajax',
        customPayload: true,
        skin: 'cce-delete',
        confirmText: getDeleteConfirmList({
            confirmTip: conf.confirmTip,
            tableColumns: conf.tableColumns,
        }),
        $toastMessage: '删除成功',
        $onError(error) {
            SanUI.Toast.error('删除失败');
        },
    });

// 创建K8S标签
export const createK8sForm = (
    labelsPlaceholder = '',
    isInstance = true,
    required = false,
    hasNoLabel = false,
    isIngressNote = false,
) => {
    const LabelInfo = {
        controls: [
            {
                required,
                label: hasNoLabel ? '' : '标签（Labels）',
                name: 'tags',
                type: 'objectMultiTextForm',
                helpDocUrl:
                    'https://kubernetes.io/zh/docs/concepts/overview/working-with-objects/labels/#%e8%af%ad%e6%b3%95%e5%92%8c%e5%ad%97%e7%ac%a6%e9%9b%86',
                helpDocText: 'K8S标签说明',
                help: `温馨提示：K8S标签（Label）是管理和选择K8S对象的标识，
                    将自动绑定到${labelsPlaceholder}创建的节点；每个${
                    isInstance ? 'Label' : ''
                }包含键和值两部分。`,
                isIngressNote,
            },
        ],
    };
    return SanUI.createForm(LabelInfo);
};

// 创建K8S注解
export const createK8sNoteForm = (labelsPlaceholder = '', required = false, hasNolabel = false) => {
    const LabelInfo = {
        controls: [
            {
                required,
                label: hasNolabel ? '' : '注解（Annotations）',
                name: 'Annotation',
                type: 'objectMultiTextForm',
                helpDocUrl:
                    'https://kubernetes.io/zh/docs/concepts/overview/working-with-objects/annotations/#%E8%AF%AD%E6%B3%95%E5%92%8C%E5%AD%97%E7%AC%A6%E9%9B%86',
                helpDocText: 'K8S注解说明',
                help: '温馨提示：K8S注解（Annotation）是附加到K8S对象上的自定义信息，以便标记和查找；每个注解包含键和值两部分。',
                isIngressNote: true,
                addText: '添加注解',
            },
        ],
    };
    return SanUI.createForm(LabelInfo);
};
