@import './variable.less';

body {
    color: #151b26;
}

.cce-color-grey {
    color: #999;
}
.cce-color-red {
    color: #eb5252;
}
.ml8 {
    margin-left: 8px;
}
.tip-warning {
    display: flex;
    padding: 10px 10px;
    margin-bottom: 20px;
    background-color: #fcf7f1;
    line-height: 20px;
    color: #ff9326;
}

.cce-tip {
    margin: 0 0 15px 0;
    color: #ff9326;
    padding: 10px 10px;
    line-height: 20px;
    background-color: #fcf7f1;

    label {
        display: inline-block;
        float: left;
    }

    div {
        margin-left: 69px;
    }
}

.list-tip {
    min-height: 30px;
    line-height: 30px;
    background-color: #f5f5f5;
    padding-left: 20px;
}

.cce-dialog {
    .cce-note {
        color: #999;
    }
}

.cce-tip-warn {
    color: #ff9326 !important;
}

.cce-tip-grey {
    color: #84868c;
    a {
        cursor: pointer;
    }
}

.instance-name {
    max-width: 80%;
}

.status::before {
    margin-right: 4px;
}

.status.error::before {
    color: #f33e3e;
}

.status.normal::before {
    color: #30bf13;
}

.status.waiting {
    color: @primary-color;
}

.status.waiting:before {
    content: '\e6e9';
}

.status.warning {
    color: #ff9326;
}

.status.holding::before {
    color: #2468f2;
}

.ant-line {
    padding-bottom: 4px;
    border-bottom: 1px dashed #b8babf;
    cursor: pointer;
    &:hover {
        border-bottom: 1px dashed #5c5f66;
    }
}

.status.unavailable {
    color: #999;
}

@keyframes scale-animation {
    0% {
        opacity: 1;
        transform: scale(0.9);
    }
    25% {
        opacity: 0.85;
        transform: scale(1.1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.4);
    }
    75% {
        opacity: 0.85;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(0.9);
    }
}

.status.rolling {
    left: 0;
    padding-left: 20px;

    &::before,
    &::after {
        animation: none;
        content: '';
        margin-left: 0;
    }
    &::before {
        width: 12px;
        height: 12px;
        top: 2px;
        background-color: #d4e5ff;
        border: 1px solid #d4e5ff;
        border-radius: 50%;
        box-sizing: border-box;
        animation-duration: 2s;
        animation-iteration-count: infinite;
        animation-name: scale-animation;
        animation-timing-function: linear;
    }
    &::after {
        width: 8px;
        height: 8px;
        margin-left: 0;
        background: #528eff;
        border-radius: 50%;
        left: 2px;
        top: 4px;
    }
}

.animation-loading {
    &::before {
        display: none;
    }
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(0, 0, 0, 0); /* 外圈颜色 */
    border-top: 2px solid #2468f2; /* 顶部条颜色 */
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.bui-form-x {
    h4 {
        font-size: 16px;
        height: auto;
        line-height: inherit;
        color: #333;
        padding: 0;
        display: inline-block;
        border-bottom: none;
        margin: 0 0 20px 20px;

        &:before {
            content: '';
            display: inline-block;
            vertical-align: middle;
            width: 4px;
            height: 16px;
            background: @primary-color;
            margin-right: 10px;
        }

        span {
            vertical-align: middle;
        }
    }

    .bui-form-item {
        margin-top: 20px;

        &:first-of-type {
            margin-top: 0;
        }

        &.bui-form-item-hidden {
            display: none;
        }

        .bui-form-item-label {
            color: #666;
            width: 100px;
            height: 30px;
            line-height: 30px;
            float: left;
        }

        &.bui-form-item-align-top .bui-form-item-label {
            vertical-align: top;
            padding-top: 3px;
        }

        .bui-form-item-content {
            display: inline-block;
            vertical-align: middle;

            .bui-form-item-tip {
                color: #999;
                margin-top: 3px;
            }

            .bui-form-item-invalid-label {
                color: #ea2e2e;
            }
        }
    }
}

.inline-edit-container {
    position: relative;
    .inline-edit-main {
        background-color: #fff;
        position: absolute;
        left: 0;
        top: -0.5em;
        display: none;
        border: 1px solid #e1e1e1;
        padding: 6px;
        z-index: 100;
        width: 515px;
        .bui-validity {
            display: block;
        }
    }
}

.cce-wrapper {
    .sidebar {
        .bui-tip {
            position: relative;
            top: -1px;
        }
    }

    .link-wrap {
        position: absolute;
        top: 18px;
        right: 40px;

        a {
            color: #999;
            margin-left: 5px;

            &:hover {
                color: #118dee;
            }

            .iconfont {
                padding-right: 5px;
            }
        }
    }

    .operation-wrap {
        .bui-textbox {
            line-height: normal;
            input {
                line-height: normal;
            }
        }

        .clear-button {
            &:after {
                content: none;
            }
        }

        .bui-select {
            display: inline-block;
        }
    }

    .list-content {
        margin-top: 20px;

        .list-tip {
            margin-top: 10px;
            height: 30px;
            line-height: 30px;
            background-color: #f5f5f5;
            padding-left: 20px;
        }
    }

    /**
     * 表格区
     */
    .bui-table {
        .bui-table-hcell-text {
            overflow: hidden;
        }
        .hcell-filter .bui-table-hcell-text {
            overflow: visible;
        }

        .instance-name {
            max-width: 90%;
        }
    }

    .bui-table.bui-table-filter {
        .bui-table-filter-panel {
            padding-right: 0;

            .bui-table-filter-select {
                right: 0;
                left: inherit;
            }
        }
    }

    .bui-table-body {
        .link-copy {
            position: relative;
            width: 18px;
            height: 17px;
            vertical-align: middle;
            display: inline !important;
        }
        .icon-copy {
            cursor: pointer;
            display: none;
            font-size: 12px;
            color: #6583cc;
            &:hover {
                color: #2c4a93;
            }
        }
        .bui-table-row-hover {
            .icon-copy {
                display: inline;
            }
        }
        .bui-table-cell-text {
            position: relative;
        }
    }

    .table-no-data-html .opration .cmd-button {
        font-size: 14px;
    }

    .table-body-info {
        border: 1px solid #e5e5e5;
        border-width: 0 1px;
        text-align: center;
        .loading {
            display: inline-block;
            width: 130px;
            margin: 40px auto;
        }
        .loading-error {
            padding: 40px;
            text-align: center;
        }
    }

    .quota-tip {
        position: absolute;
        margin-left: 10px;
        font-size: 12px;
        color: #999;
        vertical-align: middle;
    }
}

.cce-list {
    .content-wrap {
        margin-left: 160px;
    }

    .clear-button {
        background: none;
    }
}

.cce-dialog {
    font-size: 12px;
    .reminder {
        width: 420px;
        padding: 10px 10px;
        margin-bottom: 20px;
        background-color: #fcf7f1;
        line-height: 20px;
        color: #ff9326;

        label {
            display: inline-block;
            float: left;
        }
        div {
            margin-left: 60px;
        }
    }
    .form-key {
        float: left;
        white-space: nowrap;
    }
    .cce-note {
        color: #999;
        margin-top: 2px;
    }
}

.skin-cce-delete {
    .cce-delete-warm-tip {
        font-size: 12px;
        margin-bottom: 20px;
        height: 30px;
        line-height: 30px;
        background-color: #fcf7f1;
        padding: 0 20px;
    }

    .cce-tip-warn {
        background: #fcf7f1;
        padding: 5px 10px;
        font-size: 12px;
    }

    .bui-dialog-icon-confirm {
        display: none;
    }

    .cce-delete-text {
        margin-top: 10px;
        font-size: 13px;
    }

    .cce-delete-table {
        width: 100%;
        margin-top: 10px;

        thead th {
            background-color: #f5f5f5;
            color: #777;
            border-bottom-width: 0;
        }

        thead tr,
        tbody td {
            height: 40px;
        }

        td {
            height: 30px;
            color: #777;
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: #eceff8;
        }

        td:first-child {
            border-left-width: 1px;
            border-left-style: solid;
            border-left-color: #eceff8;
        }

        td:last-child {
            border-right-width: 1px;
            border-right-style: solid;
            border-right-color: #eceff8;
        }

        tr,
        th {
            border-width: 2px;
            border-style: solid;
            border-color: #f6f7fb;
        }

        th:first-child,
        th:last-child {
            border-width: 1px;
            border-style: solid;
            border-color: #eee;
        }

        td,
        th {
            padding: 5px 10px;
            font-size: 12px;
        }
    }
}

.text-overflow-ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: block;
}

.cce-select-option-ellipsis {
    .s-option {
        & > div {
            width: 100%;
            .ellipsis {
                width: 100%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                display: block;
            }
        }
    }
}

.text-overflow-wrap {
    word-wrap: break-word;
}

.cce-list {
    .bui-table {
        .bui-table-cell-text {
            word-wrap: break-word;
            word-break: break-all;
        }
        .s-table-cell-text {
            word-wrap: break-word;
            word-break: break-all;
        }
    }
}

.block-create {
    .page-breadcrumb-wrapper {
        margin-left: -20px;
        margin-right: -20px;
        .back-wrapper {
            a {
                cursor: pointer;
            }
        }
    }
    .create-content-wrapper {
        background-color: #fff;
        min-height: 400px;
    }
    h4 {
        margin-left: 0;
        margin-bottom: 8px;
    }
    .bui-form-x {
        margin-left: 20px;
    }
    .bui-select-x {
        width: 222px;
    }
    .part-border {
        &:before {
            position: absolute;
            right: -20px;
            left: -20px;
            content: '';
            border-bottom: 1px solid #e3e5eb;
            display: inline-block;
            zoom: 1;
        }
        .bui-form-item-content {
            margin-top: 20px;
        }
    }
    .submit-container {
        margin-bottom: 50px;
        .bui-button-x {
            margin-right: 15px;
        }
    }
    .break-all {
        word-break: break-all;
    }
}

.cce-error {
    color: #f33e3e;
}

.cce-add-tag.bui-form {
    .form-item-group {
        margin-bottom: 20px;

        .bui-form-item {
            display: inline-block;
            zoom: 1;
            margin: 0;
        }

        .bui-form-item-label {
            width: auto !important;
        }
    }

    a {
        // color: #999;
        margin-left: 20px;
    }

    .iconfont {
        vertical-align: middle;
        color: #999;
    }

    .icon-link {
        color: @primary-color;
    }

    .bui-form-item-invalid-label {
        display: block;
        margin-top: 10px;
    }

    .error-msg {
        display: block;
        margin-bottom: 10px;
        color: #f33e3e;
        font-size: 12px;
    }

    .cce-taints-remove-btn {
        position: relative;
        top: 5px;
    }
}

.bui-table {
    .bui-table-cell-text {
        overflow: hidden;
    }
    .s-table-cell-text {
        word-break: break-all;
    }
    .group-node-instance-name-pop {
        span,
        a {
            float: left;
            max-width: 100%;
        }
    }

    .instance-name {
        max-width: 90%;
        display: inline-block;
        zoom: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 20px;
    }

    .bui-table-empty,
    .bui-table-error {
        color: #84868c;
    }

    .bui-table-hcell:first-child .bui-table-hcell-text,
    .bui-table-cell:first-child .bui-table-cell-text {
        padding-left: 12px;
    }

    .bui-table-cell-sel {
        width: 40px;
    }
}

.cmd-button {
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    color: @primary-color;
    padding: 0;
    margin: 0 4px 0 0;
    font-size: 12px;
    font-family: inherit;

    &.state-disabled {
        cursor: not-allowed;
        color: #999;
    }
}

.cce-dialog-pagination-wrapper {
    margin-top: 10px;
    float: right;
}

.s-table {
    .s-table-hcell .s-table-hcell-text .s-table-hcell-text-content {
        display: inline-flex !important;
    }
    .s-table-container-scroll {
        .s-table-header {
            margin: 0 !important;
        }
    }
    .s-table-hcell-sort {
        .s-table-hsort {
            &:before {
                content: none !important;
            }
        }
    }
    .s-table-header {
        margin-bottom: 0 !important;
        scrollbar-width: none; /* FireFox only 不显示滚动条，但是该元素依然可以滚动 */
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .s-table-container .s-table-body table > tbody > tr > td {
        border: none !important;
    }

    .s-table-empty .empty-desc,
    .s-table-error {
        color: #84868c;
    }

    .s-table-loading-content {
        background-image: none;
    }
}

.tag-edit-panel {
    .bui-form {
        .bui-form-item {
            margin: 0 0 20px 0 !important;

            .bui-suggestion {
                vertical-align: middle !important;
            }

            .iconfont {
                margin-left: 10px !important;
            }
        }
    }
}

// SUI

.s-tabs {
    .s-tabnav {
        .s-tabnav-scroll {
            // background-color: #fff;
        }
        .s-tabnav-nav {
            .s-tabnav-nav-selected {
                color: #2468f2 !important;
            }
        }
    }

    &.s-tabs-line {
        &.s-tabs-vertical.s-tabs-left > .s-tabnav {
            .s-tabnav-scroll {
                border-right: 1px solid #ebebeb !important;
            }
            .s-tabnav-nav {
                padding-top: 15px;
                border-right: 0;
            }

            .s-tabnav-nav-item {
                text-align: left;
                height: 40px;
                line-height: 40px;
                width: 160px;
                padding: 0 16px;
                margin-bottom: 4px;
                &.s-tabnav-nav-selected {
                    background: #e6f0ff;
                }
            }
        }

        &.s-tabs-horizontal > .s-tabnav {
            .s-tabnav-nav .s-tabnav-nav-selected:after {
                width: 100%;
            }

            .s-tabnav-scroll {
                height: auto;
                border-bottom: 0;
            }
        }
    }

    &.s-tabs-horizontal.s-tabs-top > .s-tabnav {
        margin-bottom: 16px;
    }
}

.s-tabs-skin-accordion.s-tabs-left {
    border: none !important;
}

.app-create-page-footer,
.s-create-page-footer {
    width: 100%;
}

.bui-select-container {
    .bui-select-fixed {
        .bui-textbox {
            display: block;
            border-width: 0 0 1px 0;

            .bui-textbox-input-area {
                display: block;

                &:before {
                    font-family: iconfont;
                    position: absolute;
                    content: '\e7ef';
                    font-size: 20px;
                    top: 5px;
                    color: #666;
                }
            }
        }

        input[type='text'] {
            width: 100% !important;
            padding-left: 20px;
        }
    }
}
.app-detail-labels-flow {
    display: inline-block;
    .service-label {
        line-height: 18px;
        background-color: #e2e5ec;
        border-radius: 3px;
        padding: 2px 3px 2px 3px;
        margin-left: 2px;
        display: inline-block;
    }
}

.fix-sui-line {
    .s-link {
        vertical-align: unset;
    }
}

.primary-text {
    color: @primary-color;
    letter-spacing: 2px;
}

.app-sidebar {
    .link-item {
        padding-left: 0;
        margin-left: -18px;
        .link-icon {
            margin-right: 3px;
            left: 0px;
        }
    }
}

.link-icon {
    left: 0px !important;
}

a {
    color: @primary-color;
}
a:active,
a:hover {
    color: #528eff;
}

.app-legend {
    margin-bottom: 16px;
}

.app-view {
    .app-tab-page,
    .s-tab-page {
        border-radius: 6px;
        padding: 0;

        .bui-tab-content {
            padding: 0;
        }
    }

    .app-legend,
    .s-legend {
        > label {
            line-height: 24px;
            color: #151a26;
            font-weight: 500;
            &.app-legend-highlight::before,
            &.s-legend-highlight::before {
                content: none;
            }
        }
    }

    .bui-searchbox .bui-select {
        padding-right: 30px;
    }

    .bui-drawer {
        border-radius: 0;

        .bui-drawer-header {
            height: 56px;
            line-height: 56px;
            padding: 0 24px;
        }

        .bui-drawer-close {
            top: 19px;
            right: 24px;
        }

        .bui-drawer-body {
            padding: 24px;
        }
    }

    .app-list-page {
        background: #f7f7f9;

        .app-page-title {
            padding: 12px 16px;
            background-color: #fff;

            > h2 {
                border-bottom: 0;
                height: 24px;
                line-height: 24px;
                padding: 0;
                text-indent: 0;
                font-weight: 500;
                font-size: 16px;

                > span {
                    font-weight: normal;
                }
            }

            .s-alert,
            .namespace-list-tip {
                margin: 12px 0 0;
            }
        }

        .operation-wrap {
            height: auto;
            margin-bottom: 16px;
        }
    }

    .bui-textbox-multi-line {
        height: auto;

        .bui-textbox-input-area {
            // padding-bottom: 20px;
        }

        textarea {
            display: block;
        }

        .bui-textbox-limit {
            line-height: 20px;
        }
    }
}
.bui-textbox-multi-line {
    height: auto !important;

    // .bui-textbox-input-area {
    //     padding-bottom: 20px;
    // }

    textarea {
        display: block;
        resize: auto;
    }

    // .bui-textbox-limit {
    //     line-height: 20px;
    // }
}
.app-list-page {
    .table-full-wrap {
        background-color: #fff;
        border-radius: 6px;
        margin: 16px;
        padding: 24px;
    }
}

.bui-searchbox + .bui-button {
    margin-left: 4px;
}

.app-create-page-title,
.s-create-page-title {
    padding: 0 16px;
    border: none;
    background: #fff;

    .page-title-nav {
        position: unset;
    }
}

.app-detail-page,
.s-detail-page,
.app-create-page,
.s-create-page {
    background: #f7f7f9;
}

.app-detail-page-title,
.s-detail-page-title {
    background: #fff;
    border: none;
    margin: 0;
    padding: 0 16px;
}

.app-detail-page-content,
.s-detail-page-content {
    background: #fff;
    margin: 16px;
    border-radius: 6px;
    .detail-hpa-list {
        .s-list-content {
            background: #fff;
        }
        .foot-pager {
            display: none;
        }
        .table-full-wrap {
            margin: 0 !important;
            padding: 0 !important;
        }
        .operation-wrap {
            display: none;
        }
        .s-table-empty > div:first-child {
            display: none;
        }
        .title-component {
            color: #151b26 !important;
            .component-name {
                background: #f7f7f9;
                border: 1px solid #d4d6d9;
                border-radius: 10px;
                padding: 0 8px;
                font-size: 12px;
                display: inline-block;
                height: 20px;
                line-height: 20px;
                color: #151b26;
                img {
                    margin-right: 4px;
                }
            }
        }
    }
}
.s-empty-no-icon {
    .s-empty-desc p {
        line-height: 20px !important;
    }
    .s-button-skin-stringfy {
        font-size: 12px !important;
    }
    .s-empty-action-icon {
        display: none;
    }
}

.bui-legend {
    border: none !important;
    border-radius: 6px;
    padding: 24px !important;
    box-sizing: content-box;
    .highlight {
        &::before {
            content: none !important;
        }
    }
}

.highlight {
    &::before {
        content: none !important;
    }
}

.app-create-page,
.s-create-page {
    .app-create-page-content,
    .s-create-page-content {
        padding-bottom: 96px;
    }

    .app-legend,
    .s-legend {
        border: none !important;
        border-radius: 6px;
        padding: 24px !important;
        background: #fff;

        > .app-legend-highlight,
        > .s-legend-highlight {
            margin-bottom: 16px;
        }
    }

    .app-create-page-footer,
    .s-create-page-footer {
        width: 100%;
        padding: 0;
        height: 80px;

        .page-footer-wrapper {
            width: 980px;
        }
    }

    .s-button + .s-button {
        margin-left: 12px;
    }

    .s-button-large {
        min-width: 54px;
    }
}

.app-create-page-content,
.s-create-page-content {
    > .app-legend,
    > .s-legend {
        width: 980px;
        margin-top: 16px;
    }
}

.s-dialog-wrapper {
    overflow: unset !important;
}

div.s-dialog {
    > .s-dialog-wrapper .s-dialog-header > h3 {
        font-weight: 500;
    }
}

.bui-tab {
    .bui-tab-header {
        border-bottom: 1px solid #e8e9eb;
    }

    .bui-tab-nav-bar {
        height: 40px;
    }

    .bui-tab-nav-item {
        margin: 0;
        padding: 0;
        height: 40px;
        line-height: 40px;
        margin-left: 32px;

        &:first-child {
            margin-left: 0;
        }
    }

    .bui-tab-nav-item-active {
        color: #2468f2;
        border-color: #2468f2;
    }

    &.skin-accordion-tab {
        border: 0;
        height: 100%;

        .bui-tab-header {
            border-bottom: 0;
            border-right: 1px solid #ebebeb;

            > .bui-tab-nav-wrapper > .bui-tab-nav-bar {
                background-color: #fff !important;
                padding-top: 15px;

                > .bui-tab-nav-item {
                    width: 160px;
                    padding: 0 16px;
                    margin-bottom: 4px;
                    font-size: 14px;
                    color: #151b26;

                    &:hover {
                        color: #2468f2;
                    }

                    &.bui-tab-nav-item-active {
                        background-color: #e6f0ff;
                        color: #2468f2;

                        &::before {
                            content: none;
                        }
                    }
                }
            }
        }
    }
}

.bui-loading {
    width: 36px !important;
    height: 36px !important;
}

.bui-tip {
    border-radius: 50%;
    border-color: #84868c;
    color: #84868c;
    line-height: 14px;

    &:hover {
        color: #2468f2;
        border-color: #2468f2;
    }

    &:active {
        color: #144bcc;
        border-color: #144bcc;
    }
}

.bui-form .bui-form-item {
    align-items: stretch !important;
}

.bui-form-item-label {
    color: #5c5f66;
}

.panel-loading::before {
    background-size: 36px;
}

.s-input.input-addon {
    overflow: visible;
}

.s-pagination.s-pagination-normal > .s-pagination-wrapper .s-pagination-total {
    margin-right: 8px;
}

.bui-instanteditor-layer {
    padding: 12px;
    box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
    border: 0;
    border-radius: 4px;

    > .bui-instanteditor-btns {
        margin-top: 12px;

        .bui-button + .bui-button {
            margin-left: 4px;
        }
    }
}

.bui-table-cell-text {
    color: #151b26;
}

.s-app-sidebar-item a > .link-item {
    padding: 0;

    .link-icon {
        order: 1;
        margin-left: 4px;
    }
}

.app-detail-cell .detail-cell {
    line-height: 20px;
}

.s-popover .s-popover-body .s-popover-arrow {
    border: 0 none;
}

.bui-radioselect-disabled.bui-radioselect .bui-radio-block.bui-radio-selected {
    background: white;
}
.cce-cluster-add-dialog {
    .bui-radio-selected {
        background-color: #e6f0ff !important;
    }
    .bcc-help-tip {
        color: #84868c;
        margin-top: 4px;
    }
    .bui-dialog-body-panel {
        max-height: unset !important;
        padding-bottom: 0 !important;
    }
    .bui-dialog-foot-panel {
        padding-top: 0;
    }
    .bui-form .bui-form-item .required-label:before {
        left: -7px;
    }
    .bui-form-item-label.require-label.required-label {
        margin-left: 7px;
    }
    .s-form-item-label > label {
        padding-left: 0;
    }
}

.snapshot-create-cds-layer {
    .bui-searchbox .bui-select .bui-select-text {
        margin-right: 20px;
    }
}

.bui-dialog-head {
    .bui-dialog-title {
        padding-bottom: 0 !important;
        background: white;
        position: relative;
        z-index: 1000;
        border-radius: 6px;
    }
    .bui-dialog-close-icon {
        z-index: 1001;
        top: 20px;
        right: 20px;
    }
}

.skin-stringfy.bui-button-disabled .bui-button-label {
    color: #aaa;
}

.bui-suggestion {
    margin-right: 5px;
}
.bui-suggestion-layer-x {
    border-radius: 6px;

    .bui-suggestion-item {
        height: 32px;

        &.bui-suggestion-item-selected,
        &:hover,
        span.highlight {
            color: #2468f2;
        }

        &:hover {
            background-color: #e6f0ff !important;
        }
    }
}

.s-radio-button-group,
.s-radio-button {
    .s-biz-wrapper .s-radio-button {
        .s-radio-text::before {
            display: none;
        }
        &:first-child .s-radio-text {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            border-left-width: 1px;
        }
        &:last-child .s-radio-text {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            border-right-width: 1px;
        }
    }
}

.s-radio-button-group .s-wrapper > * {
    &:not(:last-child) label.s-radio-button .s-radio-text {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0;
    }

    &:not(:first-child) label.s-radio-button .s-radio-text {
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        border-left-width: 0;
    }
}

.s-checkbox-group .s-checkbox-input:checked,
.s-checkbox .s-checkbox-input:checked {
    background-color: @primary-color;
}

.s-textarea .s-textarea-limit {
    right: 16px;
}

.s-checkbox-group .s-radio-text,
.s-checkbox .s-radio-text {
    vertical-align: super;
}

// 使用 body 加一点优先级
body {
    .s-radio-button.s-radio-checked .s-radio-text {
        background-color: #e6f0ff;
    }

    // sui 边框颜色
    .s-input,
    .s-radio-button .s-radio-text,
    .bui-multi-line-textarea,
    .bui-textbox,
    .bui-select {
        border-color: @border-color;
    }

    .s-tip:hover {
        background: #fff;
        border-color: #528eff;

        .s-icon path {
            fill: #528eff;
        }
    }

    .s-form-item-invalid,
    .s-form-item-with-extra,
    .s-form-item-with-help {
        margin-bottom: 24px;
    }

    .s-form-item-error,
    .s-form-item-extra,
    .s-form-item-help {
        padding-bottom: 0;
    }

    .s-icon-button-able:hover {
        fill: #2468f2 !important;
    }

    .bui-textbox:hover,
    .bui-textbox-focus,
    .bui-select:hover,
    .bui-select-active {
        border-color: @primary-color;
    }

    .bui-select::after,
    .bui-select:hover::after {
        color: #84868c;
    }

    .bui-select-item:hover {
        background-color: #e6f0ff;
    }

    .bui-suggestion-arrow .bui-textbox {
        &::after,
        &:focus::after,
        &:focus-within::after {
            border-color: #84868c;
        }
    }
}

// 下拉框多滚动条fix
.s-select {
    .s-selectdropdown > div:first-child {
        overflow: hidden;
        max-height: none;
    }
}

.s-checkbox-disabled.s-checkbox-checked .s-checkbox-input,
.s-checkbox-group-disabled.s-checkbox-checked .s-checkbox-input {
    background-color: #7dadff !important;
    border-color: #7dadff !important;
}
.s-checkbox-checked .s-checkbox-input {
    &::before {
        color: #fff !important;
        top: 3px;
        left: 3px;
    }
}
.s-checkbox-disabled.s-checkbox-checked .s-checkbox-input:before,
.s-checkbox-group-disabled.s-checkbox-checked .s-checkbox-input:before {
    color: #fff !important;
}

.s-table .s-table-cell-sel .s-table-sell-text .s-checkbox {
    display: inline-block !important;
}

.error-msg {
    color: #f33e3e;
}

.s-dropdown .s-popup-content-box {
    box-shadow: none !important;
    background-color: transparent !important;
}

// 时间选择器样式异常覆盖
.s-daterangepicker {
    .s-popup-content-box {
        padding-top: 4px !important;
        .s-daterangepicker-popup {
            width: auto !important;
            .s-daterangepicker-popup-begin {
                margin-right: 0;
            }
            .s-daterangepicker-popup-panel {
                width: auto !important;
                .s-picker-header {
                    height: 40px;
                }
                .s-time {
                    padding: 0;
                    margin: 0;
                    width: auto;
                    .s-time-wrap {
                        margin: 0;
                    }
                    .s-time-wrap-list-item {
                        height: 28px;
                    }
                }
            }
        }
    }
}
.s-picker-shortcut {
    height: 40px !important;
}
.s-datepicker-popup table tbody tr th,
.s-daterangepicker-popup table tbody tr th {
    border: none;
    padding: 0;
}

// 选择节点sdk
.osType-select,
.osType-select-layer {
    .icon {
        display: inline-block !important;
        vertical-align: middle;
        width: 20px;
        height: 18px;
    }
}

.icon-os-type-img {
    background-size: 15px !important;
}

.os-type-windows-server,
.os-type-windows {
    background: url('../img/system/Windows.png') no-repeat;
}

.os-type-centos {
    background: url('../img/system/CentOS.png') no-repeat;
}

.os-type-ubuntu {
    background: url('../img/system/Ubuntu.png') no-repeat;
}

.os-type-debian {
    background: url('../img/system/Debian.png') no-repeat;
}

.os-type-opensuse {
    background: url('../img/system/openSUSE.png') no-repeat;
}

.os-type-freebsd {
    background: url('../img/system/FreeBSD.png') no-repeat;
}

.os-type-fedora {
    background: url('../img/system/Fedora.png') no-repeat;
}

.os-type-baidulinux {
    background: url('../img/system/baidulinux.png') no-repeat;
}

.os-type-rocky {
    background: url('../img/system/rockylinux.png') no-repeat;
}

.os-type-almalinux {
    background: url('../img/system/almalinux.png') no-repeat;
}

.com-p-l-0 {
    padding-left: 0px;
}

.s-form-item.mb0 {
    margin-bottom: 0;
}

.s-popover .s-popover-body .s-popover-content {
    min-width: auto !important;
}

.s-table.s-table-fixed .s-table-cell-left:after,
.s-table.s-table-fixed .s-table-hcell-left:after {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}
.s-table.s-table-scroll-position-left .s-table-cell-left:after,
.s-table.s-table-scroll-position-left .s-table-hcell-left:after,
.s-table.s-table-scroll-position-middle .s-table-cell-left:after,
.s-table.s-table-scroll-position-middle .s-table-hcell-left:after,
.s-table.s-table-scroll-position-right .s-table-cell-left:after,
.s-table.s-table-scroll-position-right .s-table-hcell-left:after {
    -webkit-box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
}
.s-table.s-table-scroll-position-left .s-table-cell-left:after,
.s-table.s-table-scroll-position-left .s-table-hcell-left:after {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.instance-config-panel-perf {
    .wrapper-source {
        margin-top: 5px !important;
    }
    .s-form-item-control {
        display: block;
    }
    .bui-textbox {
        height: 30px;
        line-height: 30px;
    }
    .s-form-item-control-wrapper {
        max-width: 997px;
    }
    // 老版本通过样式隐藏了产品筛选，这里放开，通过变量控制
    .filter-wrapper + .s-form-item {
        display: unset !important;
    }
    .support-buy-soldout {
        .s-table-cell:first-child::after {
            display: none !important;
        }
    }
    .filter-form {
        .wrapper-source-item {
            .s-select {
                width: 110px !important;
            }
        }
    }
    .image-selector {
        .s-form-item-label {
            text-align: left;
        }
    }
    .bindding-type-content {
        position: relative;
        right: 10px;
    }
    .bidding-block {
        .color-red {
            display: block;
            margin-top: 8px;
        }
    }
    .config-install-drive-tip {
        margin-left: 64px !important;
    }
}
.ssh-create-diaolg {
    .bui-textbox {
        height: auto;
        line-height: auto;
    }
}
.mt2 {
    margin-top: 2px;
}
.mb2 {
    margin-bottom: 2px;
}
.mt4 {
    margin-top: 4px;
}
.mb4 {
    margin-bottom: 4px;
}
.mt6 {
    margin-top: 6px;
}
.mb6 {
    margin-bottom: 6px;
}
.mt8 {
    margin-top: 8px;
}
.mb8 {
    margin-bottom: 8px;
}
.mt10 {
    margin-top: 10px;
}
.mb10 {
    margin-bottom: 10px;
}
.mt12 {
    margin-top: 12px;
}
.mb12 {
    margin-bottom: 12px;
}
.mt14 {
    margin-top: 14px;
}
.mb14 {
    margin-bottom: 14px;
}
.mt16 {
    margin-top: 16px;
}
.mb16 {
    margin-bottom: 16px;
}
.mt24 {
    margin-top: 24px;
}
.mb24 {
    margin-bottom: 24px;
}
