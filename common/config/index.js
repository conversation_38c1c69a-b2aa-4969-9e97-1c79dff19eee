/**
 * 公用配置
 *
 * @file index.js
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';

export {default as Rules} from './rules';

// 格式化时间
export const formatTime = time => {
    if (!time) {
        return '-';
    }
    return m(time).format('YYYY-MM-DD HH:mm:ss');
};

// 计算时间差
const formatTimeDiff = (startTime, endTime) => {
    if (!startTime) {
        return '-';
    }

    let unixStart = m(startTime);
    let unixEnd = m(endTime || Date.now());

    return unixEnd.diff(unixStart, 'minutes') || '-';
};
// TODO:完善文件分类
const cellRenderer = (item, key, col, rowIndex) => {
    const value = item[key];
    switch (key) {
        case 'creationDateTime':
            return formatTime(value) || '--';
        case 'lastModifiedTime':
            return formatTime(value) || '--';
        case 'runDatetime':
            return formatTimeDiff(item.creationDateTime, item.runDatetime) || '--';
        case 'taskHostStatus': {
            const statusItem = _.find(taskHostStatus, status => status.value === item.status);
            return `<span class="run-status ${statusItem.level}">${statusItem.label}</span>`;
        }
        default:
            return value || '--';
    }
};

// BOS接口错误提示
export const BosErrorPrefix = '请求BOS资源：';

// 静默请求参数
export const silentRequester = {'x-silent': true};

// 创建传输任务 toolbar
export const ToolBars = [
    {name: 'create', text: '创建传输任务', disable: false, tip: ''},
    {name: 'start', text: '启动', disable: false, tip: ''},
    {name: 'stop', text: '暂停', disable: false, tip: ''},
    {name: 'delete', text: '删除', disable: false, tip: ''},
    {name: 'edit', text: '编辑标签', disable: false, tip: ''},
];

// bui pager reset config
export const PagerBui = {
    size: 10,
    page: 1,
    count: 0,
    datasource: [
        {text: '10', value: 10},
        {text: '20', value: 20},
        {text: '50', value: 50},
        {text: '100', value: 100},
    ],
};

// sui pager reset config
export const PagerSui = {
    pageSize: 10,
    page: 1,
    pageSizes: [10, 20, 50, 100],
};

// 分隔符映射
export const delimiterMap = {
    blank: ' ',
    tab: '\t',
    longstring: '|',
    comma: ',',
};

// 引用符映射
/* eslint-disable */
export const quoteMap = {
    unused: '',
    singleQuote: "'",
    doubleQuote: '"',
};
/* eslint-enable */

// 搜索框下拉的选项数组
export const SearchTypes = {
    collects: [
        {text: 'Hostname名称', value: 'hostNamePattern', placeholder: '请输入Hostname名称进行搜索'},
        {text: '收集器ID', value: 'idPattern', placeholder: '请输入收集器ID进行搜索'},
        {text: 'IP地址', value: 'ipPattern', placeholder: '请输入IP地址进行搜索'},
    ],
};

export const FormConfig = {
    inputWidth: 220,
};

export const defaultColor = '#108cee';
