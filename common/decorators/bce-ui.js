/**
 * @file all decorators
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, isSanCmpt} from '@baiducloud/runtime';
import * as sui from '@baidu/sui';

const {invokeComp, asComponent} = decorators;

if (location.search.indexOf('locale=en') > -1) {
    sui.i18n.lang = 'en';
} else {
    sui.i18n.lang = 'zh-cn';
}

const suiComs = [];
_.each(sui, (Com, name) => {
    if (isSanCmpt(Com)) {
        name = '@s-' + _.kebabCase(name);
        asComponent(name)(Com);
        suiComs.push(name);
        let items = Object.keys(Com);
        if (items.length > 0) {
            _.each(items, item => {
                if (isSanCmpt(Com[item])) {
                    let comName = name + '-' + _.kebabCase(item);
                    asComponent(comName)(Com[item]);
                    suiComs.push(comName);
                }
            });
        }
    }
});

decorators.invokeSUI = invokeComp.apply(this, suiComs);
