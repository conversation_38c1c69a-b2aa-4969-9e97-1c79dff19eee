/**
 * @file 装饰器 - 计算装饰器
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators} from '@baiducloud/runtime';

const computed = {
    getTableSelectedLength() {
        return this.data.get('table.selectedIndex').length;
    },
    getTableSelects() {
        return _.filter(this.data.get('table.datasource'), (item, index) =>
            _.some(this.data.get('table.selectedIndex'), i => _.parseInt(i) === index));
    }
};

decorators.invokeComputed = (...args) => target => {
    _.each(args, key => {
        target.computed = {
            [key]: computed[key],
            ...target.computed
        };
    });
};
