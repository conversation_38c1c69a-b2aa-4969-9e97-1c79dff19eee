/**
 * @file 装饰器 - 方法装饰器
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators} from '@baiducloud/runtime';
import m from 'moment';


// import * as util from '../util';
// 将时间转化为utc
const formatTimeUtc = (time, pattern = 'YYYY-MM-DDTHH:mm:ss') =>
    m(time).utc().format(pattern) + 'Z';


const methods = {

    /**
     * 有效期切换
     * @param {string} key key
     * @param {string} value value
     */
    handleRadioChange(key, value) {
        this.data.set(key, value);
    },

    /**
     * 关闭对应类型弹出框
     * @param {string} key key
     */
    handleCloseDialog(key) {
        this.data.set(key, false);
    },

    /**
     * type
     * @param {Object} formData formData
     * @return {Object}
     */
    getLogTime(formData) {
        if (formData.selectTime) {
            const selectTime = formData.selectTime;
            const timeType = selectTime <= 15 ? 0 : (selectTime === 60 ? 1 : 2);
            return {
                startDateTime: formatTimeUtc(timeType === 0
                    ? m().subtract(selectTime, 'minutes')
                    : timeType === 1
                        ? m().subtract(selectTime / 60, 'hours')
                        : m().subtract(selectTime / (60 * 24), 'days')),
                endDateTime: formatTimeUtc(new Date())
            };
        }
        return {
            startDateTime: formData.startDateTime,
            endDateTime: formData.endDateTime
        };
    },

    // ********* 表格相关 *********

    /**
     * 初始化配置
     */
    initConfig() {
        this.showToolBtnShow();
        this.watch('table.selectedIndex', this.showToolBtnShow);
        this.watch('searchbox.keyword', value => {
            !value && this.data.set('isSearching', false);
        });
    },

    /**
     * 设置按钮显示
     */
    showToolBtnShow() {
        const disable = this.data.get('table.selectedIndex').length === 0;
        const toolBtn = _.map(this.data.get('toolBtn'), item => {
            return item.name !== 'create' ? _.assign({}, item, {disable}) : item;
        });
        this.data.set('toolBtn', toolBtn);
    },

    /**
     * 搜索
     */
    handleSearch() {
        this.data.set('pager.size', 10);
        this.data.set('pager.page', 1);
        this.data.set('table.selectedIndex', []);

        if (this.data.get('searchbox.keyword')) {
            this.getTableList(this.data.get('searchbox.keyword'));
            this.data.set('isSearching', true);
            return;
        }
        this.getTableList();
        this.data.set('isSearching', false);
    },

    // input事件触发
    searchAndSetKeyword() {
        this.nextTick(this.handleSearch.bind(this));
    },

    /**
     * 分页
     * @param {Object} e e
     */
    handlePageChange(e) {
        this.data.set('table.selectedIndex', []);
        this.data.set('pager.size', e.size);
        this.data.set('pager.page', e.pageNo);
        this.data.get('isSearching') ? this.getTableList(this.data.get('searchbox.keyword'))
            : this.getTableList();
    },

    /**
     * 对话框关闭
     * @param {string} type 类型
     */
    handleDialogClose(type) {
        this.data.set(`showDialog.${type}`, false);
    },

    /**
     * 处理sort归类
     * @param {string} orderBy orderBy
     * @param {string} order order
     */
    handleSort({orderBy, order}) {
        this.getTableList('', {orderBy, order});
    },

    /**
     * 表格reload
     */
    handleReload() {
        this.data.get('isSearching') ? this.getTableList(this.data.get('searchbox.keyword'))
            : this.getTableList();
    },

    /**
     * 下载
     * @param {Object} data data
     * @param {string} filename filename
     * @param {string} type type
     */
    download(data, filename, type) {
        const file = new Blob(['\ufeff' + data], {type});
        if (window.navigator.msSaveOrOpenBlob) {
            // IE10+
            window.navigator.msSaveOrOpenBlob(file, filename);
        }
        else {
            // Others
            const a = document.createElement('a');
            const url = URL.createObjectURL(file);
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            setTimeout(() => {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            });
        }
    }
};

decorators.invokeMethods = (...args) => target => {
    _.each(args, key => {
        if (!target.prototype[key]) {
            target.prototype[key] = methods[key];
        }
    });
};
