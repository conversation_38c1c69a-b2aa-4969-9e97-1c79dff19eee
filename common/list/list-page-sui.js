/**
 * @file 公共列表
 * <AUTHOR>
 */

import _ from 'lodash';
import {AppListPage} from '@baidu/sui-biz';
import {Button, Table, Pagination, Input, Select, Alert, Popover, Tooltip} from '@baidu/sui';
import {
    OutlinedPlus,
    OutlinedSearch,
    OutlinedInfoCircle,
    OutlinedUp,
    OutlinedDown,
    OutlinedLeft,
    OutlinedRefresh,
} from '@baidu/sui-icon';
import {ListPage} from '@baiducloud/runtime';

import {getTemplate} from './template-sui';

export default class ListCommonPage extends ListPage {
    static template = getTemplate();
    static components = {
        's-page': AppListPage,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        's-icon-plus': OutlinedPlus,
        's-tooltip': Tooltip,
        's-input': Input,
        's-select': Select,
        's-option': Select.Option,
        's-icon-left': OutlinedLeft,
        's-icon-search': OutlinedSearch,
        's-icon-info': OutlinedInfoCircle,
        's-alert': Alert,
        's-up': OutlinedUp,
        's-down': OutlinedDown,
        's-popover': Popover,
        's-icon-refresh': OutlinedRefresh,
    };
    static computed = {
        searchCriteria() {
            const pager = this.data.get('pager');
            const filterPayload = this.data.get('filterPayload');
            const extraPayload = this.data.get('extraPayload');
            const searchCriteria = {
                pageNo: pager.pageNo,
                pageSize: pager.pageSize,
            };

            const {value, keywordType} = this.data.get('searchbox');
            if (value && keywordType) {
                _.extend(searchCriteria, {keywordType, keyword: value});
            }
            _.extend(searchCriteria, extraPayload, filterPayload);
            return searchCriteria;
        },
    };

    initData() {
        return {
            klass: [],
            title: '',
            create: '新建',
            emptyText: '暂无数据',
            searchbox: {
                value: '',
                placeholder: '',
                keywordType: '',
                datasource: [],
            },
            table: {
                columns: [],
                loading: true,
                datasource: [],
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                },
            },
            pager: {
                totalCount: 0,
                maxItem: 7,
                pageNo: 1,
                pageSize: 10,
                pageSizes: [10, 20, 50, 100],
            },
            extraPayload: {},
            filterPayload: {},
        };
    }
    inited() {
        this.$childs = [];
        this.$tableInited = false;
        this.updateSearchPlaceholder();

        const {keywordType, keyword} = this.data.get('route.query') || {};
        if (keywordType && keyword) {
            this.data.set('searchbox.value', keyword);
            this.data.set('searchbox.keywordType', keywordType);
        }
    }
    attached() {
        this.refreshTable();
    }
    onSearch() {
        this.disposeInternalChilds();
        this.data.set('table.selection.selectedIndex', []);
        const payload = this.getSearchCriteria();
        if (this.$tableInited) {
            payload.pageNo = 1;
        }
        return this.loadPage(payload);
    }
    onRefresh() {
        this.disposeInternalChilds();
        this.refreshTable();
    }
    onPagerSizeChange(e) {
        const {page} = e.value;
        if (page === 1) {
            this.onPagerChange(e);
        }
    }
    onPagerChange(e) {
        const {page, pageSize} = e.value;
        const payload = this.getSearchCriteria();
        payload.pageNo = page;
        payload.pageSize = pageSize;
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage(payload);
    }
    onKeywordTypeChange(e) {
        this.data.set('searchbox.keywordType', e.value);
        this.updateSearchPlaceholder();

        this.fire('keyword-type-change', e);
    }
    updateSearchPlaceholder() {
        const {keywordType, datasource} = this.data.get('searchbox');
        const item = _.find(datasource, d => d.value === keywordType);
        if (item) {
            this.data.set(
                'searchbox.placeholder',
                item.placeholder ? item.placeholder : `请输入${item.text}进行搜索`,
            );
        }
    }
    getSearchCriteria() {
        return this.data.get('searchCriteria');
    }
    refreshTable() {
        this.disposeInternalChilds();
        this.data.set('table.selection.selectedIndex', []);
        const payload = this.getSearchCriteria();
        return this.loadPage(payload);
    }
    transformTable(tableData) {
        return tableData;
    }
    loadPage(payload) {
        if (!this.lifeCycle.attached) {
            return;
        }

        const {onRequest} = this.data.get();
        const requestPayload =
            typeof onRequest === 'function' ? onRequest(payload) || payload : payload;
        this.data.set('table.loading', true);
        return this.doRequest(requestPayload)
            .then((res = {}) => {
                const {page} = res;
                return {
                    searchCriteria: requestPayload,
                    searchResponse: page || {},
                };
            })
            .catch(error => {
                throw {
                    searchCriteria: requestPayload,
                    error,
                };
            })
            .then(({searchCriteria, searchResponse}) => {
                this.$tableInited = true;
                const page = searchResponse;
                const onResponse = this.data.get('onResponse');
                const responsePayload =
                    typeof onResponse === 'function' ? onResponse(page) || page : page;
                const {result, pageNo, totalCount} = responsePayload;

                const tableData = this.transformTable(result) || [];
                const resultTotalCount = totalCount > 0 ? totalCount : tableData.length;
                this.data.set('table.loading', false);
                this.data.set('table.error', null);
                this.data.set('table.datasource', tableData);
                this.data.set('pager.pageNo', pageNo || searchCriteria.pageNo);
                this.data.set('pager.totalCount', resultTotalCount);
            })
            .catch(({error}) => {
                this.$tableInited = true;

                const onError = this.data.get('onError');
                if (typeof onError === 'function') {
                    onError(error);
                }
                this.data.set('table.loading', false);
                this.data.set('table.error', error);
            });
    }
    disposeInternalChilds() {
        _.each(this.$childs, component => {
            const dialog = component.ref('dialog');
            try {
                if (dialog) {
                    dialog.dispose();
                }
                component.dispose();
            } catch (ex) {
                /* eslint-disable */
            }
            /* eslint-enable */
        });
        this.$childs = [];
    }
    disposed() {
        this.disposeInternalChilds();
    }
    resetSearchCriteria(payload) {
        const filterPayload = this.data.get('filterPayload');
        const newFilterPayload = _.extend({}, filterPayload, payload);
        this.data.set('filterPayload', newFilterPayload);
    }
    onFilter(e) {
        const payload = {
            [e.field.name]: e.filter.value,
        };
        this.resetSearchCriteria(payload);
        this.onSearch();
    }
    onSort(e) {
        const {orderBy, order} = e.value;
        this.data.set('extraPayload.orderBy', orderBy);
        this.data.set('extraPayload.order', order);
        this.onSearch();
    }
}
