/**
 * @file 公共列表
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {ListPage, redirect, templates} from '@baiducloud/runtime';
import {
    bizXPager,
    bizToolbar,
    bizRightToolbar,
    Chart,
    asDrawer,
    bizFilter,
} from '@baiducloud/bce-ui/san';
import {
    Alert,
    Tabs,
    Drawer,
    Steps,
    Form,
    Checkbox,
    Select,
    Radio,
    InputNumber,
    Tooltip,
    Table,
    Search,
    Button,
    Pagination,
    Loading,
    Dropdown,
    Menu,
} from '@baidu/sui';
import {
    OutlinedRefresh,
    OutlinedDownload,
    OutlinedPlay,
    OutlinedPause,
    OutlinedSetting,
} from '@baidu/sui-icon';
import {TableColumnToggle} from '@baidu/sui-biz';
import {ajaxAction} from '../biz/ajax-action';
import {createToolbar, valueTransform, matchAll} from '../biz/helper';
import {getTemplate} from './template';
import CreateJson from '../../components/cluster/create-v2/create-json';

const kShieldType = {};
class BizComponent extends Component {}

export default function listPage(schema, MainComponent) {
    class CommonList extends ListPage {
        static template = getTemplate(schema);

        static components = {
            'x-main': MainComponent,
            'ui-toolbar': bizToolbar,
            'ui-right-toolbar': bizRightToolbar,
            'ui-pager': bizXPager,
            'app-list-page': templates.AppListPage,
            'ui-chart': Chart,
            'ui-as-drawer': asDrawer(BizComponent),
            'ui-biz-filter': bizFilter,
            's-alert': Alert,
            's-tabs': Tabs,
            's-tabpane': Tabs.TabPane,
            's-drawer': Drawer,
            's-steps': Steps,
            's-step': Steps.Step,
            's-form': Form,
            's-form-item': Form.Item,
            's-checkbox': Checkbox,
            's-select': Select,
            's-option': Select.Option,
            's-radio': Radio,
            's-radio-group': Radio.RadioGroup,
            's-input-number': InputNumber,
            's-tooltip': Tooltip,
            's-table': Table,
            's-search': Search,
            's-button': Button,
            's-icon-refresh': OutlinedRefresh,
            's-icon-download': OutlinedDownload,
            's-pagination': Pagination,
            's-loading': Loading,
            's-icon-play': OutlinedPlay,
            's-icon-pause': OutlinedPause,
            's-table-column-toggle': TableColumnToggle,
            's-dropdown': Dropdown,
            's-menu': Menu,
            's-menu-item': Menu.Item,
            's-icon-setting': OutlinedSetting,
            'create-json-editor': CreateJson,
        };

        static computed = {
            withFilter() {
                const filter = this.data.get('filter');
                return (
                    filter &&
                    filter.$position !== 'tb' &&
                    filter.controls &&
                    filter.controls.length > 0
                );
            },
            withToolbarFilter() {
                const filter = this.data.get('filter');
                return (
                    filter &&
                    filter.$position === 'tb' &&
                    filter.controls &&
                    filter.controls.length > 0
                );
            },
            mainClass() {
                const klass = this.data.get('klass');
                return ['cce-common-list', klass];
            },
            searchCriteria() {
                const pager = this.data.get('pager');
                const extraPayload = this.data.get('$extraPayload');
                const filterPayload = this.data.get('$filterPayload');
                const searchCriteria = {
                    pageNo: pager.page,
                    pageSize: pager.size,
                };

                const keywordName = this.data.get('filter.$searchbox.name');
                if (keywordName) {
                    const keyword = filterPayload.keyword;
                    _.extend(searchCriteria, extraPayload, _.omit(filterPayload, 'keyword'), {
                        [keywordName]: keyword,
                    });
                } else {
                    _.extend(searchCriteria, extraPayload, filterPayload);
                }
                return valueTransform(searchCriteria);
            },
            tableColumns() {
                const schema = this.data.get('table.schema');
                if (schema[0].$when && schema[0].$then) {
                    // 动态的情况，遍历所有的可能？
                    for (let i = 0; i < schema.length; i++) {
                        const {$when, $then} = schema[i];
                        if (matchAll(this, $when)) {
                            return $then;
                        }
                    }
                    // TODO(leeight) 这种情况怎么处理?
                    return [];
                }
                return schema;
            },
        };

        initData() {
            /* eslint-disable */
            const {
                $pageClass,
                $pageNav,
                $breadcrumbs,
                $navs,
                $helps,
                $persistState,
                $autoToPrevPage,
                $emptyText,
                $withTip,
                $withPager,
                $withPagerSize,
                $withSearchbox,
                $withSidebar,
                $withBatchDelete,
                $withDownload,
                $withNotice,
                $withRefresh,
                remark,
                title,
                toolbar,
                body,
                tip,
            } = schema;
            const {
                bulkActions,
                filter,
                columns,
                $extraPayload,
                $select,
                $cellRenderer,
                $pageSize,
                $commands,
            } = body;
            /* eslint-enable */
            const {$onRequest, $onResponse, $onError, $onDownload} = body;
            const cellRenderer = $cellRenderer
                ? (...args) => $cellRenderer.apply(null, [...args, this.getSearchCriteria()])
                : null;

            const $filterPayload = {};

            const keywordType = _.get(filter, '$searchbox.keywordType');
            if (keywordType) {
                $filterPayload.keywordType = keywordType;
            }
            const keyword = _.get(filter, '$searchbox.value');
            if (keyword) {
                $filterPayload.keyword = keyword;
            }

            const withPersistState = $persistState && typeof history.pushState === 'function';

            this.$onRequest = $onRequest;
            this.$onResponse = $onResponse;
            this.$onError = $onError;
            this.$onDownload = $onDownload;

            const {p, e, f} = withPersistState ? this.__restorePageState() : {};

            if (cellRenderer && columns) {
                columns.forEach(element => {
                    !element.render &&
                        (element.render = (item, key, col, rowIndex, colIndex, data) => {
                            return cellRenderer(item, key, col, rowIndex, colIndex, data);
                        });
                });
            }

            const data = {
                title,
                tip,
                klass: $pageClass,
                navs: $navs,
                helps: $helps,
                remark,
                toolbar: createToolbar(toolbar),
                bulkActions,
                filter,
                pageNav: $pageNav,
                breadcrumbs: $breadcrumbs,
                withSidebar: !!$withSidebar,
                withPagerSize: !!$withPagerSize,
                withNotice: !!$withNotice,
                autoToPrevPage: !!$autoToPrevPage,
                withSearchbox: $withSearchbox !== false,
                withPager: $withPager !== false,
                withTip: !!$withTip,
                withBatchDelete: !!$withBatchDelete, // 是否支持批量删除
                withDownload: !!$withDownload, // 是否支持下载
                withRefresh: !!$withRefresh, // 是否支持刷新
                withPersistState,
                commands: $commands,

                loading: false, // 数据是否在加载中
                error: null, // 错误的情况
                table: {
                    loading: false, // 数据是否在加载中
                    disabledSelectAll: false, // 禁用全选的功能
                    error: null, // 加载失败的情况??
                    emptyText: $emptyText,
                    cellRenderer,
                    select: $select,
                    schema: columns, // 当前表格的列
                    datasource: [], // 当前可见的数据
                    selectedIndex: [],
                    selectedItems: [], // 当前选中的行
                },

                $extraPayload: e || $extraPayload,
                $filterPayload: f || $filterPayload,
                pager: p || {
                    size: $pageSize || 10,
                    page: 1,
                    count: 0,
                    datasource: [
                        {text: '10', value: 10},
                        {text: '20', value: 20},
                        {text: '50', value: 50},
                        {text: '100', value: 100},
                    ],
                },
            };

            return data;
        }

        __watcherTableColumns(tableColumns) {
            const datasource = [];
            const value = [];
            for (let i = 0; i < tableColumns.length; i++) {
                const col = tableColumns[i];
                if (!col.xui__hidden) {
                    value.push(col.name);
                }
                if (col.toggled) {
                    datasource.push({text: col.label, value: col.name});
                }
            }
            this.data.set('tct', {datasource, value});
            const cellRenderer = this.data.get('table.cellRenderer');
            if (cellRenderer && tableColumns) {
                tableColumns.forEach(element => {
                    !element.render &&
                        (element.render = (item, key, col, rowIndex, colIndex, data) => {
                            return cellRenderer(item, key, col, rowIndex, colIndex, data);
                        });
                });
            }
        }

        inited() {
            this.$childs = [];
            this.$tableInited = false;
            this.watch('tableColumns', tableColumns => this.__watcherTableColumns(tableColumns));
        }

        attached() {
            this.__watcherTableColumns(this.data.get('tableColumns'));
            this.refreshTable();
        }

        toggleTableColumns() {
            const columnNames = this.data.get('tct.value');
            const tableColumns = this.data.get('tableColumns');
            _.each(tableColumns, (col, i) => {
                // 如果不存在，说明需要隐藏
                const xuiHidden = _.indexOf(columnNames, col.name) === -1;
                this.data.set(`tableColumns[${i}].xui__hidden`, xuiHidden);
            });
        }

        __restorePageState() {
            // silent
            const query = this.data.get('route.query');
            if (query && query.__state) {
                try {
                    // {p: ..., e: ..., f: ...}
                    return JSON.parse(query.__state);
                } catch (ex) {
                    /* eslint-disable */
                }
                /* eslint-enable */
            }

            return {};
        }

        __savePageState() {
            // // 保存当前页面的搜索条件
            // const disableCache = true;
            // const {query, path} = this.$route(disableCache);
            // const pageState = this.data.get('pageState');
            // query.__state = JSON.stringify(pageState);
            // const newUrl = '#' + URL.withQuery(path, query).toString();
            // history.pushState(null, '', newUrl);
        }

        __doRequest(api, payload, options) {
            let promise = Promise.resolve();
            if (_.isString(api)) {
                if (this.$http[api]) {
                    promise = this.$http[api](payload, options);
                } else if (this[api]) {
                    promise = this[api](payload).then(response => response);
                }
            }

            return promise
                .then(page => {
                    return {
                        searchCriteria: payload,
                        searchResponse: page,
                    };
                })
                .catch(error => {
                    throw {
                        searchCriteria: payload,
                        error,
                    };
                });
        }

        getSearchCriteria() {
            return this.data.get('searchCriteria');
        }

        doSearch() {
            this.data.set('table.selectedIndex', []);
            this.disposeInternalChilds();
            const payload = this.getSearchCriteria();
            if (this.$tableInited) {
                // 如果已经完成了数据的初始化，那么后续切换过滤条件的时候，才需要把页码恢复到第一页
                // 否则在初始化的时候，经常会发现因为 x-filter#submit 触发执行了 onXFilter 的逻辑，然后执行了
                // doSearch，导致把页面从 pageState 里面的值重置到了第一页
                payload.pageNo = 1;
            }
            return this.loadPage(payload);
        }

        refreshTable(silent, disposeChilds = true) {
            this.data.set('table.selectedIndex', []);
            disposeChilds && this.disposeInternalChilds();
            const payload = this.getSearchCriteria();
            return this.loadPage(payload, silent);
        }

        transformTable(tableData) {
            return tableData;
        }

        loadPage(payload, silent) {
            if (!this.lifeCycle.attached) {
                return;
            }

            const requestPayload =
                typeof this.$onRequest === 'function'
                    ? this.$onRequest(payload) || payload
                    : payload;

            // this.__searchCriteria 保存最后一次的查询条件
            this.__searchCriteria = requestPayload;
            !silent && this.data.set('table.loading', true);
            return this.__doRequest(schema.body.api, requestPayload, schema.body.apiOptions)
                .then(({searchCriteria, searchResponse}) => {
                    this.__searchCriteria = kShieldType;
                    this.$tableInited = true;
                    const page = searchResponse;
                    const responsePayload =
                        typeof this.$onResponse === 'function'
                            ? this.$onResponse(page) || page
                            : page;
                    const {result, pageNo, totalCount} = responsePayload;

                    // 尝试请求前一页数据
                    const autoToPrevPage = this.data.get('autoToPrevPage');
                    if (autoToPrevPage && pageNo > 1 && result.length <= 0) {
                        return this.loadPage({...payload, pageNo: pageNo - 1});
                    }
                    const tableData = this.transformTable(result);
                    // 有些接口返回的 page.totalCount 是 0，但是 page.result 居然有内容
                    const resultTotalCount = totalCount > 0 ? totalCount : tableData.length;
                    this.data.set('table.loading', false);
                    this.data.set('table.error', null);
                    this.data.set('table.datasource', tableData);
                    this.data.set('table.totalCount', resultTotalCount);
                    this.data.set('pager.page', pageNo || searchCriteria.pageNo);
                    this.data.set('pager.count', resultTotalCount);

                    const withPersistState = this.data.get('withPersistState');
                    if (withPersistState) {
                        this.__savePageState();
                    }

                    return Promise.resolve();
                })
                .catch(({searchCriteria, error}) => {
                    if (
                        this.__searchCriteria === kShieldType ||
                        this.__searchCriteria !== searchCriteria
                    ) {
                        return;
                    }
                    this.__searchCriteria = kShieldType;
                    this.$tableInited = true;
                    if (typeof this.$onError === 'function') {
                        this.$onError(error);
                    }
                    this.data.set('table.loading', false);
                    this.data.set('table.error', error);
                });
        }

        disposeInternalChilds() {
            _.each(this.$childs, component => {
                const dialog = component.ref('dialog');
                try {
                    if (dialog) {
                        dialog.dispose();
                    }
                    component.dispose();
                } catch (ex) {
                    /* eslint-disable */
                }
                /* eslint-enable */
            });
            this.$childs = [];
        }

        onToolbarEvent(item) {
            const config = item;
            const select = this.data.get('table.select');
            const payload =
                select === 'single'
                    ? this.__selectedItems
                        ? this.__selectedItems[0]
                        : null
                    : {items: this.__selectedItems};
            if (config.actionType === 'custom') {
                if (typeof config.action === 'function') {
                    config.action.apply(this, [payload]);
                }
                return;
            }
            this.dispatchAction(config, payload);
        }

        onTableRowSelected({selectedIndex, selectedItems}) {
            this.__selectedItems = selectedItems;
            this.__selectedIndex = selectedIndex;

            const toolbar = this.data.get('toolbar');
            _.each(toolbar, (item, i) => {
                if (typeof item.checker === 'function') {
                    const newState = item.checker(selectedItems); // 根据当前选中的内容，重新计算状态
                    if (newState && _.isObject(newState)) {
                        // 比如返回的可以是
                        // {disabled: true, label: 'eee'}
                        // {datasource: [], value: 'eee'}
                        _.each(newState, (value, key) =>
                            this.data.set(`toolbar[${i}].${key}`, value),
                        );
                    }
                }
            });

            // 更新 bulkActions 的状态
            const bulkActions = this.data.get('bulkActions');
            _.each(bulkActions, (item, i) => {
                // FIXME(leeight) 应该起个什么名字呢？
                if (typeof item.reactive === 'function') {
                    const newState = item.reactive(selectedItems); // 根据当前选中的内容，重新计算状态
                    if (newState && _.isObject(newState)) {
                        // 比如返回的可以是
                        // {disabled: true, label: 'eee'}
                        // {datasource: [], value: 'eee'}
                        _.each(newState, (value, key) =>
                            this.data.set(`bulkActions[${i}].${key}`, value),
                        );
                    }
                }
            });
        }

        onTableCommand({type, payload, rowIndex}) {
            const commands = this.data.get('commands');
            const config = commands && commands[type];
            if (!config) {
                return;
            }
            this.data.set('table.rowIndex', rowIndex - 1);
            this.dispatchAction(config, payload);
        }

        onRowToggle(e) {
            this.fire('toggle');
        }

        dispatchAction(config, payload = {}) {
            switch (config.actionType) {
                case 'custom':
                    config.action && config.action.apply(this, [payload]);
                    break;
                case 'new-window':
                    window.open(_.template(config.link)(payload));
                    break;
                case 'link':
                    redirect(_.template(config.link)(payload));
                    break;
                case 'ajax':
                    ajaxAction.call(this, config, payload);
                    break;
            }
        }

        onPagerChange(e) {
            const payload = this.getSearchCriteria();
            payload.pageNo = e.pageNo;
            payload.pageSize = e.size;
            this.data.set('table.selectedIndex', []);
            this.data.set('pager.page', e.pageNo);
            this.loadPage(payload);
        }

        onSort({orderBy, order}) {
            this.data.set('$extraPayload.orderBy', orderBy);
            this.data.set('$extraPayload.order', order);
            this.doSearch();
        }

        resetSearchCriteria(filterPayload) {
            const $filterPayload = this.data.get('$filterPayload');
            const newFilterPayload = _.extend({}, $filterPayload, filterPayload);
            this.data.set('$filterPayload', newFilterPayload);
        }

        onXFilter(formData) {
            this.onFilter(formData);
        }

        onFilter(filterPayload) {
            this.resetSearchCriteria(filterPayload);
            this.doSearch();
        }

        disposed() {
            this.disposeInternalChilds();
        }
    }

    return CommonList;
}
