.sub-title {
    font-size: 12px;
    color: #84868c;
    line-height: 24px;
    font-weight: 400;
    margin-left: 4px;
}
.cce-common-list {
    .title-remark {
        font-size: 12px;
        color: #999;
        vertical-align: bottom;
    }

    .title-remark-highlight {
        color: #f38900;
    }

    .title-help {
        font-size: 12px;
        float: right;

        a,
        .iconfont {
            vertical-align: middle;
            color: #999;
        }

        .iconfont {
            font-size: 14px;
        }
    }
    .app-list-page {
        padding: 0px;

        .app-list-content > div > .s-alert {
            margin: 0 24px;
        }
    }
    .title-help-custom {
        font-size: 12px;
        float: right;

        a,
        .s-icon,
        span {
            vertical-align: middle;
        }
    }

    .table-error-action {
        margin-left: 10px;
    }

    .list-page-tb-left-filter {
        display: inline-block;
        zoom: 1;
        vertical-align: middle;
    }

    .list-page-tb-right {
        float: right;
    }

    .operation-wrap {
        width: 100%;
    }

    .foot-pager {
        & > .flex-between {
            justify-content: space-between;
        }
    }
}

.locale-en {
    .cce-common-list {
        .title-remark {
            max-width: 350px;
            display: inline-block;
            zoom: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.cce-common-list-page {
    .title-remark {
        font-size: 12px;
        font-weight: 400;
        color: #84868c;
        vertical-align: bottom;
    }

    .title-list-tip {
        margin-top: 10px;

        .s-alert {
            margin: 0 24px;
        }
    }
    .title-help {
        font-size: 12px;
        float: right;
        a {
            color: #151b26;
            vertical-align: middle;

            &:hover {
                color: #2468f2;
            }
        }
    }
    .buttons-wrap {
        .s-button-skin-primary {
            .s-icon-button-able:hover {
                fill: #fff !important;
            }
        }

        .s-button + .s-button {
            margin-left: 4px;
        }
    }
    .buttons-quick-wrap {
        .input-addon {
            .s-input-addon-before {
                padding: 0;
                border-radius: 6px 0 0 6px;
                background-color: none;
            }
            .s-input {
                border: none;
            }
            .s-option {
                padding-right: 34px;
                padding-left: 6px;
            }
        }
    }
    .app-list-page {
        padding: 0px;

        .foot-pager {
            margin-bottom: 10px;
        }
    }
    .operation-wrapper {
        .more-opt {
            .s-input-area {
                input {
                    &::placeholder {
                        color: #2468f2 !important;
                    }
                }
            }
            .s-select-caret {
                svg {
                    fill: rgb(36, 104, 242);
                }
            }
        }
    }
    .empty-wrap {
        padding: 50px 0;

        .empty-desc {
            margin-left: 30px;
            color: #333;
            text-align: left;

            p {
                font-size: 14px;
                margin-bottom: 10px;
            }
        }

        .table-nodata {
            width: 140px;
            height: 101px;
            background: url(../../img/table-nodata.png) 0 0 no-repeat;
            background-size: contain;
        }
    }

    .app-list-content {
        h2 {
            .s-button {
                margin-right: 10px;
            }
        }
    }

    .s-radio-text {
        display: block;
    }
}

.cce-worker-gpunumremaining {
    color: #84868c;
}
