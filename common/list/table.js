/**
 * @file common/list/table.js
 * <AUTHOR>
 */

import {DataTypes, defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Table} from '@baidu/sui';
import {Table as BuiTable, FrozenColumnTable} from '@baiducloud/bce-ui/san';

import {utcToTime} from '../../utils/util';
import {BEC_REGION} from '../../pages/cluster/create-v2/components/cluster-config';

const template = slot => html`<template>
    <ui-table
        s-if="tableType !== 'SUI'"
        schema="{{columns}}"
        select="{{table.select}}"
        select-text="{{table.selectText}}"
        loading="{{table.loading}}"
        error="{{table.error}}"
        datasource="{{table.datasource}}"
        cell-builder="{{table.cellRenderer}}"
        disabled-select-all="{{table.disabledSelectAll}}"
        selected-index="{=table.selectedIndex=}"
        on-command="onTableCommand($event)"
        on-filter="onFilter($event)"
        on-sort="onSort($event)"
        on-selected-change="onTableRowSelected($event)"
        on-row-enter="onRowToggle($event, true)"
        on-row-leave="onRowToggle($event, false)"
    >
        <div slot="error">
            <template s-if="table.error">
                {{table.error.tip || (table.error.message && (table.error.message.global ||
                table.error.message))}}
                <a href="{{table.error.href}}" s-if="table.error.href" class="table-error-href">
                    {{table.error.entrance}}
                </a>
                <template s-elif="table.error.action">
                    <a
                        href="javascript:void(0)"
                        class="table-error-action"
                        on-click="onErrorClick(table.error)"
                    >
                        {{table.error.action}}
                    </a>
                </template>
                <a s-else class="table-error-action" href="javascript:void(0)" on-click="onRefresh">
                    重新加载
                </a>
            </template>
            <template s-else>
                啊呀，出错了？
                <a href="javascript:void(0)" on-click="onRefresh">重新加载</a>
            </template>
        </div>
        <!--bca-disable-next-line-->
        <div slot="empty">{{table.emptyText ? table.emptyText : emptyText | raw}}</div>

        ${slot}
    </ui-table>
    <s-table
        s-else
        id="bui-common-table"
        columns="{{columns}}"
        selection="{{selection}}"
        loading="{{table.loading}}"
        error="{{table.error}}"
        datasource="{{table.datasource}}"
        on-filter="onFilter($event)"
        on-sort="onSort($event)"
        on-selected-change="onTableRowSelected($event)"
        on-row-enter="onRowToggle($event, true)"
        on-row-leave="onRowToggle($event, false)"
    >
        <div slot="error">
            <template s-if="table.error">
                {{table.error.tip || (table.error.message && (table.error.message.global ||
                table.error.message))}}
                <a href="{{table.error.href}}" s-if="table.error.href" class="table-error-href">
                    {{table.error.entrance}}
                </a>
                <template s-elif="table.error.action">
                    <a
                        href="javascript:void(0)"
                        class="table-error-action"
                        on-click="onErrorClick(table.error)"
                    >
                        {{table.error.action}}
                    </a>
                </template>
                <a s-else class="table-error-action" href="javascript:void(0)" on-click="onRefresh">
                    重新加载
                </a>
            </template>
            <template s-else>
                啊呀，出错了？
                <a href="javascript:void(0)" on-click="onRefresh">重新加载</a>
            </template>
        </div>
        <!--bca-disable-next-line-->
        <div slot="empty">{{table.emptyText ? table.emptyText : emptyText | raw}}</div>

        ${slot}
    </s-table>
</template>`;

export default function asTable(slot = '', components = {}, filters = {}, tableType = 'BUI') {
    const WrappedComponent = defineComponent({
        template: template(slot),

        components: {
            's-table': Table,
            'ui-table': BuiTable,
            'ui-f-table': FrozenColumnTable,
            ...components,
        },

        filters: {
            timeFormat(value) {
                return value ? utcToTime(value) : '-';
            },
            ...filters,
        },

        dataTypes: {
            columns: DataTypes.array,
            table: DataTypes.objectOf({
                error: DataTypes.any,
                emptyText: DataTypes.string,
                loading: DataTypes.bool,
                disabledSelectAll: DataTypes.bool,
                datasource: DataTypes.array,
                selectedIndex: DataTypes.array,
                cellRenderer: DataTypes.func,
                select: DataTypes.string,
            }),
        },

        inited() {
            this.data.set('isNewBec', window.$context.getCurrentRegionId() === BEC_REGION);
            this.data.set('selection.mode', this.data.get('table.select'));
            this.data.set('selection.disabledSelectAll', this.data.get('table.disabledSelectAll'));
            this.data.set('selection.selectedIndex', this.data.get('table.selectedIndex'));
            this.watch('table', table => {
                this.data.set('selection.mode', table.select);
                this.data.set('selection.disabledSelectAll', table.disabledSelectAll);
                this.data.set('selection.selectedIndex', table.selectedIndex);
            });
        },
        attached() {
            if (this.data.get('tableType') === 'SUI') {
                const table = document.querySelector('#bui-common-table');
                this.handleClick = this.handleClick.bind(this); // 绑定this上下文
                table.addEventListener('click', this.handleClick);
            }
        },
        disposed() {
            if (this.data.get('tableType') === 'SUI') {
                const table = document.querySelector('#bui-common-table');
                // 移除点击事件监听器
                table.removeEventListener('click', this.handleClick);
            }
        },

        initData() {
            return {
                emptyText: '暂无数据',
                errorText: '啊呀，出错了？',
                selection: {
                    mode: '',
                    disabledSelectAll: false,
                    selectedIndex: [],
                },
                tableType,
            };
        },

        onTableCommand(event) {
            this.fire('command', event);
        },

        onFilter(event) {
            this.fire('filter', event);
        },

        onSort(event) {
            if (this.data.get('tableType') === 'SUI') {
                this.fire('sort', event.value);
            } else {
                this.fire('sort', event);
            }
        },

        onTableRowSelected(event) {
            if (this.data.get('tableType') === 'SUI') {
                this.fire('selected-change', event.value);
            } else {
                this.fire('selected-change', event);
            }
        },

        onRefresh() {
            this.fire('refresh');
        },

        onRowToggle(event, value) {
            if (this.data.get('tableType') === 'SUI') {
                this.fire('row-toggle', {e: {rowIndex: event.value}, value});
            } else {
                this.fire('row-toggle', {e: event, value});
            }
        },

        onErrorClick(error) {
            this.onTableCommand({type: error.code});
        },
        handleClick(e) {
            // 处理点击事件的逻辑
            if (e.target.matches('a[data-command]')) {
                const type = e.target.getAttribute('data-command');
                if (!type) {
                    return;
                }

                let tr = e.target.closest('tr');
                if (!tr) {
                    return;
                }

                let rowIndex = tr.getAttribute('data-row-index');
                if (rowIndex === null) {
                    return;
                }
                const payload = this.data.get(`table.datasource[${rowIndex}]`);

                if (payload) {
                    // bui里面的备注
                    // zhangzhe 2018-03-14 为了兼容之前这里的rowIndex从1开始，所以依旧加了1
                    rowIndex++;
                    this.fire('command', {type, payload, rowIndex, domEvent: e});
                }
            }
        },
    });

    return WrappedComponent;
}
