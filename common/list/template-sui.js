/**
 * @file common/list/template-sui.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';

export const getTableTemplate = slot => html`<s-table
    columns="{{table.columns}}"
    loading="{{table.loading || tableLoading}}"
    error="{{table.error}}"
    s-ref="common-table"
    max-height="{{table.maxHeight}}"
    datasource="{{table.datasource}}"
    selection="{{table.selection}}"
    on-selected-change="onSelectedChange"
    on-filter="onFilter"
    on-sort="onSort"
>
    <div slot="error">
        <template s-if="table.error">
            {{table.error.tip || (table.error.message && (table.error.message.global ||
            table.error.message))}}
            <a href="{{table.error.href}}" s-if="table.error.href" class="table-error-href">
                {{table.error.entrance}}
            </a>
            <template s-elif="table.error.action">
                <a
                    href="javascript:void(0)"
                    class="table-error-action"
                    on-click="onErrorClick(table.error)"
                >
                    {{table.error.action}}
                </a>
            </template>
            <a s-else class="table-error-action" href="javascript:void(0)" on-click="onRefresh">
                重新加载
            </a>
        </template>
        <template s-else>
            啊呀，出错了？
            <a href="javascript:void(0)" on-click="onRefresh">重新加载</a>
        </template>
    </div>
    <div slot="empty">{{(table.emptyText || emptyText) | raw | empty}}</div>
    ${slot}
</s-table>`;

export const getTemplate = (slot, content, filterPrefixContent) => html`<div
    class="cce-common-list-page"
>
    <s-page class="{{klass}}">
        <div slot="pageTitle">
            <h2 s-if="title">
                <s-button s-if="hasBack" on-click="onBack"
                    ><s-icon-left is-button="{{false}}"
                /></s-button>
                {{title}}
                <span class="title-help" s-if="help">
                    <s-icon-info />
                    <a href="{{help}}" target="_blank">帮助文档</a>
                </span>
            </h2>
            <div class="title-list-tip" s-if="tip">
                <s-alert skin="{{tipSkin || 'info'}}">{{tip}}</s-alert>
            </div>
        </div>
        <div slot="bulk">
            <slot name="bulk">
                <s-button
                    s-if="create"
                    skin="primary"
                    on-click="onCreate"
                    disabled="{{createDisabled}}"
                >
                    <s-icon-plus />
                    {{create}}
                </s-button>
                <s-button
                    s-if="yamlCreate"
                    skin="primary"
                    on-click="onYamlCreate"
                    disabled="{{yamlCreateDisabled}}"
                >
                    {{yamlCreate}}
                </s-button>
                <x-toolbar s-if="hasToolBar" selectedItems="{{selectedItems}}" />
            </slot>
        </div>
        <div slot="filter">
            <x-filter-prefix s-if="hasFilterPrefix" />
            ${filterPrefixContent ? filterPrefixContent : ''}
            <s-input
                class="input-addon"
                width="{{searchbox.width}}"
                placeholder="{{searchbox.placeholder}}"
                on-enter="onSearch"
                value="{=searchbox.value=}"
            >
                <s-select
                    slot="addonBefore"
                    width="{{searchbox.selectWidth || 130}}"
                    placeholder="{{searchbox.placeholder}}"
                    value="{=searchbox.keywordType=}"
                    on-change="onKeywordTypeChange"
                    s-if="searchbox.datasource"
                >
                    <s-option
                        s-for="item in searchbox.datasource"
                        value="{{item.value}}"
                        label="{{item.text}}"
                        disabled="{{item.disabled}}"
                    >
                    </s-option>
                </s-select>
                <s-icon-search slot="suffix" on-click="onSearch" />
            </s-input>
            <s-button on-click="onRefresh" class="s-icon-button">
                <s-icon-refresh is-button="{{false}}" />
            </s-button>
        </div>
        ${getTableTemplate(slot)} ${content ? content : ''}
        <s-pagination
            slot="pager"
            layout="{{'total, pageSize, pager, go'}}"
            s-if="pager.totalCount"
            total="{{pager.totalCount}}"
            page="{=pager.pageNo=}"
            pageSize="{=pager.pageSize=}"
            pageSizes="{{pager.pageSizes}}"
            max-item="{{pager.maxItem}}"
            on-pagerChange="onPagerChange"
            on-pagerSizeChange="onPagerSizeChange"
        />
    </s-page>
</div>`;
