/**
 * @file common/list/template.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';

export const getTemplate = schema => html`
    <div class="{{mainClass}}">
        <app-list-page>
            <div slot="pageTitle" class="app-page-title">
                <h2 s-if="title || remark || (helps && helps.length)">
                    {{title | raw | empty}}
                    <slot name="remark">
                        <span s-if="remark" class="title-remark" title="{{remark}}">
                            <!--bca-disable-next-line-->
                            {{remark | raw}}
                        </span>
                    </slot>
                    <slot name="help">
                        <span s-if="helps && helps.length" class="title-help">
                            <ui-toolbar
                                controls="{{helps}}"
                                on-item-clicked="onToolbarEvent($event)"
                            />
                        </span>
                        <span s-if="helpCustom" class="title-help-custom">
                            <x-help-custom helpCustom="{{helpCustom}}" />
                        </span>
                    </slot>
                </h2>
                ${schema.tip ? schema.tip : ''}
            </div>
            <div slot="bulk">
                <div class="list-page-filter" s-if="withFilter">
                    <ui-biz-filter
                        s-ref="filter"
                        title="{{filter.title}}"
                        form-data="{=$filterPayload=}"
                        submit-text="{{filter.submitText}}"
                        controls="{{filter.controls}}"
                        on-submit="onXFilter($event)"
                        on-item-change="onFilterItemChange($event)"
                    />
                </div>
                <ui-toolbar
                    s-if="toolbar && toolbar.length"
                    controls="{{toolbar}}"
                    on-item-clicked="onToolbarEvent($event)"
                />
                ${schema.toolbarTpl ? schema.toolbarTpl : ''}
                <div class="list-page-tb-left-filter" s-if="withToolbarFilter">
                    <ui-biz-filter
                        s-ref="filter"
                        title="{{filter.title}}"
                        form-data="{=$filterPayload=}"
                        submit-text="{{filter.submitText}}"
                        controls="{{filter.controls}}"
                        on-submit="onXFilter($event)"
                        on-item-change="onFilterItemChange($event)"
                    />
                </div>
            </div>
            <div slot="filter">
                <div class="list-page-tb-right">
                    <ui-toolbar
                        s-if="filter.extraControls"
                        controls="{{filter.extraControls}}"
                        on-item-clicked="onToolbarEvent($event)"
                    />
                    ${schema.rightToolbarTpl ? schema.rightToolbarTpl : ''}
                    <ui-right-toolbar
                        s-ref="right-toolbar"
                        with-searchbox="{{withSearchbox}}"
                        with-download="{{withDownload}}"
                        with-refresh="{{withRefresh}}"
                        searchbox-value="{=$filterPayload.keyword=}"
                        searchbox-keyword-type="{=$filterPayload.keywordType=}"
                        searchbox-placeholder="{{filter.placeholder}}"
                        searchbox-width="{{filter.width}}"
                        searchbox-keyword-types="{{filter.keywordTypes}}"
                        searchbox-select="{{filter.select}}"
                        select-datasource="{{filter.selectDatasource}}"
                        on-refresh="refreshTable"
                        on-search="doSearch"
                        on-download="downloadTable"
                        on-keywordTypeChange="onKeywordTypeChange"
                    />
                    ${schema.columnsFilter ? schema.columnsFilter : ''}
                </div>
            </div>
            <x-main
                columns="{{table.schema}}"
                table="{{table}}"
                custom-data="{{customData}}"
                on-command="onTableCommand($event)"
                on-filter="onFilter($event)"
                on-sort="onSort($event)"
                on-selected-change="onTableRowSelected($event)"
                on-refresh="refreshTable"
                on-row-toggle="onTableRowToggle($event)"
                empty-text="{{table.emptyText}}"
            />

            <div slot="pager" class="{{showTotalCount ? 'flex-between' : ''}}">
                <div class="count-info" s-if="showTotalCount && withPager && pager.count > 0">
                    已选中{{table.selectedItems.length || 0}}条 / 共{{pager.count || 0}}条
                </div>
                <ui-pager
                    s-if="withPager && pager.count > 0"
                    class="list-page-pager"
                    pager="{=pager=}"
                    with-pager-size="{{withPagerSize}}"
                    on-pager-change="onPagerChange($event)"
                />
            </div>
        </app-list-page>
    </div>
`;
