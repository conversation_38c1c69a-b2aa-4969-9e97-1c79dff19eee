/**
 * @file 操作禁用
 * <AUTHOR>
 */
import _ from 'lodash';

const checkerMap = {
    required: 'checkRequired',
    isSingle: 'checkSingle',
    status: 'checkStatus',
    productType: 'checkProductType',
    orderAble: 'checkOrderAble'
};

class PermissionChecker {
    initRules(rules, command) {
        let rulesNew = _.cloneDeep(rules);
        // command为空时检查规则里所有操作，非空只检查当前command操作
        if (command) {
            rulesNew = {[command]: rulesNew[command]};
        }
        _.each(rulesNew, (rule, opt) => {
            rulesNew[opt] = _.isArray(rule) ? rule : [rule];
            // 默认没有设置required时，表示required 值为true
            if (!_.find(rulesNew[opt], item => item.hasOwnProperty('required'))) {
                rulesNew[opt].unshift({required: true});
            }
        });
        return rulesNew;
    }
    get<PERSON>he<PERSON>(rule) {
        let checker = null;
        _.each(checkerMap, (value, key) => {
            if (rule.hasOwnProperty(key)) {
                checker = value;
            }
        });
        return checker;
    }
    checkRequired(rule, command) {
        return !rule.required || this.data.length !== 0;
    }
    checkSingle(rule, command) {
        if (rule.isSingle && this.data.length > 1) {
            return false;
        }
        return true;
    }
    checkStatus(rule, command) {
        // 默认rule.status必须是数组
        let check = true;
        if (!_.isArray(rule.status)) {
            check = false;
        }
        // 有一项数据的status检查不通过就返回false;
        _.each(this.data, item => {
            // 表示状态的字段默认是status,如果规则里有field，默认取data[field]数据作为状态
            let status = rule.field ? item[rule.field] : item.status;
            if (!_.contains(rule.status, status)) {
                check = false;
                return false;
            }
        });
        return check;
    }
    checkProductType(rule, command) {
        // 默认rule.productType必须是数组
        let check = true;
        if (!_.isArray(rule.productType)) {
            check = false;
        }
        // 有一项数据的支付方式不通过就返回false;
        _.each(this.data, item => {
            // 表示支付方式的状态默认是payment,如果规则里有field，默认取data[key]数据作为支付方式
            let productType = rule.field ? item[rule.field] : item.payment;
            if (!_.contains(rule.productType, productType)) {
                check = false;
                return false;
            }
        });
        return check;
    }
    checkOrderAble(rule, command) {
        let check = true;
        // 有一项数据的支付方式不通过就返回false;
        _.each(this.data, item => {
            // 表示订单的状态默认是orderStatus,如果规则里有field，默认取data[key]数据作为订单的状态
            let orderStatus = rule.field ? item[rule.field] : item.orderStatus;
            if (orderStatus) {
                check = false;
                return false;
            }
        });
        return check;
    }

    /**
     * 根据数据判断当前操作是否应当禁用
     *
     * @param {Object} rules 操作限制规则
     * @param {Array | Object} data 操作限制规则的数据
     * @param {string} command 操作名称，为空时检查规则里所有操作
     * @param {Object} options 相关数据
     *
     * @return {Object} 禁用状态及相关文案
     */
    check(rules, data, command = '', options = {}) {
        this.result = {};
        this.data = _.isArray(data) ? data : [data];
        this.rules = this.initRules(rules, command);
        _.each(this.rules, (rule, opt) => {
            this.result[opt] = {};
            let check = true;
            // 对每项操作有一项检查没有通过就返回
            // _.each返回false就终止循环
            _.each(rule, (itemRule, key) => {
                if (typeof (itemRule.custom) === 'function') {
                    this.result[opt] = itemRule.custom(this.data, options) || {};
                    check = this.result[opt].disable;
                }
                else {
                    let checker = this.getChecker(itemRule);
                    if (checker) {
                        check = this[checker](itemRule, opt);
                        !check && (this.result[opt] = {
                            disable: true,
                            message: typeof (itemRule.message) === 'function'
                                        ? itemRule.message(data, itemRule)
                                        : itemRule.message
                        });
                    }
                }
                return check;
            });
        });
        return this.result;
    }
}
export default new PermissionChecker();