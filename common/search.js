/*
 * @Description: 查询相关
 * @Author: <EMAIL>
 * @Date: 2021-07-14 22:02:10
 */

// 时间宏映射
export const selectTimeMap = {
    0: '', // 自定义
    5: 'last-5-min', // 近5分钟
    15: 'last-15-min', // 近15分钟
    60: 'last-1-hou', // 近1小时
    1440: 'last-1-day', // 近1天
    10080: 'last-7-day', // 近7天
};
// 时间宏转话为数字
export function timeMarco2SelectTime(timeMarco) {
    if (!timeMarco) {
        return 0;
    }
    return +Object.keys(selectTimeMap).find(key => selectTimeMap[key] === timeMarco);
}
// 搜索条件映射处理
export function handlerQueryForm(formData) {
    if (!formData) {
        return {};
    }
    const form = {...formData};
    // 自定义时间区间查询的
    // 通过startDateTime 和 endDateTime 提交用户的时间区间值，
    // timeMarco 为空
    form.timeMarco = selectTimeMap[form.selectTime];
    // 用户是用时间宏查询的，
    // 那么将时间宏通过 timeMarco 参数提交
    // startDateTime 和 endDateTime 为空
    if (form.selectTime !== 0) {
        form.startDateTime = '';
        form.endDateTime = '';
    }
    return form;
}
