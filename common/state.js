/**
* @file 全局状态控制器
* <AUTHOR>
*/

import _ from 'lodash';

const OBJECTSTORAGE = ['clusterList'];

const judgeStateValid = {
    clusterList: v => !!(v && v.length)
};

class State {
    constructor() {
        this.clusterUuid = '';
        this.clusterList = [];
        this.namespaceName = '';
        this.init();
    }
    destroy() {
        this.init();
    }
    init() {
        this.setState('clusterUuid', '');
        this.setState('clusterList', []);
        this.setState('namespaceName', '');
    }
    setState(field, v) {
        // 兼容旧逻辑 设置applicationState的值
        this[field] = v;
        if (!v) {
            return;
        }
        sessionStorage.setItem(field, this.ifStorageObject(field) ? JSON.stringify(v) : v);
    }
    getState(field) {
        const storageState = this.getStorageValue(field);
        // 兼容旧逻辑 获取并设置applicationState的值
        const applicationState = this[field];
        if ((storageState !== applicationState) && this.validateState(field, applicationState)) {
            this.setState(field, applicationState);
        }
        return this.getStorageValue(field);
    }
    getStorageValue(field) {
        const v = sessionStorage.getItem(field);
        return this.ifStorageObject(field) ? JSON.parse(v) : v;
    }
    validateState(field, v) {
        const validator = judgeStateValid[field];
        return validator ? validator(v) : !!v;
    }
    ifStorageObject(field) {
        return _.includes(OBJECTSTORAGE, field);
    }
}

export default (new State());
