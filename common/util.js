/**
 *
 * @file common/util.js
 * <AUTHOR>
 */

import _ from 'lodash';

export function isGeelyOffline() {
    return location.host.indexOf('agilecloud') > -1;
}

export function isGeelyOnline() {
    return location.host.indexOf('cloud.geely') > -1;
}

export function isAnicecityOffline() {
    return location.host.indexOf('anicecity.org') > -1;
}

export function isWhlocal() {
    return location.host.indexOf('console.whlocal.com') > -1;
}

export function getSearchSeparator(module) {
    // 吉利环境blb还是老模块
    const geelyEdp = ['blb'];
    // 银联商务的blb、bcc都是老模块
    const whlocalEdp = ['bcc', 'blb'];
    if (isGeelyOffline() || isGeelyOnline() || isAnicecityOffline()) {
        if (_.contains(geelyEdp, module)) {
            return '~';
        }

        return '?';
    }

    if (isWhlocal()) {
        if (_.contains(whlocalEdp, module)) {
            return '~';
        }

        return '?';
    }

    return '?';
}
