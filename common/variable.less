/**
 * @description less变量文件
 * @file common/variable.less
 * <AUTHOR>
*/
@c-y-1: #f39000;
@primary-color: #2468f2;
@grey-color: #666;

@border-color: #d4d6d9;

.c-y-1 {
    color: @c-y-1;
}

.primary-color {
    color: @primary-color;
}
.grey-color {
    color: @grey-color;
}

.f12 {
    font-size: 12px;
}

.flex-center-cce {
    display: flex;
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}
.flex-1 {
    flex: 1;
}

.justify-center {
    justify-content: center;
}

.line-height-20 {
    line-height: 20px;
}

.break-word {
    word-wrap: break-word;
}
.w-1\/2 {
    width: 50%;
}

.mb12 {
    margin-bottom: 12px;
}
