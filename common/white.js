/**
 * white.js
 *
 * @file white.js 白名单
 * <AUTHOR>
 */
/* global $storage */
import _ from 'lodash';
import {ServiceFactory} from '@baiducloud/runtime';

import Client from '../utils/client';
import {PermissionType} from '../utils/enums';

const $flag = ServiceFactory.resolve('$flag');

let permissionTypeList = PermissionType.toArray();
if (!$flag.CceClusterAudit) {
    permissionTypeList = _.filter(permissionTypeList, item => item.alias !== 'AUDIT');
}

let whiteNameList = _.pluck(permissionTypeList, 'text');

export function getWhite() {
    const tasks = [];
    for (let i = 0; i < whiteNameList.length; i++) {
        const task = async name => {
            let whiteValue = await getWhiteByName(name);
            if ($storage.get('cce_white_' + name) !== whiteValue) {
                $storage.set('cce_white_' + name, whiteValue);
            }
        };
        tasks.push(task(whiteNameList[i]));
    }
    return Promise.all(tasks);
}

export function checkWhiteByName(name) {
    return $storage.get('cce_white_' + name) || false;
}

export async function getWhiteByName(name) {
    try {
        const result = await Client.checkWhiteList(name);
        if (result && result.isExist) {
            return true;
        }
        return false;
    } catch (e) {
        return false;
    }
}
