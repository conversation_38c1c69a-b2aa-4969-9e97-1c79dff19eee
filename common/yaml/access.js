/**
 * @file access
 * <AUTHOR>
 */

/* eslint-disable */
export const accessYamlClusterIP = `apiVersion: v1
kind: Service
metadata:
  annotations:
    service.apps.cce.io/topologykey: nodePool/default/web-demo # NodePool级别流量拓扑，注解值的格式为：nodePool/<BatchDeployment namespace>/<BatchDeployment name>
spec:
  selector:
    app: nginx
  type: ClusterIP
  sessionAffinity: None # 默认值，不使用客户端会话亲和性
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
`;

export const accessYamlLoadBalancer = `apiVersion: v1
kind: Service
metadata:
  annotations:
    service.apps.cce.io/topologykey: nodePool/default/web-demo # NodePool级别流量拓扑，注解值的格式为：nodePool/<BatchDeployment namespace>/<BatchDeployment name>
spec:
  selector:
    app: nginx
  type: LoadBalancer
  externalTrafficPolicy: Cluster # 默认值
  sessionAffinity: None
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
`;

export const accessYamlNodePort = `apiVersion: v1
kind: Service
metadata:
  annotations:
    service.apps.cce.io/topologykey: nodePool/default/web-demo # NodePool级别流量拓扑，注解值的格式为：nodePool/<BatchDeployment namespace>/<BatchDeployment name>
spec:
  selector:
    app: nginx
  type: NodePort
  externalTrafficPolicy: Cluster # 默认值
  sessionAffinity: None
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
    nodePort: 30000
`;