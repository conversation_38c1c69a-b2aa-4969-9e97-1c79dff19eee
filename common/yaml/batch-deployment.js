/**
 * @file deployment
 * <AUTHOR>
 */

export let batchDeploymentYaml = `kind: BatchDeployment
apiVersion: apps.cce.io/v1alpha1
metadata:
  name: <应用部署组名称>
  namespace: <应用部署组命名空间>
spec:
  deployGroup: <边缘节点组名称>
  selectedStrategy: none
  workloadTemplate:
    deployment:
      replicas: <期望Pod数>
      selector:
        matchLabels:
          <Pod标签健>: <Pod标签值>
      template:
        metadata:
          labels:
            <Pod标签健>: <Pod标签值>
        spec:
          containers:
            - image: <镜像地址>
              name: <容器名称>
              imagePullPolicy: <Pull策略>
              ports:
                - containerPort: <容器端口>
                  name: <端口名称>
`;

export let deamonSetYaml = `kind: BatchDeployment
apiVersion: apps.cce.io/v1alpha1
metadata:
  name: <应用部署组名称>
  namespace: <应用部署组命名空间>
spec:
  deployGroup: <边缘节点组名称>
  selectedStrategy: none
  workloadTemplate:
    daemonSet:
      selector:
        matchLabels:
          <Pod标签健>: <Pod标签值>
      template:
        metadata:
          labels:
            <Pod标签健>: <Pod标签值>
        spec:
          containers:
            - image: <镜像地址>
              name: <容器名称>
              imagePullPolicy: <Pull策略>
              ports:
                - containerPort: <容器端口>
                  name: <端口名称>
`;

export let statefulSetYaml = `kind: BatchDeployment
apiVersion: apps.cce.io/v1alpha1
metadata:
  name: <应用部署组名称>
  namespace: <应用部署组命名空间>
spec:
  deployGroup: <边缘节点组名称>
  selectedStrategy: none
  workloadTemplate:
    statefulSet:
      replicas: <期望Pod数>
      selector:
        matchLabels:
          <Pod标签健>: <Pod标签值>
      template:
        metadata:
          labels:
            <Pod标签健>: <Pod标签值>
        spec:
          containers:
            - image: <镜像地址>
              name: <容器名称>
              imagePullPolicy: <Pull策略>
              ports:
                - containerPort: <容器端口>
                  name: <端口名称>
`;
