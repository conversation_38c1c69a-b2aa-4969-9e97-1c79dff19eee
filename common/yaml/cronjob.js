/**
 * @file service
 * <AUTHOR>
 */
import {getImagePrefix} from '../../utils/helper';

export let cronjobYaml  = `apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cronjob-example
spec:
  schedule: "*/1 * * * *"
  suspend: false
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  concurrencyPolicy: "Forbid" # 不允许并发执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: hello
            image: ${getImagePrefix()}/cce/busybox:latest
            args:
            - /bin/sh
            - -c
            - date; echo Hello from the Kubernetes cluster
          restartPolicy: OnFailure
`;
