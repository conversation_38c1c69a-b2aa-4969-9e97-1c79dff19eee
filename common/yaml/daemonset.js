/**
 * @file daemonset
 * <AUTHOR>
 */
import {getImagePrefix} from '../../utils/helper';

export let daemonsetYaml  = `apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: daemonset-example
spec:
  minReadySeconds: 0
  selector:
    matchLabels:
      app: daemonset-example
  revisionHistoryLimit: 10
  template:
    metadata:
      labels:
        app: daemonset-example
    spec:
      containers:
        - name: daemonset-example
          image: ${getImagePrefix()}/cce/busybox:latest
          command:
          - sleep
          - "3600"
  updateStrategy:
    type: RollingUpdate
`;
