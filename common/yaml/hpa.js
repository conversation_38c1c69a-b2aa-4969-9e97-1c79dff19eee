/**
 * @file hpa
 * <AUTHOR>
 */
import {getImagePrefix} from '../../utils/helper';
export const hpaYaml = (params, version = 'v2beta2') => {
    const {
        kind,
        hpaName,
        namespace,
        deployName,
        minReplicas,
        maxReplicas,
        cpuUtilization,
        cpuAverageValue,
        memoryUtilization,
        memoryAverageValue,
        podDiskReadRate,
        podDiskWriteRate,
        podNetworkSz,
        podNetworkRx,
    } = params;
    const header = `apiVersion: autoscaling/${version}
kind: HorizontalPodAutoscaler
metadata:
  name: ${hpaName}
  namespace: ${namespace}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: ${kind}
    name: ${deployName}
  minReplicas: ${minReplicas}
  maxReplicas: ${maxReplicas}
  metrics:`;
    const cpuUtilizationTpl = `
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: ${cpuUtilization}`;
    const cpuAverageValueTpl = `
  - type: Resource
    resource:
      name: cpu
      target:
        type: AverageValue
        averageValue: ${cpuAverageValue}m`;
    const memoryUtilizationTpl = `
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: ${memoryUtilization}`;
    const memoryAverageValueTpl = `
  - type: Resource
    resource:
      name: memory
      target:
        averageValue: ${memoryAverageValue}Mi
        type: AverageValue`;
    const podDiskReadRateTpl = `
  - type: Pods
    pods:
      metric:
        name: pod_disk_read_rate
      target:
        type: AverageValue
        averageValue: ${podDiskReadRate * 1024}`;
    const podDiskWriteRateTpl = `
  - type: Pods
    pods:
      metric:
        name: pod_disk_write_rate
      target:
        type: AverageValue
        averageValue: ${podDiskWriteRate * 1024}`;
    const podNetworkSzTpl = `
  - type: Pods
    pods:
      metric:
        name: pod_network_sz
      target:
        type: AverageValue
        averageValue: ${podNetworkSz * 1024}`;
    const podNetworkRxTpl = `
  - type: Pods
    pods:
      metric:
        name: pod_network_rx
      target:
        type: AverageValue
        averageValue: ${podNetworkRx * 1024}`;
    let template = header;
    if (cpuUtilization) {
        template += cpuUtilizationTpl;
    }
    if (cpuAverageValue) {
        template += cpuAverageValueTpl;
    }
    if (memoryUtilization) {
        template += memoryUtilizationTpl;
    }
    if (memoryAverageValue) {
        template += memoryAverageValueTpl;
    }
    if (podDiskReadRate) {
        template += podDiskReadRateTpl;
    }
    if (podDiskWriteRate) {
        template += podDiskWriteRateTpl;
    }
    if (podNetworkSz) {
        template += podNetworkSzTpl;
    }
    if (podNetworkRx) {
        template += podNetworkRxTpl;
    }
    return template;
};

export const cronhpaYaml = params => {
    const {kind, name, namespace, kindName, crons, excludeDates} = params;

    const header = `apiVersion: cce.baidubce.com/v1
kind: CronHPA
metadata:
  name: ${name}
  namespace: ${namespace}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: ${kind}
    name: ${kindName}`;
    const excludeDatesTpl =
        excludeDates && excludeDates.length
            ? `  excludeDates:\n${excludeDates.map(date => `    - "${date}"`).join('\n')}`
            : '';
    const cronsTpl =
        crons && crons.length
            ? `  crons:\n${crons
                  .map(
                      cron =>
                          `    - name: ${cron.name}\n` +
                          `      schedule: "${cron.schedule}"\n` +
                          `      runOnce: ${cron.runOnce}\n` +
                          `      targetSize: ${cron.targetSize}`,
                  )
                  .join('\n')}`
            : '';
    const template = `${header}\n${excludeDatesTpl}\n${cronsTpl}`;

    return template;
};

export const deploymentYaml = `---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: custom-metrics:system:auth-delegator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
- kind: ServiceAccount
  name: custom-metrics-apiserver
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: custom-metrics-auth-reader
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: extension-apiserver-authentication-reader
subjects:
- kind: ServiceAccount
  name: custom-metrics-apiserver
  namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: custom-metrics-apiserver
  name: custom-metrics-apiserver
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: custom-metrics-apiserver
  template:
    metadata:
      labels:
        app: custom-metrics-apiserver
      name: custom-metrics-apiserver
    spec:
      serviceAccountName: custom-metrics-apiserver
      containers:
      - name: custom-metrics-apiserver
        image: ${getImagePrefix()}/jpaas-public/cce-metrics-adapter:latest
        imagePullPolicy: Always
        args:
        - --secure-port=6443
        - --logtostderr=true
        - --monitClientSetDebug=true
        resources:
            limits:
              memory: 200Mi
              cpu: 200m
            requests:
              memory: 20Mi
              cpu: 50m
        ports:
        - containerPort: 6443
          name: https
        - containerPort: 8080
          name: http
        volumeMounts:
        - mountPath: /tmp
          name: temp-vol
        - mountPath: /var/run/secrets/cce/cce-plugin-token
          name: cce-plugin-token
          readOnly: true
        - mountPath: /etc/kubernetes/cloud.config
          name: cloudconfig
          readOnly: true
      volumes:
      - name: temp-vol
        emptyDir: {}
      - name: cce-plugin-token
        secret:
          defaultMode: 0400
          secretName: cce-plugin-token
      - name: cloudconfig
        hostPath:
          path: /etc/kubernetes/cloud.config
       
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: custom-metrics-resource-reader
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: custom-metrics-resource-reader
subjects:
- kind: ServiceAccount
  name: custom-metrics-apiserver
  namespace: kube-system
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: custom-metrics-apiserver
  namespace: kube-system
---
apiVersion: v1
kind: Service
metadata:
  name: custom-metrics-apiserver
  namespace: kube-system
spec:
  ports:
  - name: https
    port: 443
    targetPort: 6443
  - name: http
    port: 80
    targetPort: 8080
  selector:
    app: custom-metrics-apiserver
---
apiVersion: apiregistration.k8s.io/v1beta1
kind: APIService
metadata:
  name: v1beta1.custom.metrics.k8s.io
spec:
  service:
    name: custom-metrics-apiserver
    namespace: kube-system
  group: custom.metrics.k8s.io
  version: v1beta1
  insecureSkipTLSVerify: true
  groupPriorityMinimum: 100
  versionPriority: 100
---
apiVersion: apiregistration.k8s.io/v1beta1
kind: APIService
metadata:
  name: v1beta2.custom.metrics.k8s.io
spec:
  service:
    name: custom-metrics-apiserver
    namespace: kube-system
  group: custom.metrics.k8s.io
  version: v1beta2
  insecureSkipTLSVerify: true
  groupPriorityMinimum: 100
  versionPriority: 200
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: custom-metrics-server-resources
rules:
- apiGroups:
  - custom.metrics.k8s.io
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: custom-metrics-resource-reader
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  - pods
  - services
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: hpa-controller-custom-metrics
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: custom-metrics-server-resources
subjects:
- kind: ServiceAccount
  name: horizontal-pod-autoscaler
  namespace: kube-system`;
