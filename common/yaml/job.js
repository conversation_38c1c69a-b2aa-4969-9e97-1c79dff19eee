/**
 * @file job
 * <AUTHOR>
 */

import {getImagePrefix} from '../../utils/helper';

export let jobYaml = `apiVersion: batch/v1
kind: Job
metadata:
  name: job-example
spec:
  completions: 1 # One-off job
  parallelism: 1 # Non-parallel
  backoffLimit: 6 # default number of retries when job is failed
  template:
    metadata:
      name: pi
    spec:
      containers:
      - name: pi
        image: ${getImagePrefix()}/cce/perl:latest
        command: ["perl",  "-Mbignum=bpi", "-wle", "print bpi(2000)"]
      restartPolicy: Never
`;
