import {getAiImagePrefix} from '../../utils/helper';

export const MXJob = `apiVersion: "kubeflow.org/v1"
kind: "MXJob"
metadata:
  name: "mxnet-job"
spec:
  jobMode: MXTrain
  mxReplicaSpecs:
    Scheduler:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
            # if your libcuda.so.1 is in custom path, set the correct path with the following annotation
            # kubernetes.io/baidu-cgpu.nvidia-driver-lib: /usr/lib64
        spec:
          schedulerName: volcano
          containers:
            - name: mxnet
              image: ${getAiImagePrefix()}/cce-public/mxjob/mxnet:gpu
              resources:
                limits:
                  baidu.com/v100_32g_cgpu: "1"
                  # for gpu core/memory isolation
                  baidu.com/v100_32g_cgpu_core: 5
                  baidu.com/v100_32g_cgpu_memory: "1"
              # if gpu core isolation is enabled, set the following preStop hook for graceful shutdown.
              # ${'`'}train_mnist.py${'`'} needs to be replaced with the name of your gpu process.
              lifecycle:
                preStop:
                  exec:
                    command: [
                      "/bin/sh", "-c",
                      "kill -10 ${'`'}ps -ef | grep train_mnist.py | grep -v grep | awk '{print $2}'${'`'} && sleep 1"
                    ]
    Server:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
            # if your libcuda.so.1 is in custom path, set the correct path with the following annotation
            # kubernetes.io/baidu-cgpu.nvidia-driver-lib: /usr/lib64
        spec:
          schedulerName: volcano
          containers:
            - name: mxnet
              image: ${getAiImagePrefix()}/cce-public/mxjob/mxnet:gpu
              resources:
                limits:
                  baidu.com/v100_32g_cgpu: "1"
                  # for gpu core/memory isolation
                  baidu.com/v100_32g_cgpu_core: 5
                  baidu.com/v100_32g_cgpu_memory: "1"
              # if gpu core isolation is enabled, set the following preStop hook for graceful shutdown.
              # ${'`'}train_mnist.py${'`'} needs to be replaced with the name of your gpu process.
              lifecycle:
                preStop:
                  exec:
                    command: [
                      "/bin/sh", "-c",
                      "kill -10 ${'`'}ps -ef | grep train_mnist.py | grep -v grep | awk '{print $2}'${'`'} && sleep 1"
                    ]
    Worker:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
            # if your libcuda.so.1 is in custom path, set the correct path with the following annotation
            # kubernetes.io/baidu-cgpu.nvidia-driver-lib: /usr/lib64
        spec:
          schedulerName: volcano
          containers:
          - name: mxnet
            image: ${getAiImagePrefix()}/cce-public/mxjob/mxnet:gpu
            env:
              # for gpu memory over request, set 0 to disable
              - name: CGPU_MEM_ALLOCATOR_TYPE
                value: 1
            command: ["python"]
            args: [
              "/incubator-mxnet/example/image-classification/train_mnist.py",
              "--num-epochs","10","--num-layers","2","--kv-store","dist_device_sync","--gpus","0"
            ]
            resources:
              requests:
                cpu: 1
                memory: 1Gi
              limits:
                baidu.com/v100_32g_cgpu: "1"
                # for gpu core/memory isolation
                baidu.com/v100_32g_cgpu_core: 20
                baidu.com/v100_32g_cgpu_memory: "4"
            # if gpu core isolation is enabled, set the following preStop hook for graceful shutdown.
            # ${'`'}train_mnist.py${'`'} needs to be replaced with the name of your gpu process.
            lifecycle:
              preStop:
                exec:
                  command: [
                    "/bin/sh", "-c",
                    "kill -10 ${'`'}ps -ef | grep train_mnist.py | grep -v grep | awk '{print $2}'${'`'} && sleep 1"
                  ]
`;
