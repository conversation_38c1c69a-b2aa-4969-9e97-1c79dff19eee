import {getAiImagePrefix} from '../../utils/helper';

export const PaddleJob = `apiVersion: batch.paddlepaddle.org/v1
kind: PaddleJob
metadata:
  name: resnet
spec:
  cleanPodPolicy: Never
  worker:
    replicas: 2
    template:
      spec:
        schedulerName: volcano
        containers:
          - name: resnet
            image: ${getAiImagePrefix()}/paddle-operator/demo-resnet:v1
            env:
              # for gpu memory over request, set 0 to disable
              - name: CGPU_MEM_ALLOCATOR_TYPE
                value: 1
            command:
            - python
            args:
            - "-m"
            - "paddle.distributed.launch"
            - "train_fleet.py"
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
            resources:
              requests:
                cpu: 1
                memory: 2Gi
              limits:
                baidu.com/v100_16g_cgpu: "1"
        volumes:
        - name: dshm
          emptyDir:
            medium: Memory
`;