/**
 * @file secret
 * <AUTHOR>
 */

const Opaque = `apiVersion: v1
kind: Secret
metadata:
  name: baidu-secret
type: Opaque
data:
  username: dXNlcm5hbWU=
  password: cGFzc3dvcmQ=
`;
const TLSConfig = `apiVersion: v1
kind: Secret
metadata:
  name: secret-tls
type: kubernetes.io/tls
data:
  tls.crt: |
      LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJHVENCeEFJSkFKQ0N1S2l6SzVib0
      1BMEdDU3FHU0liM0RRRUJDd1VBTUJReEVqQVFCZ05WQkFNTUNXeHYKWTJGc2FHOXpkREFl
      RncweU5EQTNNRGt3T0RFMU16RmFGdzB5TkRBM01UQXdPREUxTXpGYU1CUXhFakFRQmdOVg
      pCQU1NQ1d4dlkyRnNhRzl6ZERCY01BMEdDU3FHU0liM0RRRUJBUVVBQTBzQU1FZ0NRUUNv
      cnVzdHloMlpPZmJGClphbmRqcWdXZmYycmxsSmNLTzFsMXpMKzlzMUVDaXAycWtJTkR0Q1
      Nyd0daR1NSaGtQNC9nZjFLQ1o2cURXSTIKOUFDRUdpZy9BZ01CQUFFd0RRWUpLb1pJaHZj
      TkFRRUxCUUFEUVFBd2VFU3h6b3p3YVdlU1hrZzBhOEx2ekJkYwpvUGdTT1FVSkhyaVdUeD
      RNMG5mWXlueTdWbzlpd1Z4VTVzMU9yWGcydVJwTi9xdklVMk9adzdoV1pYb2cKLS0tLS1F
      TkQgQ0VSVElGSUNBVEUtLS0tLQ==
  tls.key: |
      RXhhbXBsZSBkYXRhIGZvciB0aGUgVExTIGNydCBmaWVsZA==`;

export let secretYaml = {Opaque: Opaque, TLS证书: TLSConfig};
