/**
 * @file service
 * <AUTHOR>
 */

export const serviceYamlClusterIP = `apiVersion: v1
kind: Service
metadata:
  name: service-example
  annotations:
    prometheus.io/scrape: "true"
    # service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true" # LB Service 不分配 EIP
spec:
  selector:
    app: nginx
  type: ClusterIP
  sessionAffinity: None # 默认值，不使用客户端会话亲和性
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
`;

export const serviceYamlLoadBalancer = `apiVersion: v1
kind: Service
metadata:
  name: service-example
  annotations:
    prometheus.io/scrape: "true"
    # service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true" # LB Service 不分配 EIP
spec:
  selector:
    app: nginx
  type: LoadBalancer
  externalTrafficPolicy: Cluster # 默认值
  sessionAffinity: None
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
`;

export const serviceYamlNodePort = `apiVersion: v1
kind: Service
metadata:
  name: service-example
  annotations:
    prometheus.io/scrape: "true"
    # service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true" # LB Service 不分配 EIP
spec:
  selector:
    app: nginx
  type: NodePort
  externalTrafficPolicy: Cluster # 默认值
  sessionAffinity: None
  ports:
  - name: nginx
    protocol: TCP
    port: 80
    targetPort: 80
    nodePort: 30000
`;
