/**
 * @file statefulset
 * <AUTHOR>
 */

import {getImagePrefix} from '../../utils/helper';

export let statefulsetYaml  = `apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: statefulset-example
spec:
  serviceName: "nginx"
  replicas: 2
  selector:
    matchLabels:
      app: nginx # has to match .spec.template.metadata.labels
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: ${getImagePrefix()}/cce/nginx-alpine-go:latest
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          timeoutSeconds: 1
          periodSeconds: 5
        ports:
        - containerPort: 80
          name: web
      updateStrategy:
        type: RollingUpdate
`;
