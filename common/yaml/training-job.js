import {getAiImagePrefix} from '../../utils/helper';

/* eslint-disable */
export const TrainingJob = `apiVersion: kongming.cce.baiudbce.com/v1
kind: AITrainingJob
metadata:
  name: job-horovod-test
  namespace: default
spec:
  cleanPodPolicy: All
  completePolicy: Any
  failPolicy: Any
  frameworkType: horovod
  faultTolerant: true
  plugin:
    ssh:
    - ""
    discovery:
    - ""
  priority: normal
  replicaSpecs:
    launcher:
      completePolicy: Any
      failPolicy: Any
      maxReplicas: 1
      minReplicas: 1
      replicaType: master
      replicas: 1
      restartLimit: 100
      restartPolicy: OnNodeFailWithExitCode
      restartTimeLimit: 60
      restartTimeout: 864000
      template:
        metadata:
          creationTimestamp: null
        spec:
          initContainers:
          - args:
            - --barrier_roles=trainer
            - --incluster
            - --name=$(TRAININGJOB_NAME)
            - --namespace=$(TRAININGJOB_NAMESPACE)
            image: ${getAiImagePrefix()}/cce-plugin-dev/jobbarrier:v0.9
            imagePullPolicy: IfNotPresent
            name: job-barrier
            resources: 
              limits:
                cpu: "1"
                memory: 1Gi
              requests: 
                cpu: "1"
                memory: 1Gi
            restartPolicy: Never
            schedulerName: volcano
            terminationMessagePath: /dev/termination-log
            terminationMessagePolicy: File
            securityContext: {}
          containers:
          - command:
            - /bin/bash
            - -c
            - horovodrun -np 3 --min-np=1 --max-np=5 --verbose --log-level=DEBUG  --host-discovery-script /etc/edl/discover_hosts.sh python /horovod/examples/elastic/pytorch/pytorch_synthetic_benchmark_elastic.py
            env:
            image: ${getAiImagePrefix()}/cce-plugin-dev/horovod:v0.23.0
            imagePullPolicy: Always
            name: aitj-0
            resources:
            securityContext:
              capabilities:
                add:
                - SYS_ADMIN
            volumeMounts:
            - mountPath: /dev/shm
              name: cache-volume
          dnsPolicy: ClusterFirstWithHostNet
          terminationGracePeriodSeconds: 30
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1449Gi
            name: cache-volume
    trainer:
      completePolicy: None
      failPolicy: None
      faultTolerantPolicy:
      - exitCodes: 129,10001,127,137,143,129
        restartPolicy: ExitCode
        restartScope: Pod
      - exceptionalEvent: "nodeNotReady,PodForceDeleted"
        restartPolicy: OnNodeFail
        restartScope: Pod
      maxReplicas: 5
      minReplicas: 1
      replicaType: worker
      replicas: 3
      restartLimit: 100
      restartPolicy: OnNodeFailWithExitCode
      restartTimeLimit: 60
      restartTimeout: 864000
      template:
        metadata:
          creationTimestamp: null
        spec:
          containers:
          - command:
            - /bin/bash
            - -c
            - /usr/sbin/sshd && sleep 40000
            image: ${getAiImagePrefix()}/cce-plugin-dev/horovod:v0.23.0
            imagePullPolicy: Always
            name: aitj-0
            resources:
              limits:
                baidu/gpu_p40_8: "1"
              requests:
                baidu/gpu_p40_8: "1"
            securityContext:
              capabilities:
                add:
                - SYS_ADMIN
            volumeMounts:
            - mountPath: /dev/shm
              name: cache-volume
          dnsPolicy: ClusterFirstWithHostNet
          terminationGracePeriodSeconds: 300
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1449Gi
            name: cache-volume
  schedulerName: volcano
`;
