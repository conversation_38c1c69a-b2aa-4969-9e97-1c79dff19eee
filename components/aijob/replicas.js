/**
 * @file components/aijob/replicas.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Form, Alert, Dialog, InputNumber} from '@baidu/sui';
import jsyaml from 'js-yaml';

const {asComponent} = decorators;

const template = html`<div>
<s-dialog
    s-ref="dialog"
    open="{=open=}"
    title="扩缩容"
    class="cce-cluster-aijob-replicas"
    on-confirm="onConfirm">
    <s-form
        form-data="{=formData=}">
        <div class="aijob-configuration">
            <s-alert skin="warning">
                <ul class="training-tip">
                    <li>提示：</li>
                    <li>1、AI任务的框架要求：TensorFlow 1.15+, PyTorch 1.2+，Horovod 0.20.0+</li>
                    <li>2、AI任务需有CheckPoint机制</li>
                    <li>3、Trainer副本数的弹性扩缩容</li>
                    <li>4、期望副本数需要在minReplicas~maxReplicas之间</li>
                    <!-- <li><a href="#" target="_blank">更多信息</a></li> -->
                </ul>
            </s-alert>
            <ul class="aijob-configuration-list">
                <li>
                    <label>任务名称：</label>
                    <span>{{detail.name}}</span>
                </li>
                <li>
                    <label>minReplicas：</label>
                    <span>{{detail.minReplicas}}</span>
                </li>
                <li>
                    <label>maxReplicas：</label>
                    <span>
                        {{detail.maxReplicas}}
                    </span>
                </li>
            </ul>
        </div>
        <s-form-item
            inline
            label="期望副本数（Replicas）：">
            <s-input-number
                value="{=replicas=}"
                min="{{detail.minReplicas}}"
                max="{{detail.maxReplicas}}" />
        </s-form-item>
        <s-form-item
            inline
            label="扩缩容超时（秒）：">
            <s-input-number
                value="{=scaleTimeout=}"
                min="{{0}}" />
        </s-form-item>
    </s-form>
</s-dialog>
</div>
`;

@asComponent('@cce-cluster-aijob-replicas')
export default class Replicas extends Component {
    static template = template

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-alert': Alert,
        's-dialog': Dialog,
        's-input-number': InputNumber
    }

    initData() {
        return {
            open: true,
            replicas: 0,
            scaleTimeout: 0
        };
    }

    attached() {
        this.getAiJobDetail();
    }

    getFormData() {
        return this.data.get('formData');
    }

    getAiJobDetail() {
        const payload = {
            clusterUuid: this.data.get('clusterID'),
            k8sName: this.data.get('k8sName'),
            kind: this.data.get('kind'),
            k8sNamespace: this.data.get('k8sNamespace')
        };
        return this.$http.getJobDetail(payload)
            .then(({result}) => {
                try {
                    const manifest = jsyaml.load(result.manifest);
                    const minReplicas = _.get(manifest, 'spec.replicaSpecs.trainer.minReplicas', 0);
                    const maxReplicas = _.get(manifest, 'spec.replicaSpecs.trainer.maxReplicas', 0);
                    const replicas = _.get(manifest, 'spec.replicaSpecs.trainer.replicas', 0);
                    const scaleTimeout = _.get(manifest, 'spec.replicaSpecs.trainer.scaleTimeout', 30 * 60);
                    Object.assign(result, {minReplicas, maxReplicas});
                    this.data.set('replicas', replicas);
                    this.data.set('scaleTimeout', scaleTimeout);
                }
                catch (e) {}
                this.data.set('detail', result);
            });
    }

    async onConfirm() {
        const {
            clusterID,
            k8sName,
            kind,
            k8sNamespace,
            replicas,
            scaleTimeout
        } = this.data.get();
        return this.$http.scaleAiJob(clusterID, k8sName, kind, k8sNamespace, {
            replicas,
            scaleTimeout
        })
        .then(() => {
            this.data.set('open', false);
            this.fire('success');
        });
    }
}
