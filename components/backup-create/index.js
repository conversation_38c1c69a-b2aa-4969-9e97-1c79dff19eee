import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Dialog, Select, Button, Form, Input, Radio, InputNumber, Tooltip} from '@baidu/sui';
import {BackUpCenterStatus} from '@utils/enums';
import {OutlinedPlus, OutlinedRefresh} from '@baidu/sui-icon';
import {Tip} from '@baidu/sui-biz';
const CornReg = require('cron-validator');
const {asComponent} = decorators;

import './index.less';

let ResourceNum = 0;

const template = html`
    <template>
        <s-dialog
            class="create-backup-task-dialog"
            closeAfterMaskClick="{{false}}"
            open="{=open=}"
            width="800"
            title="{{getTitle}}"
        >
            <div class="content-wrap">
                <s-form
                    s-ref="form"
                    data="{=formData=}"
                    label-align="left"
                    rules="{{rules}}"
                >
                    <div class="basic-title">配置信息</div>
                    <s-form-item label="{{'任务名称：'}}" prop="taskName">
                        <div class="back-rule-item">
                            <s-input width="400" value="{=formData.taskName=}"></s-input>
                            <span class="rule-des-name">-{{clusterUuid}}</span>
                        </div>
                        <div class="help-text">
                            长度为1-63个字符，只能包含数字、小写字母、和“-”，且必须以字母开头，数字或字母结尾
                        </div>
                    </s-form-item>
                    <s-form-item label="{{'备份仓库：'}}" prop="repositoryID">
                        <div class="repository-item">
                            <s-select
                                width="400"
                                value="{=formData.repositoryID=}"
                                datasource="{{backupRepositoriesDatasource}}"
                                placeholder="请选择备份仓库"
                            >
                            </s-select>
                            <s-button class="s-icon-button" on-click="setAvailableBackupRepositories"
                                ><s-icon-refresh class="button-icon" is-button="{{false}}" />
                            </s-button>
                        </div>

                        <div class="help-text">
                            <p>
                                若无合适备份仓库，您可以去<a
                                    href="#/cce/backup/list"
                                    target="_blank"
                                    >备份中心-备份仓库</a
                                >创建
                            </p>
                        </div>
                    </s-form-item>
                    <s-form-item label="{{'定时规则：'}}" prop="schedule" s-if="{{type === 'schedule'}}">
                        <div class="back-rule-item">
                            <s-input width="400" on-change="getScheduleText" value="{=formData.schedule=}"></s-input><span class="rule-des">{{scheduleText}}</span>
                        </div>
                        <div class="help-text">
                        支持Crontab表达式设置定时备份周期，更多信息请参考<a href="https://www.runoob.com/w3cnote/linux-crontab-tasks.html" target="_blank">如何使用Crontab</a><br />
                        格式：分时天月周（如：0 0 * * *表示每天00:00执行一次备份）
                        </div>
                    </s-form-item>
                    <s-form-item label="备份范围：" prop="backupScope">
                        <s-radio-group
                            radioType="button"
                            datasource="{{backupScopeSource}}"
                            value="{=formData.backupScope=}"
                        />
                        <div class="help-text">{{backupScopeText}}</div>
                    </s-form-item>
                    <s-form-item
                        label="{{'备份命名空间：'}}"
                        prop="includedNamespaces"
                        s-if="{{formData.backupScope === 'Specified'}}"
                    >
                        <s-select
                            width="400"
                            value="{=formData.includedNamespaces=}"
                            datasource="{{nameSpaceDatasource}}"
                            placeholder="请选择备份命名空间"
                            multiple
                            taggable
                            searchable
                            clearable
                            labelFilter="{{labelFilter}}"
                        >
                        </s-select>
                    </s-form-item>
                    <s-form-item label="备份有效期：" prop="backupExpirationDays" required>
                        <s-input-number
                            value="{=formData.backupExpirationDays=}"
                            max="{{65536}}"
                            min="{{1}}"
                            width="120"
                        />天
                    </s-form-item>
                    <div class="seniro-wrap">
                        <div class="seior-active" on-click="onToggle">
                            <div class="senior-icon {{!showSenior ? 'toggle' : ''}}"></div>
                            <div class="senior-title">高级配置</div>
                        </div>
                        <div class="senior-des" s-if="!showSenior">{{getSeniorDes}}</div>
                    </div>
                    <div class="senior-config-wrap {{showSenior ? '' : 'senior-fold'}}">
                        <s-form-item
                            label="{{'排除命名空间：'}}"
                            prop="excludedNamespaces"
                            s-if="{{formData.backupScope === 'All'}}"
                        >
                            <s-select
                                width="400"
                                value="{=formData.excludedNamespaces=}"
                                datasource="{{nameSpaceDatasource}}"
                                placeholder="请选择排除命名空间"
                                on-clear="onExNsClear"
                                on-change="excludedNamespacesChange"
                                multiple
                                taggable
                                searchable
                                clearable
                                labelFilter="{{labelFilter}}"
                            >
                            </s-select>
                            <div class="help-text">备份将忽略此命名空间</div>
                        </s-form-item>
                        <s-form-item label="{{'指定备份对象：'}}" prop="includedResources">
                            <s-select
                                width="400"
                                value="{=formData.includedResources=}"
                                datasource="{{resourceList}}"
                                placeholder="请选择指定备份对象"
                                multiple
                                searchable
                                checkAll
                                taggable
                                clearable
                                labelFilter="{{resourceLabelFilter}}"
                            >
                            </s-select>
                        </s-form-item>
                        <s-form-item label="{{'排除备份对象：'}}" prop="excludedResources" s-if="{{resourceList.length && formData.includedResources.length === resourceList.length}}">
                            <s-select
                                width="400"
                                value="{=formData.excludedResources=}"
                                datasource="{{resourceList}}"
                                placeholder="请选择排除备份对象"
                                multiple
                                searchable
                                clearable
                                labelFilter="{{resourceLabelFilter}}"
                            >
                            </s-select>
                        </s-form-item>
                        <s-form-item>
                            <span slot="label"
                                >指定标签备份：<s-tip useNewVersion><div slot="content" class="back-tag-tooltip">
                                <div class="back-tag-wrap">
                                    <div class="key">键：</div>
                                    <div class="value">
                                        由前缀和名称组成，用斜杠（/）分隔，名称不能为空，仅支持字母数字字符、连字符（-）、下划线（_）、或点（.），并且必须以字母数字字符开头和结束，最多63个字符；前缀可选，必须是DNS子域，由点（.）进行分隔，最多253个字符
                                    </div>
                                </div>
                                <div class="back-tag-wrap">
                                    <div class="key">值：</div>
                                    <div class="value">
                                        可以包含字母数字字符、连字符（-）、下划线（_）、或点（.），并且必须以字母数字字符开头和结束，最多63个字符
                                </div>
                            </div></s-tip></span>
                            <div s-for="item,index in tagList" class="tag-item-wrap">
                                <s-form-item label="标准键">
                                    <s-input value="{=item.key=}" placeholder="请输入" width="160"></s-input>
                                </s-form-item>
                                <s-form-item label="值">
                                    <s-input value="{=item.value=}" placeholder="请输入" width="160"></s-input>
                                </s-form-item>
                                <s-button skin="stringfy" on-click="deleteTag(index)">删除</s-button>
                            </div>
                            <s-button skin="stringfy" class="back-dialog-add" on-click="addTag"><s-icon-plus /> 添加</s-button>
                            <div class="help-text">根据指定的标签匹配目标资源对象进行备份</div>
                        </s-form-item>
                    </div>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button skin="primary" on-click="dialogConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

@asComponent('@backup-task-create')
export default class Create extends Component {
    static template = template;

    static computed = {
        backupScopeText() {
            const backupScope = this.data.get('formData.backupScope');
            if (backupScope === 'All') {
                return '备份集群中所有命名空间中的资源';
            }
            return '备份集指定命名空间中的资源';
        },
        getTitle() {
            const type = this.data.get('type');
            return `创建${type === 'normal' ? '备份任务' : '定时备份策略'}`;
        },
        getSeniorDes() {
            const backupScope = this.data.get('formData.backupScope');
            const includedResources = this.data.get('formData.includedResources');
            const resourceList = this.data.get('resourceList');
            return `${backupScope === 'All' ? '排除命名空间 | ' : ''}指定备份对象 | ${
                resourceList.length && includedResources.length === ResourceNum
                    ? '排除备份对象 | '
                    : ''
            }指定标签备份`;
        },
    };
    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-button': Button,
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item,
        's-icon-refresh': OutlinedRefresh,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-icon-plus': OutlinedPlus,
        's-tooltip': Tooltip,
        's-tip': Tip,
    };

    initData() {
        return {
            title: '创建备份任务',
            isEdit: false,
            type: 'normal', // normal schedule
            open: true,
            rules: {
                taskName: [
                    {required: true, message: '请输入任务名称'},
                    {
                        validator(rule, value, callback) {
                            if (!/^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$/.test(value)) {
                                return callback('任务名称不符合规则');
                            }
                            return callback();
                        },
                    },
                ],
                repositoryID: [{required: true, message: '请选择备份仓库'}],
                schedule: [
                    {required: true, message: '请输入定时规则'},
                    {
                        validator(rule, value, callback) {
                            const check = CornReg.isValidCron(value, {
                                alias: true,
                                allowBlankDay: true,
                                allowSevenAsSunday: true,
                            });
                            if (!check) {
                                return callback('定时规则格式错误');
                            }

                            return callback();
                        },
                    },
                ],
            },
            formData: {
                taskName: '',
                repositoryID: '',
                backupScope: 'All',
                backupExpirationDays: 30,
                includedResources: [],
                excludedNamespaces: ['kube-system', 'kube-public', 'kube-node-lease', 'cce-backup'],
            },
            backupScopeSource: [
                {
                    label: '全部命名空间',
                    value: 'All',
                },
                {
                    label: '指定命名空间',
                    value: 'Specified',
                },
            ],
            nameSpaceDatasource: [],
            labelFilter(labels) {
                return labels.join(',');
            },
            resourceLabelFilter(labels) {
                if (labels.length === ResourceNum) {
                    return '全部资源对象';
                }
                return labels.join(',');
            },

            tagList: [],
            resourceList: [],
            backupRepositoriesDatasource: [],
            scheduleText: '-',
            showSenior: false,
        };
    }

    inited() {
        const {isEdit, editTask} = this.data.get('');
        if (isEdit) {
            this.data.set('formData', {
                ...editTask,
                taskName: '',
            });
            if (editTask.labelSelector) {
                let tagList = Object.keys(editTask.labelSelector).map(k => {
                    return {
                        key: k,
                        value: editTask.labelSelector[k],
                    };
                });
                this.data.set('tagList', tagList);
            }
        }
    }

    attached() {
        this.getNamespaceList();
        this.getBackResourceObjList();
        this.setAvailableBackupRepositories();
    }
    excludedNamespacesChange({value}) {
        const defaultNamespaces = ['kube-system', 'kube-public', 'kube-node-lease', 'cce-backup'];
        const newNamespaces = value.filter(ns => !defaultNamespaces.includes(ns));
        this.nextTick(() => {
            this.data.set('formData.excludedNamespaces', [...defaultNamespaces, ...newNamespaces]);
        });
    }

    onToggle() {
        this.data.set('showSenior', !this.data.get('showSenior'));
    }

    async setAvailableBackupRepositories() {
        const {isEdit, formData} = this.data.get('');
        const payload = {
            pageNo: 1,
            pageSize: 1000,
        };
        const data = await this.$http.getBackupRepositoryList(payload);
        const backupRepositorys = data.result.backupRepositoryPage.backupRepositorys;
        const allRegion = window.$context.getEnum('AllRegion');
        const datasource = backupRepositorys.map(item => ({
            label: `${item.name}/${allRegion.valueIndex[item.region]?.text}`,
            value: item.repositoryID,
            disabled: item.status !== BackUpCenterStatus.AVAILABLE,
        }));
        if (isEdit) {
            const {repositoryID} = formData;
            if (repositoryID && !datasource.some(d => d.value === repositoryID)) {
                this.data.set('formData.repositoryID', '');
            }
        }
        this.data.set('backupRepositoriesDatasource', datasource);
    }

    getScheduleText = _.debounce(e => {
        const {clusterUuid} = this.data.get('');
        // 先校验格式，通过后请求接口
        const check = CornReg.isValidCron(e.value, {
            alias: true,
            allowBlankDay: true,
            allowSevenAsSunday: true,
        });
        if (check) {
            this.$http
                .getCornTabText({
                    schedule: e.value,
                    clusterUuid,
                })
                .then(res => {
                    const {scheduleDescribe} = res.result;
                    this.data.set('scheduleText', scheduleDescribe);
                });
        }
    }, 1000);

    async getBackResourceObjList() {
        const {clusterUuid, isEdit, editTask} = this.data.get('');
        let resourceList = [];
        try {
            const res = await this.$http.getBackResourceObjList(clusterUuid);
            const {apiResourceLists} = res?.result;
            (apiResourceLists || []).forEach(d => {
                (d.apiResources || []).forEach(m => {
                    if (m.namespaced && !resourceList.find(v => v.value === m.kind)) {
                        resourceList.push({
                            label: m.kind,
                            value: m.kind,
                        });
                    }
                });
            });
            ResourceNum = resourceList.length;
            this.data.set('resourceList', resourceList);
            if (!isEdit || (editTask?.excludedResources && editTask?.excludedResources.length)) {
                this.data.set(
                    'formData.includedResources',
                    resourceList.map(d => d.value),
                );
            }
        } catch (err) {
            console.error(err);
        }
    }

    addTag() {
        this.data.push('tagList', {
            key: '',
            value: '',
        });
    }

    deleteTag(index) {
        this.data.splice('tagList', [index, 1]);
    }

    onExNsClear() {
        this.nextTick(() => {
            this.data.set('formData.excludedNamespaces', [
                'kube-system',
                'kube-public',
                'kube-node-lease',
                'cce-backup',
            ]);
        });
    }

    async getNamespaceList() {
        const {clusterUuid} = this.data.get('');
        try {
            const payload = {clusterUuid};
            const {result} = await this.$http.listAppNamespace(payload);
            let namespaceList = _.uniq(
                _.map(_.get(result, 'namespaces', []), item => _.get(item, 'objectMeta.name', '')),
            );
            namespaceList = _.map(namespaceList, item => {
                let namespaceItem = {
                    label: item,
                    value: item,
                };
                if (
                    ['kube-system', 'kube-public', 'kube-node-lease', 'cce-backup'].includes(item)
                ) {
                    namespaceItem.disabled = true;
                }
                return namespaceItem;
            });
            this.data.set('nameSpaceDatasource', namespaceList);
        } catch (err) {
            console.error(err);
        }
    }

    async dialogConfirm() {
        await this.ref('form').validateFields();
        const {formData, tagList, clusterUuid, type, nameSpaceDatasource} = this.data.get('');
        const labelSelector = {};
        tagList.forEach(d => {
            if (d.key) {
                labelSelector[d.key] = d.value;
            }
        });
        const {
            taskName,
            repositoryID,
            backupScope,
            includedNamespaces,
            excludedNamespaces,
            includedResources,
            excludedResources,
            backupExpirationDays,
            schedule,
        } = formData;
        let param = {
            taskName: `${taskName}-${clusterUuid}`,
            repositoryID,
            backupScope,
            labelSelector,
            backupExpirationDays,
            includedResources,
            excludedResources,
        };
        if (excludedResources && excludedResources.length) {
            param.includedResources = includedResources.filter(d => !excludedResources.includes(d));
        }
        if (backupScope === 'Specified') {
            param.includedNamespaces = includedNamespaces;
        }
        if (backupScope === 'All') {
            param.excludedNamespaces = excludedNamespaces;
            param.includedNamespaces = nameSpaceDatasource
                .filter(d => !d.disabled)
                .map(d => d.value)
                .filter(d => !excludedNamespaces.includes(d));
        }
        if (type === 'schedule') {
            param.schedule = schedule;
            await this.$http.createBackScheduleTask({
                backupTaskArgs: param,
                clusterUuid,
            });
        } else {
            await this.$http.createBackTask({
                backupTaskArgs: param,
                clusterUuid,
            });
        }
        this.fire('success');
        this.close();
    }
    close() {
        this.dispose && this.dispose();
        this.fire('cancel');
    }
}
