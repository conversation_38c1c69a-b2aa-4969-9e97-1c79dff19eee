.create-backup-task-dialog {
    .basic-title {
        font-size: 14px;
        color: #151b26;
        line-height: 22px;
        font-weight: 500;
        margin: 8px 0 16px 0;
    }
    .s-icon-button {
        padding: 0 7px;
        margin-left: 5px;
        svg {
            position: relative;
            top: -1px;
        }
    }
    .help-text {
        font-size: 12px;
        color: #84868c;
        line-height: 20px;
    }
    .s-dialog > .s-dialog-wrapper {
        max-height: 700px;
    }
    .back-dialog-add {
        padding: 0;
        display: flex;
        align-items: center;
        svg {
            margin-right: 6px;
            margin-top: -2px;
        }
    }
    .s-form-item-label {
        width: 110px;
        .s-trigger-container {
            left: -10px;
            position: relative;
        }
    }
    .tag-item-wrap {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .s-form-item {
            margin-bottom: 0;
            margin-right: 8px;
        }
        .s-form-item-label > label {
            padding-left: 0;
        }
        .s-form-item-label {
            width: 42px !important;
        }
    }
    .seniro-wrap {
        display: flex;
        align-items: center;
        margin: 8px 0 16px 0;
        .seior-active {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .senior-icon {
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #000;
        }
        .senior-title {
            font-size: 14px;
            color: #151b26;
            line-height: 22px;
            font-weight: 500;
            margin-left: 8px;
        }
        .senior-des {
            font-size: 12px;
            color: #5c5f66;
            line-height: 20px;
            margin-left: 16px;
        }
        .toggle {
            transform: rotate(180deg);
        }
    }
    .senior-config-wrap {
        max-height: auto;
        transition: max-height 1s ease-out;
    }
    .senior-fold {
        max-height: 0;
        overflow: hidden;
        transition: max-height 1s ease-out;
    }
    .back-rule-item {
        .rule-des {
            margin-left: 8px;
        }
        .rule-des-name {
            display: inline-block;
        }
    }
}

.back-tag-tooltip {
    .back-tag-wrap {
        display: flex;
        font-size: 12px;
        .value {
            color: #84868c;
            width: 200px;
        }
    }
}
