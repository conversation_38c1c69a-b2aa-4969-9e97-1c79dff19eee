import _ from 'lodash';
import {Component, defineComponent} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Empty} from '@baidu/sui-biz';
import {Dialog, Button, Tabs, Loading} from '@baidu/sui';
import {FormItem, Select} from '@baiducloud/bce-ui/san';

import './index.less';

const {asComponent, invokeComp} = decorators;

const template = html`<div class="backup-empty-wrap">
    <div s-if="{{openStatus !== 'success'}}">
        <div class="open-title">开启应用备份，支持 Kubernetes 应用数据容灾和迁移</div>
        <div class="open-des">
            备份中心是 CCE 基于开源项目 Velero
            提供的集群扩展能力，用于对容器化应用进行安全的备份、恢复与迁移。备份的数据将存储在备份仓库所绑定的对象存储
            BOS Bucket 中。
        </div>
        <div class="open-des-icon">
            <div class="des-icon des-icon1"></div>
            <div>
                计费：对象存储 BOS 将会按照实例的存储数据进行收费，具体参考<a
                    target="_blank"
                    href="https://cloud.baidu.com/doc/BOS/s/2k8dzoj86"
                >
                    BOS 计费说明</a
                >
            </div>
        </div>
        <div class="open-des-icon">
            <div class="des-icon des-icon2"></div>
            <div class="bls-audit-icon-wrap">
                组件：开启时系统将自动为你安装组件
                <div class="bls-audit-icon">
                    <div class="bls-audit-icon-png"></div>
                    CCE Backup Controller
                </div>
            </div>
        </div>
        <s-button skin="primary" on-click="turnOn" class="bls-open-button">立即开启</s-button>
    </div>
    <div s-else>
        <div class="open-title">开启中，正在为你准备配置...</div>
        <div class="bls-process-wrap">
            <div s-for="item, index in openProcess" class="bls-process-item">
                <div class="process-line" s-if="{{index !== openProcess.length - 1}}"></div>
                <div class="bls-process-item-top">
                    <div class="process-icon {{item.status}}"></div>
                    <div class="process-title">{{item.title}}</div>
                </div>
                <div class="bls-process-item-des">
                    <span s-if="{{item.status === 'loading'}}">{{item.des}}</span>
                    <span s-if="{{item.status === 'error'}}" class="bls-process-item-des-error"
                        >{{item.errMsg}}，<a on-click="turnOn">重试</a></span
                    >
                </div>
            </div>
        </div>
    </div>
</div>`;

@asComponent('@cce-back-manger-unopen')
export default class extends Component {
    static template = template;

    static components = {
        's-button': Button,
    };

    initData() {
        return {
            openStatus: 'init',
            openProcess: [
                {
                    title: '安装 CCE Backup Controller 组件',
                    status: 'loading', // 状态有 init loading success error四种
                    des: '安装中...',
                    errMsg: '',
                },
                {
                    title: '启动应用备份',
                    status: 'init',
                    des: '启动中...',
                    errMsg: '',
                },
            ],
        };
    }

    async turnOn() {
        const {clusterUuid} = this.data.get('');
        this.data.set('openProcess[0].status', 'loading');
        try {
            this.data.set('openStatus', 'success');
            // 安装备份组件
            await this.$http.installComp(clusterUuid, {
                name: 'cce-backup-controller',
            });
            this.fire('changeBackCompStatus');
        } catch (e) {
            this.data.set('openProcess[0].errMsg', '安装失败');
            this.data.set('openProcess[0].status', 'error');
            console.error(e);
        }
    }
}
