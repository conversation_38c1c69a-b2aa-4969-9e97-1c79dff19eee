.backup-empty-wrap {
    min-width: 980px;
    height: 320px;
    background-image: url('../../img/bls-empty-bg.png');
    background-size: cover;
    padding: 40px;
    border-radius: 6px;
    .open-title {
        font-size: 24px;
        color: #151b26;
        line-height: 22px;
        font-weight: 500;
        margin-bottom: 24px;
    }
    .open-des {
        width: 1000px;
        font-size: 14px;
        color: #151b26;
        line-height: 22px;
        font-weight: 400;
        margin-bottom: 24px;
    }
    .open-des-icon {
        font-size: 14px;
        color: #151b26;
        line-height: 22px;
        font-weight: 400;
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }
    .des-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }
    .des-icon1 {
        background: url('../../img/bls-price.png');
    }

    .des-icon2 {
        background: url('../../img/bls-con.png');
    }
    .bls-audit-icon-wrap {
        display: flex;
        align-items: center;
    }
    .bls-audit-icon {
        margin-left: 8px;
        background: #f7f7f9;
        border: 1px solid #d4d6d9;
        border-radius: 10px;
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        padding: 0 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .bls-audit-icon-png {
            width: 12px;
            height: 12px;
            background: url('../../img/bls-con2.png');
            background-size: cover;
            margin-right: 8px;
        }
    }
    .bls-open-button {
        margin-top: 45px;
    }
    .bls-process-wrap {
        .bls-process-item {
            position: relative;
            .process-line {
                width: 2px;
                height: 40px;
                background: #e8e9eb;
                position: absolute;
                top: 20px;
                left: 5px;
            }
            .bls-process-item-top {
                display: flex;
                align-items: center;
                .process-icon {
                    width: 12px;
                    height: 12px;
                    background: #e8e9eb;
                    border-radius: 12px;
                    margin-right: 12px;
                }
                .loading {
                    background-color: transparent;
                    border-top: 1px solid #2468f2;
                    border-right: 1px solid #2468f2;
                    border-bottom: 1px solid #2468f2;
                    border-left: 1px solid transparent;
                    animation: rotate 1s linear infinite;
                }
                .success {
                    background: url('../../img/bls-pass.svg');
                    background-size: cover;
                }
                .error {
                    background: url('../../img/bls-error.svg');
                    background-size: cover;
                }
                .process-title {
                    font-size: 14px;
                    color: #151b26;
                    line-height: 20px;
                    font-weight: 400;
                }
            }
            .bls-process-item-des {
                height: 24px;
                padding-left: 24px;
                font-size: 12px;
                color: #84868c;
                line-height: 20px;
                font-weight: 400;
                margin-top: 4px;
                margin-bottom: 12px;
                .bls-process-item-des-error {
                    color: #f33e3e;
                }
            }
        }
    }
    a {
        cursor: pointer;
    }
}
@media only screen and (max-width: 1440px) {
    .backup-empty-wrap {
        .open-des {
            width: 800px;
        }
    }
}
