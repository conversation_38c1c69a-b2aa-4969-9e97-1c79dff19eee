/**
 * @file components/batch-deployments/deploy-group.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Select} from '@baiducloud/bce-ui/san';
import {decorators, html} from '@baiducloud/runtime';

const {asComponent} = decorators;

const template = html`
<div>
    <ui-select
        width="{{width}}"
        datasource="{{datasource}}"
        value="{=value=}" />
</div>
`;

@asComponent('@cluster-deploy-group-select')
class DeployGroupControl extends Component {
    static components = {
        'ui-select': Select
    }
    static template = template;
    initData() {
        return {
            width: 220
        };
    }

    inited() {
        this.getDeployGroupList();
    }

    attached() {
        this.watch('clusterUuid', value => {
            if (value) {
                this.getDeployGroupList(value);
            }
        });
    }

    async getDeployGroupList(clusterUuid = this.data.get('clusterUuid')) {
        if (!clusterUuid) {
            return;
        }
        const value = this.data.get('value');
        try {
            const response = await this.$http.getNodegroupsList({
                clusterID: clusterUuid,
                pageSize: 0,
                pageNo: 0
            });

            const datasource = _.map(response.page.result, item => {
                return {text: item.name, value: item.name};
            });
            this.data.set('datasource', datasource);
            if (!datasource.length) {
                this.data.set('value', null);
            }
            const names = _.uniq(_.map(response.page.result, 'name'));
            if (datasource.length && (!value || !_.includes(names, value))) {
                this.data.set('value', datasource[0].value);
            }
        }
        catch (error) {
            this.data.set('value', null);
        }
    }
}
