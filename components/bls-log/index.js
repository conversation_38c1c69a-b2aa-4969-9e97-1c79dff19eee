import {defineComponent, DataTypes} from 'san';
import pic from './bls_log.png';
import core from './core_dns.png';

export default defineComponent({
    template: /*san*/ `
    <template>
        <img s-if="type === 'core'" style="{{style}}" src="{{core}}" class="{{class}}"/>
        <img s-else style="{{style}}" src="{{src}}" class="{{class}}"/>
    </template>`,
    dataTypes: {
        size: DataTypes.number,
        class: DataTypes.string,
        type: DataTypes.string,
    },
    initData() {
        return {
            src: pic,
            core: core,
        };
    },
    computed: {
        style() {
            const size = this.data.get('size');
            if (size) {
                return `width:${size}px;`;
            }
        },
    },
});
