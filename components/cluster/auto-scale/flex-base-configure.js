/**
 * 集群配置
 *
 * @file components/cluster/auto-scale/flex-base-configure.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';

import tips from '../../../utils/tips';
import rule from '../../../utils/rule';
import cluster from '../../../utils/cluster';
import {getAvaliableByMask, getAvaliableContent} from '../../../utils/network';
import {ConflictType, ClusterNodeType as NodeType, PermissionType} from '../../../utils/enums';
import {checkWhiteByName} from '../../../common/white';
import {getVpcName, getSubnetName} from '../../../utils/helper';

const {asComponent, invokeBceSanUI} = decorators;

/* eslint-disable */
const template = html`<div>
    <ui-biz-legend label="基础配置" class="cluster-auto-scale-create-card cluster-configure">
        <ui-form>
            <ui-form-item required label="伸缩组名称：" inline>
                <ui-text-box
                    width="260"
                    title="伸缩组名称"
                    value="{=groupName=}"
                    placeholder="请输入伸缩组名称"
                />
                <div s-if="!groupNameValid" class="cce-error">
                    名称必须填写，支持大小写字母、数字、中文以及-_
                    /.特殊字符，必须以字母开头，长度1-65
                </div>
            </ui-form-item>
            <ui-form-item
                required
                label="最小节点数："
                inline
                help="0~{{maxNodeLimit}}"
                class="min-node-num-item"
            >
                <ui-number-textline min="0" max="{{maxNodeNum}}" value="{=minNodeNum=}" />
            </ui-form-item>
            <ui-form-item
                required
                label="最大节点数："
                inline
                help="0~{{maxNodeLimit}}"
                class="max-node-num-item"
            >
                <ui-number-textline
                    min="{{minNodeNum}}"
                    max="{{maxNodeLimit}}"
                    value="{=maxNodeNum=}"
                />
            </ui-form-item>
            <ui-form-item required label="可用区：" inline>
                <span class="cce-tip-grey cluster-info">
                    多可用区资源完全一致，提供跨机房容灾能力，如有访问公网需求，参考：
                    <a href="${tips.doc.networkPractice}" target="_blank">CCE访问公网实践</a>
                </span>
                <br />
                <span class="cce-tip-grey cluster-info" s-if="masterExposed">
                    选择多可用区时，Master节点将部署至第一个可用区。
                </span>
                <ui-loading size="small" s-if="zoneList.length === 0" />
                <div
                    s-if="isExpand || (productType && productType === 'prepay')"
                    class="logical-zone"
                    s-for="zone, index in zoneList"
                >
                    <ui-radio-box
                        name="group"
                        title="{{zone.name}}"
                        checked="{{zone.checked}}"
                        on-change="onLogicalZoneChangeRadio($event, zone, index)"
                    />
                    <div s-if="zone.checked" class="subnet">
                        <label required>节点子网：</label>
                        <div s-if="zone.subnetList">
                            <ui-select
                                width="200"
                                value="{=subnetIds[zone.value]=}"
                                datasource="{{zone.subnetList}}"
                                on-change="onSubnetChange($event, index)"
                            />
                            <ui-tip message="{{tips.cluster.subnet}}" />
                            <span s-if="zone.subnetType === 3" class="cce-tip-grey"
                                >该子网为NAT专属子网，节点不允许绑定EIP</span
                            >
                        </div>
                        <ui-loading size="small" s-else />
                        <div s-if="zone.subnetList && zone.subnetList.length < 1" class="cce-error">
                            暂无数据
                        </div>
                    </div>
                </div>
                <div s-else s-for="zone, index in zoneList" class="logical-zone">
                    <ui-check-box
                        on-change="onLogicalZoneChange($event, zone, index)"
                        title="{{zone.name}}"
                        checked="{{zone.checked}}"
                    />
                    <div s-if="zone.checked" class="subnet">
                        <label required>节点子网：</label>
                        <div s-if="zone.subnetList">
                            <ui-select
                                width="200"
                                value="{=subnetIds[zone.value]=}"
                                datasource="{{zone.subnetList}}"
                                on-change="onSubnetChange($event, index)"
                            />
                            <ui-tip message="{{tips.cluster.subnet}}" />
                            <span s-if="zone.subnetType === 3" class="cce-tip-grey"
                                >该子网为NAT专属子网，节点不允许绑定EIP</span
                            >
                        </div>
                        <ui-loading size="small" s-else />
                        <div s-if="zone.subnetList && zone.subnetList.length < 1" class="cce-error">
                            暂无数据
                        </div>
                    </div>
                </div>
                <div s-if="isExpand && logicalZone.length > 1" class="cce-error">
                    集群扩容场景，仅支持选定一个可用区
                </div>
                <div s-if="!subnetValid" class="cce-error">
                    选择的可用区节点网络中没有子网，请先创建子网或者选择其他私有网络区
                </div>
                <div s-if="!logicalZoneValid" class="cce-error">请选择一个可用区</div>
            </ui-form-item>
        </ui-form>
    </ui-biz-legend>
</div> `;
/* eslint-enable */

@asComponent('@cce-flex-base-configure')
@invokeBceSanUI
export default class FlexBaseConfigure extends Component {
    static template = template;

    initData() {
        return {
            tips: tips,
            zoneList: [],
            vpc: {
                status: 'loading',
            },
            logicalZone: [],
            subnetIds: {},
            cidr0: '172',
            cidr0Datasource: [
                {text: '172', value: '172'},
                {text: '192', value: '192'},
                {text: '10', value: '10'},
            ],
            cidr1: '16',
            cidr2: '0',
            mask: '16',
            maskTip: '',
            groupNameValid: false,
            clusterCommentValid: true,
            logicalZoneValid: true,
            subnetValid: true,
            containerNetError: null,
            nodeType: NodeType.getValueFromAlias('BCC'),
            nodeTypeSelectedIndex: 0,
            // 专属服务器相关数据
            exclusiveServerList: [],
            exclusiveServerValue: '',
            exclusiveServer: {
                status: 'loading',
            },
            exclusiveServerValid: true,
            minNodeNum: 0,
            maxNodeNum: 1,
            maxNodeLimit: 1000,
        };
    }

    static computed = {
        cidr1Datasource() {
            const cidr0 = this.data.get('cidr0');
            if (cidr0 !== '10') {
                return _.find(cluster.cidr, item => item.value === cidr0).children;
            }
            return null;
        },
        maskDatasource() {
            const cidr0 = this.data.get('cidr0');
            if (cidr0 !== '10') {
                return [
                    {text: '16', value: '16'},
                    {text: '17', value: '17'},
                    {text: '18', value: '18'},
                    {text: '19', value: '19'},
                ];
            }
            return [
                {text: '14', value: '14'},
                {text: '15', value: '15'},
                {text: '16', value: '16'},
                {text: '17', value: '17'},
                {text: '18', value: '18'},
                {text: '19', value: '19'},
            ];
        },
        containerNet() {
            const cidr0 = this.data.get('cidr0');
            const cidr1 = this.data.get('cidr1');
            const cidr2 = this.data.get('cidr2');
            const mask = this.data.get('mask');
            return cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + '0/' + mask;
        },
        groupNameValid() {
            const name = this.data.get('groupName');
            return rule.NAME.pattern.test(name);
        },
        logicalZoneValid() {
            const logicalZone = this.data.get('logicalZone');
            return logicalZone.length > 0;
        },
        cidrValid() {
            const cidr1 = this.data.get('cidr1');
            const cidr2 = this.data.get('cidr2');
            const mask = this.data.get('mask');
            let result1 = getAvaliableByMask(mask, 1);
            let result2 = getAvaliableByMask(mask, 2);
            return (
                !(_.indexOf(result1, parseInt(cidr1, 10)) > -1) ||
                !(_.indexOf(result2, parseInt(cidr2, 10)) > -1)
            );
        },
    };

    cidr1Focus() {
        let mask = this.data.get('mask');
        let result = getAvaliableByMask(mask, 1);
        this.data.set('maskTip', getAvaliableContent(result));
    }

    cidrBlur() {
        this.data.set('maskTip', '');
    }

    cidr2Focus() {
        let mask = this.data.get('mask');
        let result = getAvaliableByMask(mask, 2);
        this.data.set('maskTip', getAvaliableContent(result));
    }

    maskChange(e) {
        let value = parseInt(e.value, 10);
        if (value <= 8) {
            this.data.set('cidr1', 0);
            this.data.set('cidr1Disable', true);
            this.data.set('cidr0Disable', true);
        } else {
            this.data.set('cidr1Disable', false);
            this.data.set('cidr0Disable', false);
        }

        if (value > 16) {
            this.data.set('cidr2Disable', false);
        } else {
            this.data.set('cidr2', 0);
            this.data.set('cidr2Disable', true);
        }

        this.onCheckContainerNet();
    }

    loadVpcList() {
        this.data.set('vpc', {status: 'loading'});
        return this.$http
            .getVpcList({})
            .then(data => {
                let result = [];
                _.each(data.result || [], item => {
                    let text = getVpcName(item.name) + (item.cidr ? '（' + item.cidr + '）' : '');
                    result.push({
                        value: item.vpcId,
                        text: text,
                        title: text,
                        defaultVpc: item.defaultVpc,
                        cidr: item.cidr,
                        shortId: item.shortId,
                    });
                });
                this.data.set('vpc', {status: 'done', payload: result});
                !this.data.get('vpcId') && this.data.set('vpcId', result[0].value);
                this.data.set(
                    'isDefaultVpc',
                    result[0].defaultVpc && result[0].cidr.split('/')[0] === '0.0.0.0',
                );
                return result;
            })
            .then(result => {
                let logicalZone = this.data.get('logicalZone');
                let vpcId = this.data.get('vpcId');
                if (vpcId && logicalZone.length > 0) {
                    return _.each(logicalZone, zone => this.loadSubnetList(vpcId, zone));
                }
            })
            .catch(data => this.data.set('vpc', {status: 'error'}));
    }

    loadSubnetList(vpcId, zone) {
        vpcId = vpcId || this.data.get('vpcId');
        zone = zone || this.data.get('logicalZone')[0];
        let subnetIds = this.data.get('subnetIds') || {};
        const {zoneList, masterExposed} = this.data.get();
        this.subnetDatasource = this.subnetDatasource || {};
        let zoneIndex = _.findIndex(zoneList, item => item.value === zone);
        this.data.set(`zoneList[${zoneIndex}].subnetList`, '');
        const subnetTypes = (masterExposed && [1]) || [1, 3];
        const payload = {vpcId, zone, subnetTypes};

        return this.$http
            .getSubnetList(payload)
            .then(data => {
                let result = [];
                _.each(data.result || [], item => {
                    let text =
                        getSubnetName(item.name) + (item.cidr ? '（' + item.cidr + '）' : '');
                    result.push({
                        value: item.subnetId,
                        text: text,
                        title: text,
                        cidr: item.cidr,
                        subnetType: item.subnetType,
                    });
                });
                // 不需要每次切换vpc就要加载一般子网，将加载过来额子网保存下来,key值为logicalZone+vpc
                this.subnetDatasource[zone] = this.subnetDatasource[zone] || {};
                this.subnetDatasource[zone][vpcId] = result;
                subnetIds[zone] = result[0] ? result[0].value : '';
                this.data.set('subnetIds', subnetIds);
                this.data.set(`zoneList[${zoneIndex}].subnetList`, result);
                this.data.set(
                    `zoneList[${zoneIndex}].subnetType`,
                    result[0] ? result[0].subnetType : '',
                );

                return result;
            })
            .catch(data => this.data.set('subnetList', 'error'));
    }

    checkContainerNet() {
        let {vpcId, vpc, containerNet, isExpand} = this.data.get();
        let selectedVpc = vpc.payload ? _.find(vpc.payload, item => item.value === vpcId) : {};
        if (!isExpand && selectedVpc.cidr) {
            return this.$http
                .checkContainerNet({
                    vpcShortId: selectedVpc.shortId,
                    vpcCidr: selectedVpc.cidr,
                    containerNetCidr: containerNet,
                })
                .then(response => {
                    const {isConflict, conflictNet, conflictVpcRoute} = response;
                    let containerNetError = !!isConflict;
                    if (conflictNet) {
                        const {netType, clusterId} = conflictNet;
                        containerNetError = {
                            ...response,
                            clusterId: (clusterId === 'THIS_CLUSTER' && '当前集群') || clusterId,
                            code: (ConflictType.fromValue(netType) || {}).code,
                        };
                    }
                    if (conflictVpcRoute) {
                        containerNetError = {
                            ...response,
                            code: 'Cce.ContainerNetConflictVpcRoute',
                        };
                    }
                    this.data.set('containerNetError', containerNetError);
                })
                .catch(error => this.data.set('containerNetError', error));
        }
    }

    onLogicalZoneChange(e, zone, index) {
        let {vpcId, zoneList} = this.data.get();
        let logicalZone = [];
        let self = this;
        if (e.value) {
            this.loadSubnetList(vpcId, zone.value);
        }
        _.each(zoneList, item => {
            if (
                (zone.value !== item.value && item.checked) ||
                (zone.value === item.value && e.value)
            ) {
                logicalZone.push(item.value);
            }
        });
        this.data.set(`zoneList[${index}].checked`, e.value);
        this.data.set('logicalZone', logicalZone);
        this.dispatch('ui:logical-zone-change', {logicalZone});
        this.dispatch('updatePrice');
        this.dispatch('updateMasterPrice');
        this.nextTick(() => {
            self.data.set('subnetValid', self.setSubnetValid());
        });
    }

    onLogicalZoneChangeRadio(e, zone, index) {
        // 预支付不支持多可用区 暂时处理 @lisa 2019-04-12
        let {vpcId, zoneList} = this.data.get();
        let logicalZone = [];
        if (e.value) {
            this.loadSubnetList(vpcId, zone.value);
        }
        let newList = _.map(zoneList, (item, itemIndex) => {
            if (itemIndex === index) {
                logicalZone.push(item.value);
                item.checked = e.value;
            } else if (itemIndex !== index && item.checked) {
                item.checked = false;
            }
            return item;
        });
        this.data.set('zoneList', newList);
        this.data.set('logicalZone', logicalZone);
        this.dispatch('ui:logical-zone-change', {logicalZone});
        this.dispatch('updatePrice');
        this.nextTick(() => {
            this.data.set('subnetValid', this.setSubnetValid());
        });
    }

    onSubnetChange(e, index) {
        let selectedItem =
            _.find(this.data.get('zone.subnetList'), item => item.value === e.value) || {};
        let subnetType = selectedItem && selectedItem.subnetType ? selectedItem.subnetType : '';
        this.data.set(`zoneList[${index}].subnetType`, subnetType);
    }

    onCheckContainerNet() {
        this._invokeCheckContainerNet();
    }

    getComponentData() {
        let subnet = {};
        let subnetDatasource = this.subnetDatasource || {};
        let {
            cluster,
            groupName,
            vpcId,
            vpcCidr,
            logicalZone,
            containerNet,
            subnetIds,
            vpc,
            minNodeNum,
            maxNodeNum,
        } = this.data.get();
        _.each(subnetIds, (value, key) => {
            if (_.contains(logicalZone, key)) {
                subnet[key] = _.find(subnetDatasource[key][vpcId], item => item.value === value);
            }
        });

        return {
            clusterUuid: cluster ? cluster.clusterUuid : '',
            groupName,
            minNodeNum,
            maxNodeNum,
            vpcId,
            logicalZone,
            containerNet,
            // 获取价格的时候，subnetUuid不影响价格，可以取任意选中的那个，下订单的时候，subnetUuid需要区分可用区
            subnetUuid: _.get(subnet, `${logicalZone[0]}.value`, '') || '',
            subnet,
            vpc: _.find(vpc.payload, item => item.value === vpcId || item.cidr === vpcCidr),
            slaveVmCount: cluster ? cluster.slaveVmCount : 0,
        };
    }

    validateComponentData() {
        let valid = true;
        let {
            groupName,
            isExpand,
            vpc,
            vpcId,
            vpcCidr,
            logicalZoneValid,
            cidrValid,
            logicalZone,
            containerNetError,
        } = this.data.get();
        let selectedVpc = vpc.payload
            ? _.find(vpc.payload, item => item.value === vpcId || item.cidr === vpcCidr)
            : {};
        if (!groupName || !this.data.get('groupNameValid')) {
            this.data.set('groupNameValid', false);
            // 伸缩组名称检查
            valid = false;
        }
        if (!logicalZoneValid || cidrValid || !vpcId || containerNetError) {
            // 可用区个数 cird合法性 节点网络 网络冲突 检查
            // DCC实例下无需检查可用区个数
            valid = false;
        }
        // 扩容暂时只支持一个可用区，后续还需支持2个可用区
        if (isExpand && logicalZone.length > 1) {
            valid = false;
        }
        // 非默认vpc必须要有子网，默认vpc可以没有子网
        if (!selectedVpc.defaultVpc) {
            let enableSubnet = this.setSubnetValid();
            this.data.set('subnetValid', enableSubnet);
            valid = valid && enableSubnet;
        }
        return valid;
    }

    setSubnetValid() {
        let {subnetIds, logicalZone, vpc, vpcId, vpcCidr} = this.data.get();
        let selectedVpc = vpc.payload
            ? _.find(vpc.payload, item => item.value === vpcId || item.cidr === vpcCidr)
            : {};
        let enableSubnet = true;
        if (!selectedVpc.defaultVpc) {
            _.each(subnetIds, (value, key) => {
                if (_.contains(logicalZone, key) && !value) {
                    enableSubnet = false;
                }
            });
        }
        return enableSubnet;
    }

    updateZoneList() {
        const rawZoneList = this.data.get('rawZoneList');
        if (!rawZoneList.length) {
            return;
        }
        let zoneList = _.map(rawZoneList, (item, index) => ({
            name: window.$zone.getLabel(item.logicalZone),
            value: item.logicalZone,
            checked: item.defaultZone,
        }));
        const selectedZone = _.find(zoneList, item => item.checked) || zoneList[0];
        selectedZone.checked = true;
        this.data.set('zoneList', zoneList);
        const logicalZone = [selectedZone.value];
        this.data.set('logicalZone', logicalZone);
        this.dispatch('ui:logical-zone-change', {logicalZone});
    }

    attached() {
        let isExpand = this.data.get('isExpand');
        this._invokeCheckContainerNet = _.debounce(function () {
            this.checkContainerNet();
        }, 1e3);
        // 加载vpc列表，加载完后检查一下容器网络是否冲突
        this.loadVpcList().then(() => this.onCheckContainerNet());
        // 可用区加载完后需要加载一下子网
        this.watch('logicalZone', logicalZone => {
            let vpcId = this.data.get('vpcId');
            if (vpcId) {
                _.each(logicalZone, zone => this.loadSubnetList(vpcId, zone));
            }
        });
        this.watch('vpcId', vpcId => {
            this.dispatch('ui:vpc-change', vpcId);
        });
        this.watch('rawZoneList', zoneList => {
            this.updateZoneList();
        });
        this.watch('zoneList', zoneList => {
            let hasNat =
                _.findIndex(
                    this.data.get('zoneList'),
                    item => item.subnetType === 3 && item.checked,
                ) >= 0;
            if (hasNat !== this.data.get('hasNat')) {
                this.dispatch('subnetTypeChange', {hasNat});
                this.data.set('hasNat', hasNat);
            }
        });
        // 如果是扩容集群，需要将集群信息填充到this.data
        if (isExpand) {
            this.watch('cluster', cluster => {
                let logicalZone = this.data.get('logicalZone');
                this.data.set('comment', cluster.comment);
                this.data.set('vpcCidr', cluster.vpcCidr);
                this.data.set('containerNet', cluster.containerNet);
                this.data.set('vpcId', cluster.vpcUuid);
                this.data.set('kubernetesValue', cluster.version);
                // 加载当前vpcId下的子网
                _.each(logicalZone, zone => this.loadSubnetList(cluster.vpcUuid, zone));
            });
        }

        // select控件通过setProperties的方式改变datasource不会改变value，导致cidr1值错误
        // common的select控件兼容后这里可以删除
        this.watch('cidr1Datasource', cidr1Datasource => {
            if (cidr1Datasource === null) {
                this.data.set('cidr1', 0);
                this.data.set('cidr2', 0);
            } else {
                this.data.set('cidr1', cidr1Datasource[0].value);
            }
        });

        this.watch('maskDatasource', maskDatasource => {
            this.data.set('mask', maskDatasource[0].value);
        });
    }
}
