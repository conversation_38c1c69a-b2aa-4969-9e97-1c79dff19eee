/**
 * @file components/cluster/auto-scale/global-config.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Toast} from '@baiducloud/bce-ui/san';

import '../../common/form/register-form-items';
import {createCustomForm as createForm} from '../../common/form/create-form';
import FormDialog from '../../common/form/form-dialog';
import {ExpanderMethod, SkipInstance} from '../../../utils/enums';

const {asComponent, invokeBceSanUI} = decorators;

const ChooseList = [
    {
        text: '开启',
        value: true,
    },
    {
        text: '关闭',
        value: false,
    },
];

const ConfigureInfo = {
    controls: [
        {
            label: '集群名称',
            name: 'clusterName',
            type: 'string',
        },
        {
            label: '集群ID',
            name: 'clusterUuid',
            type: 'string',
        },
        {
            label: '自动缩容',
            name: 'scalingDownEnable',
            type: 'boxgroup',
            datasource: ChooseList,
        },
        {
            label: '缩容阀值',
            name: 'scalingDownThreshold',
            type: 'withtip',
            tag: 'ui-form-numbertextline',
            addon: '%',
            max: 80,
            min: 20,
            message:
                '伸缩组内节点资源（cpu、gpu、mem）利用率均低于设定阈值时，集群可能会触发自动缩容。' +
                '默认输入范围：20 - 80',
            layerWidth: 200,
            visibleOn: {
                scalingDownEnable: true,
            },
        },
        {
            label: '缩容触发时延',
            name: 'scalingDownDelay',
            type: 'withtip',
            tag: 'ui-form-numbertextline',
            addon: '分钟',
            max: 60,
            min: 1,
            message:
                '在配置的缩容触发时延内，节点资源利用率持续低于缩容阈值，集群可能会触发自动缩容。' +
                '默认输入范围：1 - 60',
            layerWidth: 200,
            visibleOn: {
                scalingDownEnable: true,
            },
        },
        {
            label: '最大并发缩容数',
            name: 'scalingDownBulk',
            type: 'withtip',
            tag: 'ui-form-numbertextline',
            max: 20,
            min: 1,
            message: '当节点的利用率为0时，并发缩容节点的数目。' + '默认输入值范围：1 - 20',
            layerWidth: 200,
            visibleOn: {
                scalingDownEnable: true,
            },
        },
        {
            label: '扩容后缩容启动间隔',
            name: 'scalingDownDelayAfterAdd',
            type: 'withtip',
            tag: 'ui-form-numbertextline',
            addon: '分钟',
            max: 60,
            min: 1,
            message:
                '扩容出来的节点经过此间隔后开始评估是否可以被缩容。' + '默认输入值范围：1 - 60',
            layerWidth: 200,
            visibleOn: {
                scalingDownEnable: true,
            },
        },
        {
            label: '不缩容以下节点',
            name: 'skipInstance',
            type: 'boxgroup',
            boxType: 'checkbox',
            visibleOn: {
                scalingDownEnable: true,
            },
            datasource: SkipInstance.toArray(),
        },
        {
            label: '扩容算法',
            name: 'expander',
            type: 'radioselect',
            datasource: ExpanderMethod.toArray(),
            help:
                '<a href="https://github.com/kubernetes/autoscaler/blob/master/cluster-autoscaler/FAQ.md#what-are-expanders"' +
                ' target="_blank">' +
                '扩容算法介绍' +
                '</a>',
        },
        {
            label: '竞价等待时间',
            name: 'bidMinutes',
            type: 'numbertextline',
            min: 1,
            max: 15,
            visibleOn: {
                bidMinutesEnable: true,
            },
        },
    ],
};

const template = html`<template>
    <ui-form-dialog
        s-if="open"
        s-ref="formDialog"
        title="全局配置"
        width="{{800}}"
        on-ok="onSubmit"
        on-close="onCloseDialog()"
        form-comp="{{InstallInfo}}"
        form-data="{=formData=}"
        form-errors="{=formErrors=}"
        loading="{{loading}}"
        class="cce-cluster-auto-scale-global-dialog"
    />
</template> `;

@asComponent('@cce-flex-global-config')
@invokeBceSanUI
export default class FlexGlobalConfig extends Component {
    static components = {
        'ui-form-dialog': FormDialog,
    };

    static template = template;

    initData() {
        return {
            InstallInfo: createForm(ConfigureInfo),
            open: true,
        };
    }

    inited() {
        const formData = this.data.get('formData') || {};
        this.data.set(
            'formData',
            _.extend(
                {},
                {
                    scalingDownThreshold: 50,
                    scalingDownDelay: 10,
                    scalingDownBulk: 10,
                    scalingDownDelayAfterAdd: 10,
                    expander: 'random',
                    skipInstance: ['scalingDownSkipLocalStorage'],
                    scalingDownEnable: false,
                },
                formData,
            ),
        );
    }

    onCloseDialog() {
        this.data.set('open', false);
        this.data.set('loading', false);
    }

    onSubmit(formData) {
        this.data.merge('formData', formData);
        delete formData.clusterName;

        this.data.set('loading', true);
        const payload = this.data.get('formData');

        payload.scalingDownSkipLocalStorage = _.includes(
            payload.skipInstance,
            'scalingDownSkipLocalStorage',
        );
        payload.scalingDownSkipSystemPods = _.includes(
            payload.skipInstance,
            'scalingDownSkipSystemPods',
        );

        delete payload.gpuScalingDownThreshold;

        return this.$http
            .editAutoscalerGlobalConfig(payload)
            .then(res => {
                this.fire('ok');
                Toast.success('修改成功');
            })
            .catch(e => Toast.error('修改失败'))
            .then(() => this.onCloseDialog());
    }
}
