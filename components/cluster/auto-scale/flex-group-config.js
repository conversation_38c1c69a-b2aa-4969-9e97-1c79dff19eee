/**
 * @file components/cluster/auto-scale/flex-group-config.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {createForm, Toast} from '@baiducloud/bce-ui/san';

import '../../common/form/register-form-items';
import FormDialog from '../../common/form/form-dialog';
import {PermissionType} from '../../../utils/enums';
import {checkWhiteByName} from '../../../common/white';

const {asComponent, invokeBceSanUI} = decorators;

const getConfigureInfo = instanceNum => ({
    controls: [
        {
            label: '名称/ID',
            name: 'name',
            type: 'string',
        },
        {
            label: '最小节点数',
            name: 'minNodeNum',
            type: 'numbertextline',
            min: 0,
            max: instanceNum,
            help: `0~${instanceNum}`,
        },
        {
            label: '最大节点数',
            name: 'maxNodeNum',
            type: 'numbertextline',
            min: 0,
            max: instanceNum,
            help: `0~${instanceNum}`,
        },
        {
            label: '扩容优先级',
            name: 'priority',
            type: 'numbertextline',
            min: 1,
            max: 100,
            help: '1~100',
        },
    ],
});

const template = html`
    <template>
        <ui-form-dialog
            s-if="open"
            s-ref="formDialog"
            title="修改伸缩组配置"
            width="{{800}}"
            on-ok="onSubmit"
            on-close="onCloseDialog()"
            form-comp="{{InstallInfo}}"
            form-data="{=formData=}"
            form-errors="{{formErrors}}"
            loading="{{loading}}"
        />
    </template>
`;

@asComponent('@cce-flex-group-config')
@invokeBceSanUI
export default class FlexGroupConfig extends Component {
    static components = {
        'ui-form-dialog': FormDialog,
    };
    static template = template;

    initData() {
        return {
            open: true,
            formData: {
                reduceLimit: 50,
                coldTime: 600,
            },
            autoScalerGroupWhiteTag: true,
        };
    }

    inited() {
        const instanceNum = this.data.get('autoScalerGroupWhiteTag') ? 1000 : 200;
        this.data.set('InstallInfo', createForm(getConfigureInfo(instanceNum)));
    }

    onCloseDialog() {
        this.data.set('open', false);
        this.data.set('loading', false);
    }

    onSubmit(formData) {
        this.data.merge('formData', formData);

        const {payload} = this.data.get();
        const ifValid = this.validateFormData();

        if (!ifValid) {
            return;
        }

        this.data.set('loading', true);

        return this.$http
            .editAutoscalerGroup({...payload, ...this.data.get('formData')})
            .then(res => Toast.success('修改成功'))
            .catch(e => Toast.error('修改失败'))
            .then(() => {
                this.onCloseDialog();
                this.fire('ok');
            });
    }

    validateFormData(formData = this.data.get('formData')) {
        const formErrors = {};
        let {minNodeNum, maxNodeNum} = formData;
        if (parseInt(minNodeNum, 10) > parseInt(maxNodeNum, 10)) {
            formErrors.maxNodeNum = '最大节点数需大于最小节点数';
        }

        this.data.set('formErrors', formErrors);

        return !_.keys(formErrors).length;
    }
}
