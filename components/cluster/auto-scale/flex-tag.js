/**
 * @file components/cluster/auto-scale/flex-tag.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {TagEditPanel} from '@baiducloud/tag-sdk/san';

import {createK8sForm} from '../../../common/biz/helper';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<template>
    <ui-biz-legend
        label="{{hiddenLegend ? '' : '标签'}}"
        class="{{hiddenLegend ? 'simple-label' : 'cluster-auto-scale-create-card tag-configure'}}"
    >
        <ui-form>
            <ui-form-item
                inline
                label="{{hiddenLabel ? '' : '资源标签：'}}"
                help="温馨提示：资源标签支持您按各种标准（如用途、所有者或项目）对云上资源进行分类；每个标签包含键和值两部分。"
            >
                <tag-edit-panel
                    hasReminder="{{false}}"
                    instances="{{defaultInstances}}"
                    options="{{tagListRequster}}"
                    error-msg="{=validateErrorInfo=}"
                    has-arrow="{{true}}"
                    hide-not-match-item="{{false}}"
                    icon-name="link"
                    s-ref="bccTags"
                />
            </ui-form-item>
            <ui-form-item inline label=" " s-if="{{!hiddenRelationTag}}">
                <div class="relation-tag">
                    其关联资源CDS、EIP统一添加标签： <ui-switch checked="{=relationTag=}" />
                </div>
            </ui-form-item>
        </ui-form>

        <label-form
            s-if="!hiddenK8s"
            s-ref="labelsForm"
            form-data="{{formData}}"
            isflex="{{true}}"
        />
    </ui-biz-legend>
</template> `;

@asComponent('@cce-flex-tag')
@invokeBceSanUI
export default class FlexTag extends Component {
    static components = {
        'tag-edit-panel': TagEditPanel,
        'label-form': createK8sForm('伸缩组', false),
    };

    static template = template;

    initData() {
        return {
            formData: {},
            defaultInstances: [],
            validateErrorInfo: null,
            tagListRequster: this.getTagList.bind(this),
            relationTag: true,
            hiddenRelationTag: false,
        };
    }

    getTagList() {
        const payload = {
            region: ['global'],
            serviceType: [],
        };
        return this.$http.getTagList(payload).then(data => data.result);
    }

    validateForm() {
        const bccTags = this.ref('bccTags');
        const tags = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');
        const promiseArr = [bccTags.validate()];
        tags && promiseArr.push(tags.validateForm());
        return Promise.all(promiseArr);
    }

    getFormData() {
        const bccTags = this.ref('bccTags');
        const tags = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');

        const result = {bccTags: _.filter(bccTags.pickTags(), t => t.tagKey)};
        result.tags = tags && _.map(tags.getValue(), (value, key) => ({key, value}));
        result.relationTag = this.data.get('relationTag');
        return result;
    }
}
