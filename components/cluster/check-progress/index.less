.check-progress {
    // 弹窗
    &-dialog {
        .s-dialog {
            .s-dialog-wrapper {
                max-height: 100%;

                .s-dialog-footer {
                    display: none;
                    border-top: unset;
                    padding: 0;
                }
            }
        }
    }

    &-info {
        margin-left: 13px;
    }

    // 进度条
    &-precent {
        display: flex;
        align-items: center;
        height: 30px;
        padding-left: 13px;
        &-info {
            height: 20px;
            display: flex;
            align-items: center;
        }
        .percent-icon {
            line-height: 16px;
            .s-icon {
                cursor: unset;
            }
        }
        .s-button {
            padding-right: 0;
        }
        .s-progress-info {
            position: relative;
            top: -15px;
            line-height: 20px;
        }
        .s-progress {
            // padding: 5px 0;
        }
    }
}
