/**
 * @description 查看当前任务进度
 * @file components/cluster/check-progress/index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Progress, Loading} from '@baidu/sui';
import {OutlinedCloseCircle, OutlinedPauseCircle} from '@baidu/sui-icon';
import moment from 'moment';

import type {Workflow, TaskGroup, WatchDogStatus} from '@types';

import PopverConfirm from '@components/common/popver-confirm';
import {WorkFlowStatus, WorkFlowType} from '@utils/enums';
import http from '@utils/client';

import UpgradeList from './upgrade-list';

import './index.less';

/* eslint-disable */
const template = html`
    <template>
        <s-dialog
            class="check-progress-dialog"
            width="750"
            open="{=open=}"
            foot="{{false}}"
            on-close="onCancel"
        >
            <div slot="title">
                <template s-if="!loading">
                    <template s-if="isMonitorTask"> 执行进度 </template>
                    <template s-else> {{cluster.clusterUuid}} {{title}} </template>
                </template>
            </div>
            <div s-if="loading" class="flex-center justify-center" style="width: 100%;">
                <s-loading loading="{{loading}}" />
            </div>
            <template s-else>
                <div s-if="isMonitorTask">
                    状态：
                    <span class="status {{workflow.status.phase | statusClass}}">
                        {{workflow.status.phase | statusText}}
                    </span>
                    <s-button
                        s-if="workflow.status.phase === 'Failed'"
                        skin="stringfy"
                        on-click="retryMonitorTask"
                        disabled="{{reseting}}"
                        class="ml20"
                    >
                        重新执行
                    </s-button>
                </div>
                <s-progress
                    class="check-progress-precent"
                    s-else-if="workflow.status.phase !== WorkFlowStatus.SUCCESSDED"
                    percent="{{percent}}"
                    color="{{colorMap[workflow.status.phase] || ['#999999']}}"
                    width="{{538}}"
                >
                    <div slot="info" class="check-progress-precent-info">
                        <template s-if="workflow.status.phase === WorkFlowStatus.UPGRADING">
                            <span>{{percent}}%</span>
                            <popver-confirm
                                text="暂停升级"
                                content="暂停升级后，将暂停未开始执行的任务，进行中的任务将继续执行，你可以后续再继续未升级任务。请确定是否要暂停升级？"
                                on-confirm="suspendResumeTask('pause')"
                                data-track-id="{{dataTrackId}}"
                                track-name="暂停升级"
                            />
                        </template>
                        <template
                            s-if="workflow.status.phase === WorkFlowStatus.FAILED
                        || workflow.status.phase === WorkFlowStatus.PAUSED"
                        >
                            <s-icon-pause-circle
                                s-if="workflow.status.phase === WorkFlowStatus.PAUSED"
                                color="#999999"
                                isButton="{{false}}"
                                class="percent-icon"
                            />

                            <s-icon-close-circle
                                s-else
                                isButton="{{false}}"
                                color="#EA2E2E"
                                class="percent-icon"
                            />

                            <s-button
                                skin="stringfy"
                                on-click="suspendResumeTask('resume')"
                                track-id="{{dataTrackId}}"
                                track-name="继续升级"
                            >
                                继续升级
                            </s-button>
                            <popver-confirm
                                text="取消升级"
                                content="取消升级后，未开始执行的任务将不会再执行，进行中的任务将继续执行，您可以后续再次继续执行升级操作。请确定是否要取消升级？"
                                on-confirm="cancelWorkflow"
                                data-track-id="{{dataTrackId}}"
                                track-name="取消升级"
                            />
                        </template>
                    </div>
                </s-progress>

                <section class="check-progress-info line-height-20">
                    <div
                        class="grey-color"
                        s-if="workflow.spec.workflowType === WorkFlowType.UPGRADENODESK8SVERSION"
                    >
                        本次共升级<span class="primary-text">{{totalNodes}}</span>个节点，已完成
                        <span class="primary-text">{{succeedNodes}}</span>个节点
                    </div>
                    <div
                        class="grey-color"
                        s-if="!isMonitorTask && watchDogStatus.totalPodCount > 0"
                    >
                        当前集群中不可用Pod数为
                        <span class="primary-text">{{watchDogStatus.unhealthyPodCount || 0}}</span>
                        个，当前集群不可用Pod数比例为
                        <span class="primary-text">{{unhealthyPodProportion}}</span>
                        <span> {{maxPodPercentText}} </span>
                    </div>
                </section>
                <upgrade-list
                    type="{{
                        workflow.spec.workflowType === WorkFlowType.UPGRADENODESK8SVERSION
                            ? 'Worker'
                            : 'Master'
                    }}"
                    timeline="{{!isMonitorTask}}"
                    finishedTaskCount="{{workflow.status.finishedTaskCount}}"
                    totalTaskCount="{{workflow.status.totalTaskCount}}"
                    postCheck="{{postCheck}}"
                    operate="{{operate}}"
                    status="{{workflow.status.phase}}"
                    data-track-id="{{dataTrackId}}"
                />
            </template>
        </s-dialog>
    </template>
`;
/* eslint-disable */
export class ClusterCheckProgressDialog extends Component {
    _cacheInterval: NodeJS.Timeout | null = null;
    static template = template;
    static filters = {
        statusClass(status: string) {
            return WorkFlowStatus.fromValue(status).klass;
        },
        statusText(status) {
            if (status === WorkFlowStatus.UPGRADING) {
                return '进行中';
            }
            return WorkFlowStatus.getTextFromValue(status);
        },
    };
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-progress': Progress,
        's-icon-close-circle': OutlinedCloseCircle,
        's-icon-pause-circle': OutlinedPauseCircle,
        's-loading': Loading,
        'popver-confirm': PopverConfirm,
        'upgrade-list': UpgradeList,
    };
    static computed = {
        progress() {
            const operate = this.data.get('operate');
            const total: number = operate.taskList.length;
            const successded: number = operate.taskList.filter(
                i => i.workflowTaskPhase === WorkFlowStatus.SUCCESSDED,
            ).length;
            return `(${successded} / ${total})`;
        },
        percent() {
            const workflow: Workflow = this.data.get('workflow');
            const finished = workflow.status.finishedTaskCount || 0;
            const total = workflow.status.totalTaskCount || 1;
            return +((finished / total) * 100).toFixed(2);
        },
        unhealthyPodProportion() {
            const watchDogStatus: WatchDogStatus = this.data.get('watchDogStatus');
            const totalPodCount = watchDogStatus.totalPodCount || 1;
            const unhealthyPodCount = watchDogStatus.unhealthyPodCount || 0;
            return `${((unhealthyPodCount / totalPodCount) * 100).toFixed(2)}%`;
        },
        maxPodPercentText(): string {
            const workflow = this.data.get('workflow');
            const percent = workflow.spec?.watchDogConfig?.unhealthyPodsPercent;
            if (!percent || percent <= 0) {
                return '（最大不可用占比未设置）';
            }
            return html`（最大不可用占比为${percent}%)`;
        },
        // 当前仅用于worker节点升级的节点展示
        totalNodes(): number {
            const spec = this.data.get('workflow.spec');
            const config = spec.config || {};
            return config?.upgradeNodesK8SVersionWorkflowConfig?.cceInstanceIDList?.length || 0;
        },
        // 当前仅用于worker节点升级的节点展示
        succeedNodes(): number {
            const operate: TaskGroup = this.data.get('operate');
            const taskList = operate.taskList || [];
            let succeed = 0;
            taskList.forEach(i => {
                if (
                    i.workflowTaskType === WorkFlowType.UPGRADENODESK8SVERSION &&
                    i.workflowTaskPhase === WorkFlowStatus.SUCCESSDED
                ) {
                    succeed += i.taskConfig.cceInstanceIDList?.length || 0;
                }
            });
            return succeed;
        },

        isMonitorTask(): boolean {
            const workflowType = this.data.get('workflow.spec.workflowType') || '';
            return workflowType === WorkFlowType.UPGRADEMONITORCOLLECTOR;
        },
    };

    static messages = {
        reloadTable: async () => {
            this.data.set('loading', true);
            await this.setWorkFlowDetail();
            this.data.set('loading', false);
        },
    };

    initData() {
        return {
            WorkFlowType,
            WorkFlowStatus,
            title: '升级进度',
            nodeType: 'Master',
            open: false,
            process: {},
            loading: true,
            finished: {
                status: true,
                time: '',
            },
            colorMap: {
                [WorkFlowStatus.FAILED as string]: ['#EA2E2E'],
                [WorkFlowStatus.UPGRADING as string]: ['#108CEE'],
                [WorkFlowStatus.SUCCESSDED as string]: ['#5FB333'],
                [WorkFlowStatus.PAUSED as string]: ['#999999'],
                [WorkFlowStatus.DELETING as string]: ['#EA2E2E'],
                [WorkFlowStatus.DELETED as string]: ['#EA2E2E'],
                [WorkFlowStatus.PENDING as string]: ['#999999'],
                [WorkFlowStatus.UNKNOWN as string]: ['#999999'],
            },

            workflow: {
                spec: {},
                status: {},
            },
            postCheck: {
                taskGroupPhase: '',
                taskList: [],
            },
            watchDogStatus: {},
            operate: {
                taskList: [],
            },
            dataTrackId: 'cce_cluster_upgrade',
        };
    }

    attached() {
        this.watch('open', flag => {
            if (flag) {
                this.setWorkFlowDetail();
            }
        });
    }

    /**
     *  获取请求数据
     */
    getPayload() {
        const cluster = this.data.get('cluster');

        return {
            clusterID: cluster.clusterUuid,
            workflowID: cluster.upgradeWorkflowID,
        };
    }

    /**
     *  获取workflow并设置
     */
    async setWorkFlowDetail() {
        try {
            const {
                result: {workflow},
            } = await http.getWorkflow(this.getPayload());
            workflow.status?.taskGroupList?.forEach((taskGroup: TaskGroup) => {
                if (taskGroup.taskGroupName === 'PostCheck') {
                    this.data.set('postCheck', taskGroup);
                } else if (taskGroup.taskGroupName === 'Operate') {
                    if (taskGroup.taskList) {
                        this.data.set('operate', taskGroup);
                    }
                }
            });
            if (workflow.status.phase === WorkFlowStatus.SUCCESSDED) {
                this._cacheInterval && clearInterval(this._cacheInterval);
                this.fire('taskSucceed');
            } else if (workflow.status.phase === WorkFlowStatus.UPGRADING) {
                this._time = setTimeout(() => {
                    this.setWorkFlowDetail();
                    clearTimeout(this._time);
                }, 2000);
            }
            this.data.set('workflow', workflow);
            this.data.set('watchDogStatus', workflow.status?.watchDogStatus || {});
        } catch (error) {
            this.fire('refresh');
            this.onCancel();
        } finally {
            this.data.set('loading', false);
        }
    }

    /**
     * 暂停/继续workflow
     */
    async suspendResumeTask(action: 'resume' | 'pause') {
        this.data.set('loading', true);
        try {
            const payload = this.getPayload();
            await http.suspendResumeWorkflow({...payload, action});
            this.fire('refresh');
            this.setWorkFlowDetail();
        } catch (error) {
            this.data.set('loading', false);
        }
    }

    /**
     *  取消workflow
     */
    async cancelWorkflow() {
        this.data.set('loading', true);
        try {
            const payload = this.getPayload();
            await http.deleteWorkflow(payload);
            this.fire('refresh');
            this.onCancel();
        } catch (error) {
            this.data.set('loading', false);
        }
    }

    /**
     * 关闭弹窗
     */
    onCancel() {
        const phase = this.data.get('workflow.status.phase');
        if (~[WorkFlowStatus.FAILED, WorkFlowStatus.SUCCESSDED].indexOf(phase)) {
            this.fire('refresh');
        }
        this.data.set('open', false);
    }

    async retryMonitorTask() {
        if (this._time) {
            clearTimeout(this._time);
        }
        this.data.set('reseting', true);
        try {
            const {clusterID, workflowID} = this.getPayload();
            await this.$http.suspendResumeWorkflow({
                clusterID,
                workflowID,
                action: 'resume',
            });
            this.setWorkFlowDetail();
            this.data.set('reseting', false);
        } catch (error) {
            this.data.set('reseting', false);
        }
    }

    detach() {
        this.fire('detached');
    }
}
