/**
 * @description 升级Master进度详情列表
 * @file pages/cluster/check-progress/upgrade-list/index
 * <AUTHOR>
 */

import {Component} from 'san';
import moment from 'moment';
import {Button, TimeLine} from '@baidu/sui';
import {OutlinedLoading} from '@baidu/sui-icon';
import {html} from '@baiducloud/runtime';
import {utcToTime} from '@utils/util';
import {WorkFlowStatus} from '@utils/enums';

import UpgradeTable from '../upgrade-table';
import './index.less';
import {TaskGroup, WorkflowTask} from '@types';

const template = html`
    <template>
        <s-timeline class="upgrade-timeline" s-if="timeline">
            <s-timeline-item icon="{{false}}" class="{{status | statusClass}}">
                <div slot="time">{{type}} 升级({{uploadNodeProgress}})</div>
                <div slot="icon">
                    <s-icon-loading
                        s-if="status === WorkFlowStatus.UPGRADING"
                        animation="spin"
                        width="12"
                        color="#108CEE"
                    />
                </div>
                <upgrade-table datasource="{{operate.taskList}}" dataTrackId="{{dataTrackId}}" />
            </s-timeline-item>
            <s-timeline-item
                s-if="postCheck.taskList"
                class="{{postCheck.taskGroupPhase | statusClass}}"
            >
                <div slot="time">升级后检查({{postCheckProgress}})</div>
                <div slot="icon">
                    <s-icon-loading
                        s-if="postCheck.taskGroupPhase === WorkFlowStatus.UPGRADING"
                        animation="spin"
                        width="12"
                        color="#108CEE"
                    />
                </div>
                <upgrade-table datasource="{{postCheck.taskList}}" dataTrackId="{{dataTrackId}}" />
            </s-timeline-item>
        </s-timeline>
        <upgrade-table s-else datasource="{{operate.taskList}}" dataTrackId="{{dataTrackId}}" />
    </template>
`;
export default class UpgradeList extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-timeline': TimeLine,
        's-timeline-item': TimeLine.TimeLineItem,
        'upgrade-table': UpgradeTable,
        's-icon-loading': OutlinedLoading,
    };
    static filters = {
        statusClass(status: string) {
            switch (status) {
                case WorkFlowStatus.FAILED:
                    return 'failed';
                case WorkFlowStatus.SUCCESSDED:
                    return 'success';
                case WorkFlowStatus.UPGRADING:
                    return 'running';
                default:
                    return '';
            }
        },
    };

    static computed = {
        postCheckTime() {
            const postCheck = this.data.get('postCheck');
            if (postCheck.taskGroupPhase === WorkFlowStatus.SUCCESSDED) {
                let startTime = '';
                let finishedTime = '';
                postCheck.taskList?.forEach(i => {
                    if (!startTime) {
                        startTime = i.startTime;
                    }
                    if (!finishedTime) {
                        finishedTime = i.finishedTime;
                    }
                    if (moment(startTime).isBefore(i.startTime)) {
                        startTime = i.startTime;
                    }
                    if (moment(finishedTime).isAfter(i.finishedTime)) {
                        finishedTime = i.finishedTime;
                    }
                });
                return startTime && `${utcToTime(startTime)} 至 ${utcToTime(finishedTime)}`;
            }
        },

        uploadNodeProgress() {
            const operate: TaskGroup = this.data.get('operate');
            const succeed = operate.taskList.filter(
                i => i.workflowTaskPhase === WorkFlowStatus.SUCCESSDED,
            );
            return `${succeed.length}/${operate.taskList.length}`;
        },

        postCheckProgress(): string {
            const postCheckList: WorkflowTask[] = this.data.get('postCheck.taskList') || [];
            const succeed = postCheckList.filter(
                i => i.workflowTaskPhase === WorkFlowStatus.SUCCESSDED,
            );

            return `${succeed.length}/${postCheckList.length || 1}`;
        },
    };

    initData() {
        return {
            operate: {
                taskList: [],
            },
            postCheck: {
                taskList: [],
            },
            WorkFlowStatus,
        };
    }

    inited() {}
}
