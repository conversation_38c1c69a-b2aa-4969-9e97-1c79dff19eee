/**
 * @description 升级进度列表
 * @file pages/cluster/check-progress/upgrade-table
 * <AUTHOR>
 */

import {Component} from 'san';
import {Tooltip, Button, Table, Link} from '@baidu/sui';
import {OutlinedInfo} from '@baidu/sui-icon';
import {utcToTime} from '@utils/util';
import {WorkFlowStatus} from '@utils/enums';
import {html} from '@baiducloud/runtime';

import './index.less';

const template = html`
    <template>
        <div s-ref="content">
            <s-table
                class="upgrade-table"
                columns="{{columns}}"
                datasource="{{datasource}}"
                max-height="{{235}}"
            >
                <div slot="c-taskName">
                    <s-tooltip>
                        <div slot="content">{{row.taskName}}</div>
                        <div class="text-overflow-ellipsis" style="max-width: 135px;">
                            {{row.taskName}}
                        </div>
                    </s-tooltip>
                </div>
                <div slot="c-workflowTaskPhase">
                    <span class="status {{row.workflowTaskPhase | statusClass}}">
                        {{row | statusText}}
                        <!--由于会出现success 并携带errormessage的情况需要过滤判断-->
                        <s-tooltip
                            s-if="row.errorMessage && row.workflowTaskPhase === WorkFlowStatus.FAILED"
                        >
                            <div
                                slot="content"
                                placement="topRight"
                                class="break-word upgrade-error-content"
                            >
                                失败信息: <br />
                                {{row.errorMessage}},请
                                <s-link href="{{ticketUrl}}" skin="primary" class="fix-sui-line">
                                    提交工单
                                </s-link>
                                <span>处理</span>
                            </div>
                            <s-icon-info color="#F39000" class="icon-info-react" />
                        </s-tooltip>
                    </span>
                </div>
                <div slot="error">
                    啊呀，出错了？
                    <a
                        href="javascript:void(0)"
                        on-click="reload"
                        data-track-id="{{dataTrackId}}"
                        data-track-name="重新加载"
                    >
                        重新加载
                    </a>
                </div>
                <span slot="c-startTime"> {{row.startTime | utcToTime}} </span>
                <span slot="c-finishedTime"> {{row.finishedTime | utcToTime}} </span>
            </s-table>
        </div>
    </template>
`;
export default class UpgradeTable extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-tooltip': Tooltip,
        's-table': Table,
        's-link': Link,
        's-icon-info': OutlinedInfo,
    };

    static filters = {
        statusClass(status: string) {
            return WorkFlowStatus.fromValue(status).klass;
        },
        statusText(row) {
            const type = row.workflowTaskType;
            const monitorType = ['UpgradeCCEMonitorCollector', 'UpgradeCPromMonitor'];
            const status = row.workflowTaskPhase;
            if (status === WorkFlowStatus.UPGRADING && monitorType.includes(type)) {
                return '进行中';
            }
            return WorkFlowStatus.getTextFromValue(status);
        },
        utcToTime(time: string) {
            return (time && utcToTime(time)) || '-';
        },
    };

    initData() {
        return {
            columns: [
                {name: 'taskName', label: '事件描述'},
                {name: 'workflowTaskPhase', label: '状态', width: 110},
                {name: 'startTime', label: '开始时间'},
                {
                    name: 'finishedTime',
                    label: '结束时间',
                },
            ],
            WorkFlowStatus,
            datasource: [],
            ticketUrl: window.$context.getDomains().ticket,
        };
    }

    reload() {
        this.dispatch('reloadTable');
    }
}
