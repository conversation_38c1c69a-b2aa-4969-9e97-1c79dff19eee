/**
 * @file components/cluster/create-v2/advanced.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Icon} from '@baiducloud/bce-ui/san';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<template>
    <div class="network-advanced-toggle {{open ? '' : 'network-advanced-toggle-close'}}">
        <div class="network-advanced-title" on-click="onAdvancedToggle">
            <slot name="icon">
                <ui-icon name="downarrow" />
                <span>高级设置</span>
            </slot>
        </div>
    </div>
    <div class="{{open ? '' : 'network-advanced-content-close'}}">
        <slot />
    </div>
</template>`;

@asComponent('@cce-cluster-create-advanced')
@invokeBceSanUI
export default class Advanced extends Component {
    static template = template
    
    static components = {
        'ui-icon': Icon
    }

    initData() {
        return {
            open: false
        };
    }

    onAdvancedToggle() {
        const open = this.data.get('open');
        this.data.set('open', !open);
    }
}
