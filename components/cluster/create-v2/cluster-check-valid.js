import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Tip} from '@baiducloud/bce-ui/san';
import {connect} from 'san-store';

import SeniorDialog from '../../cluster/senior-dialog';
import StoreMap from '../../../pages/cluster/create-v2/store/map';
import tips from '../../../utils/tips';
import {MasterType, NodeType} from '../../cluster/create-v2/enums';

const {asComponent} = decorators;
const checkTip = {
    INSTANCE_QUOTA: tips.cluster.bccQuota,
    SUBNET_AVAILABLE_IP: tips.cluster.ipQuota,
    ACCOUNT_BALANCE: tips.cluster.balance,
    CLUSTER_QUOTA: tips.cluster.clusterQuota,
    CLUSTER_NODE_QUOTA: tips.cluster.nodeQuota,
    REAL_NAME: tips.cluster.realName,
    BLB_QUOTA: tips.cluster.BLBQuota,
    REAL_NAME_AUTHENTICATE: tips.cluster.seniorAuth,
};

const template = html`
    <template class="check-valid-container">
        <ui-tip
            s-if="orderTip"
            layerWidth="188"
            message="{{orderTip}}"
            s-ref="tip"
            skin="warning"
        />
    </template>
`;

class ClusterCheckValidTip extends Component {
    static template = template;

    static components = {
        'ui-tip': Tip,
    };

    initData() {
        return {
            initTipList: [],
            checkTipList: [],
            nodeCheckList: [],
            masterNode: [],
            workerNode: [],
            accountBalance: false,
        };
    }

    static computed = {
        orderTip() {
            const ticketUrl = window.$context.getDomains().ticket;
            const initTipList = this.data.get('initTipList');
            const checkTipList = this.data.get('checkTipList');
            const nodeCheckList = this.data.get('nodeCheckList');
            const customData = _.filter(
                this.data.get('workerNode.customData') || [],
                t => t.serviceType === NodeType.BCC || t.serviceType === NodeType.EBC,
            );
            const accountBalance = this.data.get('accountBalance');
            const isExpand = this.data.get('isExpand');
            let type =
                initTipList[0] || accountBalance || checkTipList[0] || nodeCheckList[0] || '';
            // 忽略余额检验
            if (customData.length === 0 && isExpand) {
                type = nodeCheckList[0] || checkTipList[0] || initTipList[0] || '';
            }
            return checkTip[type]?.replace(/\{\{.+\}\}/, ticketUrl);
        },
    };

    static messages = {
        'layer:click'(e) {
            const key = e.value.target.getAttribute('data-key');
            if (key === 'seniorAuth') {
                this.openDownloadDialog();
            }
        },
    };

    inited() {
        this.initPurchaseValid();
    }

    attached() {
        this.initCheckValid();

        this.watch('orderTip', value => {
            this.fire('valid-change', value);
            this.showLayer();
        });

        this.watch('exposedPublic', () => {
            this.onPublicChange();
        });
        this.watch('masterType', value => {
            this.onBlbCheck(value === MasterType.CUSTOM);
        });
    }

    initCheckValid() {
        const {masterType} = this.data.get('');
        this.onPublicChange();
        this.onBlbCheck(masterType === MasterType.CUSTOM);
        const orderTip = this.data.get('orderTip');
        this.fire('valid-change', orderTip);
        this.showLayer();
    }
    showLayer() {
        const orderTip = this.data.get('orderTip');
        if (orderTip) {
            this.nextTick(() => {
                const ref = this.ref('tip');
                ref && ref.showLayer();
            });
        }
    }

    openDownloadDialog() {
        const dialog = new SeniorDialog();
        dialog.attach(document.body);
    }

    onBlbCheck(value) {
        if (!this.$context.isVerifyUser()) {
            return;
        }
        this.data.remove('checkTipList', 'BLB_QUOTA');
        value &&
            this.$http.commonCheckQuota(this.getCheckParams('BLB_QUOTA')).then(({result}) => {
                if (!result.pass) {
                    this.data.unshift('checkTipList', 'BLB_QUOTA');
                }
            });
    }

    onPublicChange() {
        if (!this.$context.isVerifyUser()) {
            return;
        }
        let exposedPublic = this.data.get('exposedPublic');
        const masterCustom = this.data.get('masterNode.customData') || [];
        const workerCustom = this.data.get('workerNode.customData') || [];
        const customData = _.filter(
            [...masterCustom, ...workerCustom],
            t => t.serviceType === NodeType.BCC || t.serviceType === NodeType.EBC,
        );
        _.each(customData, item => {
            exposedPublic = exposedPublic || item?.eip?.ifBuyEip;
            if (exposedPublic) {
                return;
            }
        });
        this.data.remove('checkTipList', 'REAL_NAME_AUTHENTICATE');
        exposedPublic &&
            this.$http
                .commonCheckQuota(this.getCheckParams('REAL_NAME_AUTHENTICATE'))
                .then(({result}) => {
                    if (!result.pass) {
                        this.data.unshift('checkTipList', 'REAL_NAME_AUTHENTICATE');
                    }
                });
    }

    initPurchaseValid() {
        const realname = this.$context.isVerifyUser();
        this.data.set('initTipList', []);
        !realname && this.data.push('initTipList', 'REAL_NAME');
        const isExpand = this.data.get('isExpand');
        if (isExpand) {
            this.$http.commonCheckQuota(this.getCheckParams('ACCOUNT_BALANCE')).then(({result}) => {
                if (!result.pass) {
                    this.data.set('accountBalance', 'ACCOUNT_BALANCE');
                }
            });
        } else {
            this.$http
                .commonCheckQuota(this.getCheckParams('CLUSTER_QUOTA'))
                .then(({result}) => {
                    if (!result.pass) {
                        this.data.push('initTipList', 'CLUSTER_QUOTA');
                        return Promise.reject();
                    }
                    return this.$http.commonCheckQuota(this.getCheckParams('ACCOUNT_BALANCE'));
                })
                .then(({result}) => {
                    if (!result.pass) {
                        this.data.set('accountBalance', 'ACCOUNT_BALANCE');
                    }
                });
        }
    }

    async onNodeChange({type, data = {}}) {
        if (!this.$context.isVerifyUser()) {
            return;
        }
        this.data.set(`${type}Node`, data);
        await Promise.resolve();
        const masterCustom = this.data.get('masterNode.customData') || [];
        const workerCustom = this.data.get('workerNode.customData') || [];
        const customData = _.filter(
            [...masterCustom, ...workerCustom],
            t => t.serviceType === NodeType.BCC || t.serviceType === NodeType.EBC,
        );
        const subnetIdObj = {};
        this.data.set('nodeCheckList', []);
        customData.map(item => {
            subnetIdObj[item.subnetId] === undefined
                ? (subnetIdObj[item.subnetId] = item.purchaseNum || 1)
                : (subnetIdObj[item.subnetId] += item.purchaseNum || 1);
        });
        this.onPublicChange();
        const reqArr = [];
        Object.keys(subnetIdObj).map(item => {
            reqArr.push(
                new Promise((resolve, reject) => {
                    this.$http
                        .commonCheckQuota({
                            checkType: 'SUBNET_AVAILABLE_IP',
                            checkOptions: {
                                subnetID: item,
                                addNum: subnetIdObj[item] + '',
                            },
                        })
                        .then(result => resolve(result))
                        .catch(err => reject(err));
                }),
            );
        });
        Promise.all(reqArr)
            .then(result => {
                for (let i = 0; i < result.length; i++) {
                    if (!result[i].result.pass) {
                        this.data.unshift('nodeCheckList', 'SUBNET_AVAILABLE_IP');
                        return Promise.reject();
                    }
                }
                const instanceQuotaParams = this.getCheckParams('INSTANCE_QUOTA');
                if (instanceQuotaParams) {
                    return this.$http.commonCheckQuota(instanceQuotaParams);
                } else {
                    return Promise.resolve({
                        result: {pass: true},
                    });
                }
            })
            .then(({result}) => {
                if (!result.pass) {
                    this.data.unshift('nodeCheckList', 'INSTANCE_QUOTA');
                    return Promise.reject();
                }
                return this.$http.commonCheckQuota(this.getCheckParams('CLUSTER_NODE_QUOTA'));
            })
            .then(({result}) => {
                if (!result.pass) {
                    this.data.unshift('nodeCheckList', 'CLUSTER_NODE_QUOTA');
                }
            });
    }

    getCheckParams(checkType) {
        const masterCustom = this.data.get('masterNode.customData') || [];
        const workerCustom = this.data.get('workerNode.customData') || [];
        const workerExist = this.data.get('workerNode.existData') || [];
        const existCount = existData => {
            let count = 0;
            const data = _.filter(
                existData,
                t => t.serviceType === NodeType.BCC || t.serviceType === NodeType.EBC,
            );
            _.each(_.pluck(data, 'instanceList'), item => {
                count += item.length;
            });

            return count;
        };
        const customCount = customData => {
            let count = 0;
            const data = _.filter(
                customData,
                t => t.serviceType === NodeType.BCC || t.serviceType === NodeType.EBC,
            );
            _.each(data, item => {
                count += item.purchaseNum;
            });

            return count;
        };
        const isExpand = this.data.get('isExpand');
        if (checkType === 'INSTANCE_QUOTA') {
            const paymentTiming = workerCustom[0]?.instanceChargingType;
            if (!paymentTiming || paymentTiming === 'Prepaid') {
                // 预付费不做校验
                return null;
            }

            return {
                checkType,
                checkOptions: {
                    paymentTiming,
                    addNum: customCount(masterCustom) + customCount(workerCustom) + '',
                },
            };
        } else if (checkType === 'SUBNET_AVAILABLE_IP') {
        } else if (checkType === 'ACCOUNT_BALANCE') {
            return {
                checkType,
                checkOptions: {},
            };
        } else if (checkType === 'CLUSTER_QUOTA') {
            return {
                checkType,
                checkOptions: {
                    addNum: isExpand ? '0' : '1',
                },
            };
        } else if (checkType === 'CLUSTER_NODE_QUOTA') {
            return {
                checkType,
                checkOptions: {
                    clusterID: isExpand ? this.data.get('route.query.clusterUuid') : '',
                    addNum: customCount(workerCustom) + existCount(workerExist) + '',
                },
            };
        } else if (checkType === 'BLB_QUOTA') {
            return {
                checkType,
                checkOptions: {
                    addNum: 1 + '',
                },
            };
        } else if (checkType === 'REAL_NAME_AUTHENTICATE') {
            return {
                checkType,
                checkOptions: {
                    level: 'senior',
                },
            };
        }
    }
}

@asComponent('@cce-cluster-check-valid')
export default class ClusterCheckValid extends connect.san(StoreMap)(ClusterCheckValidTip) {}
