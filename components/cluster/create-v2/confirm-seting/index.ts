/**
 * @description 配置确认列表
 * @file pages/cluster/create-v2/confirm-seting.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {Table, Popover} from '@baidu/sui';
import {html} from '@baiducloud/runtime';
import {math, moneyUtil} from '@baiducloud/billing-sdk';
import {connect} from 'san-store';

import {NetworkMode, MasterType} from '@components/cluster/create-v2/enums';
import decorators from '@utils/decorators';
import StoreMap from '@pages/cluster/create-v2/store/map';
import {waitActionDialog} from '@common/biz/helper';
import {convertPriceConfig} from '@utils/cluster-helper';

import {PayType, WorkFlowType, KubeProxyType} from '@utils/enums';

import NodeView from './node-view';
import ShoppingCart from './shopping-cart';

import './index.less';

const {invokeBceSanUI, asComponent, invokeComp, invokeAppComp} = decorators;
const moneyUtilDefault = moneyUtil.default;

const template = html`
    <section class="cce-create-confirm-seting">
        <ui-biz-legend label="集群配置">
            <s-table
                class="mt10"
                loading="{{loading}}"
                columns="{{columns}}"
                datasource="{{datasource}}"
            >
                <div slot="c-config">
                    <node-view s-if="row._isNode" item="{{row.config}}" payConfig="{{payConfig}}" />
                    <div s-if="row._isExistNode">
                        <label class="grey-color">实例ID：</label>
                        <ul slot="c-existNode" class="flex-center flex-wrap">
                            <li s-for="node in row.config" class="w-1/2">
                                <a
                                    href="/bcc/#/bcc/instance/detail?instanceId={{node.id}}&id={{node.instanceId}}"
                                    target="_blank"
                                >
                                    {{node.instanceId}}
                                </a>
                                <span class="grey-color"> ({{node.internalIp}}) </span>
                            </li>
                        </ul>
                    </div>
                    <app-detail-cell
                        datasource="{{row.config}}"
                        divide="{{1}}"
                        s-if="!row._isExistNode && !row._isNode"
                    >
                        <div slot="c-eniSubnet">
                            <s-popover s-if="item.value.length > 3">
                                <span class="primary-color">共{{item.value.length}}个</span>
                                <ul slot="content">
                                    <li s-for="subnet in item.value">{{subnet.cidr}}</li>
                                </ul>
                            </s-popover>
                            <ul s-else>
                                <li s-for="subnet in item.value">{{subnet.cidr}}</li>
                            </ul>
                        </div>
                    </app-detail-cell>
                </div>
                <div slot="c-price">
                    <shopping-cart
                        class="cce-create-confirm-price"
                        s-if="row._priceType === 'BCC' || row._priceType === 'BBC'"
                        queryBccPrice="{{row.price}}"
                        publicNetwork="{{formData.cluster.masterConfig.exposedPublic}}"
                        payConfig="{{payConfig}}"
                    />
                    <shopping-cart
                        class="cce-create-confirm-price"
                        s-if="row._priceType === 'BLB'"
                        queryBlbPrice="{{row.price}}"
                        publicNetwork="{{formData.cluster.masterConfig.exposedPublic}}"
                        payConfig="{{payConfig}}"
                    />
                    <span s-if="!row._priceType"> {{row.price}} </span>
                </div>
            </s-table>
        </ui-biz-legend>

        <ui-biz-legend label="前置检查" class="bordered">
            <cce-create-precheck
                tip="温馨提示：请等待创建集群前置检查项全部结束，待确认状态的检查项需要确认后才能创建集群"
                clusterUuid="{{route.query.clusterUuid}}"
                payload="{{precheckPayload}}"
            />
        </ui-biz-legend>
    </section>
`;

class ConfirmSeting extends Component {
    static template = template;

    static components = {
        's-table': Table,
        'node-view': NodeView,
        's-popover': Popover,
        'shopping-cart': ShoppingCart,
    };

    static filters = {
        getPriceExtra(money, unit = '') {
            if (money <= 0) {
                return '';
            }

            const moneyDay = moneyUtilDefault.showMoney(money * 1440);
            const moneyMonth = moneyUtilDefault.showMoney(money * 30 * 1440);
            return `预计￥${moneyDay}/天${unit}，￥${moneyMonth}/月${unit}`;
        },
    };

    initData() {
        return {
            columns: [
                {name: 'name', label: '产品类型', width: '15%'},
                {name: 'config', label: '产品配置', width: '380px'},
                {name: 'num', label: '数量', width: '8%'},
                {name: 'pay', label: '付款方式', width: '8%'},
                {name: 'duration', label: '购买时长', width: '8%'},
                {name: 'price', label: '配置费用'},
            ],
            datasource: [],
            loading: false,
        };
    }

    async inited() {
        const formData = _.cloneDeep(this.data.get('formData') || {});
        if (!formData.cluster) {
            return;
        }
        const ResourceChargingOption = formData.cluster.ResourceChargingOption || {};
        const {chargingType, purchaseTime, purchaseTimeUnit} = ResourceChargingOption;
        const clusterChargingOption = {
            chargingType: chargingType,
        };
        if (chargingType === 'Prepaid') {
            clusterChargingOption.purchaseTime = purchaseTime;
            clusterChargingOption.purchaseTimeUnit = purchaseTimeUnit;
        }
        formData.cluster.clusterChargingOption = clusterChargingOption;

        const precheckPayload = {
            workflowType: WorkFlowType.CREATECLUSTERPRECHECK,
            workflowConfig: {
                CreateClusterPrecheckWorkflowConfig: {
                    ...formData,
                },
            },
        };

        if (chargingType === 'Prepaid') {
            let totalCost = 0;
            const {queryBccPrice, queryBlbPrice} = this.data.get();
            totalCost = math.safeAdd(totalCost, queryBccPrice?.basePrice?.money || 0);
            totalCost = math.safeAdd(totalCost, queryBccPrice?.eipBandwidthPrice?.money || 0);
            totalCost = math.safeAdd(totalCost, queryBccPrice?.bbcPrice?.money || 0);
            totalCost = math.safeAdd(totalCost, queryBlbPrice?.eipBandwidthPrice?.money || 0);
            precheckPayload.workflowConfig.CreateClusterPrecheckWorkflowConfig.totalCost =
                totalCost;
            precheckPayload.workflowConfig.CreateClusterPrecheckWorkflowConfig.clusterCost = `${totalCost}`;
            precheckPayload.workflowConfig.CreateClusterPrecheckWorkflowConfig.confirmed = false;
        }
        this.data.set('precheckPayload', precheckPayload);

        try {
            this.data.set('loading', true);
            const cce = await this.setCCE(formData.cluster);
            const blb = await this.setBLB(formData.cluster);
            const master = await this.setMaster(formData.cluster);
            const worker = await this.setWorker();
            this.data.set('datasource', [cce, blb, ...master, ...worker]);
        } catch (e) {}
        this.data.set('loading', false);
    }

    async setCCE(cluster) {
        const config = [
            this.getCell('name', '集群名称：', cluster.clusterName),
            this.getCell('ipv6', 'IPv6双线：', this.data.get('IPversion') ? '开启' : '关闭'),
            this.getCell('k8sVersion', 'Kubernetes版本：', cluster.k8sVersion),
            this.getCell(
                'runtimeVersion',
                '容器运行时：',
                `${cluster.runtimeType?.replace(/^\S/, s => s.toUpperCase())} ${
                    cluster.runtimeVersion
                }`,
            ),
            this.getCell('vpc', 'VPC网络：', this.getVpc(cluster.vpcID)),
            this.getCell(
                'networkMode',
                '容器网络模式：',
                NetworkMode.getTextFromValue(this.data.get('networkMode')),
            ),
        ];
        const netowrk = await this.setNetwork(cluster);
        return {
            name: '容器引擎CCE',
            num: 1,
            price: '免费',
            duration: '无',
            pay: '无',
            config: config.concat(netowrk).filter(i => i && i.key),
        };
    }

    async setNetwork(cluster) {
        const networkConfig = cluster.containerNetworkConfig;
        const selectedVpcItem = this.data.get('selectedVpcItem');

        const isAutoDetect = networkConfig.mode === NetworkMode.AUTO_DETECT;
        const isCni = networkConfig.mode === NetworkMode.CNI;
        const isHybrid = networkConfig.mode === NetworkMode.VPC_HYBRID;

        let data = [];
        if (isAutoDetect) {
            const options = this.data.get('formData.options');
            const {maxNodeNum} = await this.$http.checkContainerNetworkCidr({
                vpcID: cluster.vpcID,
                vpcCIDR: selectedVpcItem.cidr,
                containerCIDR: networkConfig.clusterPodCIDR,
                clusterIPCIDR: networkConfig.clusterIPServiceCIDR,
                maxPodsPerNode: networkConfig.maxPodsPerNode,
                skipContainerCIDRCheck: options?.skipNetworkCheck,
            });
            data.push(
                this.getCell(
                    'networkMode',
                    '容器网段：',
                    html`${networkConfig.clusterPodCIDR}(节点Pod上限
                        <span class="c-y-1">${networkConfig.maxPodsPerNode}</span>
                        集群节点上限<span class="c-y-1">${maxNodeNum}</span> )`,
                ),
            );
        } else if (isCni) {
            const eniSubNetList = this.data.get('eniSubNetList');
            const zoneList = Object.keys(networkConfig.eniVPCSubnetIDs);
            let subnetList: any[] = [];
            zoneList.forEach(zone => {
                const shortIdList = networkConfig.eniVPCSubnetIDs[zone];
                shortIdList.forEach(shortId => {
                    const subnet = eniSubNetList.filter(i => i.shortId === shortId)[0];
                    subnet && subnetList.push(subnet);
                });
            });
            data.push(this.getCell('eniSubnet', '容器网段：', subnetList));
        } else if (isHybrid) {
        }
        const cidr = networkConfig.clusterIPServiceCIDR || '';
        const mask = +(cidr.split('/')[1] || '32');
        const clusterMaxServiceNum = Math.pow(2, 32 - mask);
        return data.concat([
            this.getCell(
                'clusterPNetwork',
                'ClusterIP网段：',
                html`${networkConfig.clusterIPServiceCIDR}(集群Service上限
                    <span class="c-y-1">${clusterMaxServiceNum}</span>)`,
            ),
            this.getCell(
                'nodePort',
                'NodePort范围：',
                `${networkConfig.nodePortRangeMin}-${networkConfig.nodePortRangeMax}`,
            ),
            this.getCell(
                'kuboProxy',
                'Kube-proxy模式：',
                KubeProxyType.getTextFromValue(networkConfig.kubeProxyMode),
            ),
            ...[
                networkConfig.enableNodeLocalDNS
                    ? this.getCell('localDNS', 'NodeLocalDNS：', networkConfig.nodeLocalDNSAddr)
                    : {},
            ],
        ]);
    }

    getVpc(vpcID) {
        const vpcList = this.data.get('vpcList');
        const vpc = vpcList.find(i => i.shortId === vpcID);
        return vpc.text;
    }

    getSubnet(subnetId) {
        const subNetList = this.data.get('subNetList');
        return subNetList.find(i => i.value === subnetId);
    }

    async setBLB(cluster) {
        const blbSubnet = this.getSubnet(cluster?.masterConfig?.clusterBLBVPCSubnetID);
        const price = this.data.get('queryBlbPrice');
        const config = [
            this.getCell('BLBSubnet', 'BLB子网：', `${blbSubnet?.title}`),
            this.getCell(
                'exposedPublic',
                '公网访问：',
                cluster.masterConfig.exposedPublic ? '开启' : '关闭',
            ),
        ];
        return {
            _priceType: 'BLB',
            name: '负载均衡BLB-API Server',
            num: 1,
            price: price,
            duration: '无',
            pay: PayType.getTextFromAlias('POSTPAY'),
            config,
        };
    }

    async setMaster(cluster) {
        if (cluster.masterConfig.masterType === MasterType.MANAGEDPRO) {
            return [];
        }
        const masterNodeList = this.data.get('masterNodeList');
        const nodeList = await this.setNode(masterNodeList?.customData, 'master');
        const existNodeList = await this.setExistNode(masterNodeList?.existData, 'master');
        return [...nodeList, ...existNodeList];
    }

    async setWorker() {
        const workerNodeList = this.data.get('workerNodeList');
        const nodeList = await this.setNode(workerNodeList?.customData, 'worker');
        const existNodeList = await this.setExistNode(workerNodeList?.existData, 'worker');
        return [...nodeList, ...existNodeList];
    }

    async setNode(nodeList, type) {
        if (!nodeList) {
            return [];
        }
        const priceList = nodeList.map(async node => {
            let price;
            const payConfig = this.data.get('payConfig') || {};
            if (node.serviceType === 'BCC') {
                const params = convertPriceConfig([{value: node}], payConfig);
                const {result} = await this.$http.queryBccPrice(params);
                price = result;
            } else if (node.serviceType === 'BBC') {
                // const {result} = await this.$http.queryCpt1Price({
                //     configs: [
                //         {
                //             serviceType: node.serviceType,
                //             count: node.purchaseNum || 1,
                //             flavor: [{name: node.bbc.flavorId, value: 1, scale: 1}],
                //         },
                //     ],
                // });
                // let bbcPrice = 0;
                // _.each(result || [], r => (bbcPrice += r.price || 0));
                // price = {
                //     bbcPrice: {money: bbcPrice},
                // };

                const config = {
                    serviceType: node.serviceType,
                    count: node.purchaseNum || 1,
                    flavor: [
                        {
                            name: node.bbc.flavorId,
                            value: payConfig.payType === 'prepay' ? 'default' : 1,
                            scale: 1,
                        },
                    ],
                };
                const region = window.$context.getCurrentRegion()?.id;
                region && (config.region = region);
                if (payConfig.payType === 'prepay') {
                    config.duration = payConfig.duration;
                    config.timeUnit = 'MONTH';
                    config.type = 'NEW';
                    config.unitText = '台';
                }
                let data = null;
                if (payConfig.payType === 'prepay') {
                    data = await this.$http.queryCpt2Price({configs: [config]});
                } else {
                    data = await this.$http.queryCpt1Price({configs: [config]});
                }
                price = {
                    bbcPrice: data?.result,
                };
            }
            const payText =
                payConfig?.payType === 'prepay'
                    ? '预付费'
                    : PayType.getTextFromValue(node.productType);
            const duration = payConfig?.payType === 'prepay' ? payConfig?.duration : '无';
            let durationText =
                duration === '无' ? '无' : duration > 9 ? duration / 12 + '年' : duration + '个月';
            // 获取价格
            return {
                name: `云服务器${node.serviceType}-${type === 'master' ? 'Master' : 'Worker'}`,
                num: node.purchaseNum,
                duration: durationText,
                pay: payText,
                config: node,
                price,
                _isNode: true,
                _priceType: node.serviceType,
            };
        });
        const dataList = await Promise.all(priceList);
        return dataList;
    }

    async setExistNode(instanceList, type: string) {
        if (!instanceList) {
            return [];
        }
        return instanceList.map(instance => {
            return {
                name: `云服务器${instance.nodeType}-${type === 'master' ? 'Master' : 'Worker'}`,
                config: instance.instanceList,
                num: instance.instanceList.length,
                _isExistNode: true,
            };
        });
    }

    getCell(key, label, value) {
        if (!value) {
            return '';
        }
        return {key, label, value: typeof value === 'function' ? value() : value, slot: key};
    }
}
@invokeComp('@app-detail-cell', '@cce-cluster-v2-shopping-cart', '@cce-create-precheck')
@invokeBceSanUI
@invokeAppComp
@asComponent('@cce-cluster-create-confirm-seting')
export default class ConfirmSetingStore extends connect.san(StoreMap)(ConfirmSeting) {}
