/**
 * @description
 * @file
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Table, Popover} from '@baidu/sui';
import {Arch, Classify, DiskType} from '@baidu/bce-bcc-sdk-enum';
import {html} from '@baiducloud/runtime';
import {NodeType, UserPassType, EipType, BecNodeType, BbcFlavorType} from '../enums';
import {PayType} from '@utils/enums';
import './node-view.less';

const template = html`
    <div class="cluster-create-node-item cluster-create-node-view mb10">
        <div class="cluster-create-custom-item-list">
            <div>
                <label class="item-label">节点子网：</label>
                <span class="item-content">{{item.selectedSubnetItem.text}}</span>
            </div>
            <div s-if="item.bcc && item.bcc.selectedFlavor || item.bbc && item.bbc.flavor">
                <label class="item-label">实例规格：</label>
                <span class="item-content" s-if="item.bcc && item.bcc.selectedFlavor">
                    {{item.bcc.selectedFlavor.spec}} （{{item.bcc.cpu}}核 {{item.bcc.memory}}GB）
                </span>
                <span class="item-content" s-if="item.bbc && item.bbc.flavor">
                    {{item.bbc.flavorId}} （{{item.bbc.flavor.cpu}}核 {{item.bbc.flavor.memory}}GB）
                </span>
            </div>
            <div>
                <label class="item-label">操作系统：</label>
                <span class="item-content">{{item | osText}}</span>
            </div>
            <div s-if="item.cds">
                <label class="item-label">系统盘：</label>
                <span class="item-content" s-if="item.cds.rootDiskStorageType">
                    {{item.cds.rootDiskStorageType | rootDiskStorageTypeText}}&nbsp;
                    {{item.cds.rootDiskSizeInGb}}GB
                </span>
                <span class="item-content" s-elif="item.cds.sysDiskSize">
                    {{item.cds.sysDiskSize}}GB
                </span>
            </div>
            <div s-if="item.cds.ebsSize && item.cds.ebsSize.length">
                <label class="item-label">数据盘：</label>
                <span class="item-content">
                    <s-popover s-if="item.cds.ebsSize.length > 3">
                        <span class="primary-color">共{{item.cds.ebsSize.length}}个</span>
                        <ul slot="content">
                            <li s-for="cds in item.cds.ebsSize">{{cds | ebsSizeText}}</li>
                        </ul>
                    </s-popover>
                    <span s-else> {{item.cds.ebsSize | ebsSizeText}} </span>
                </span>
            </div>
            <div class="item-col" s-if="item.eip && item.eip.ifBuyEip === '1'">
                <label class="item-label">公网IP：</label>
                <span class="item-content"> {{item.eip | eipText}} </span>
            </div>
        </div>
    </div>
`;
export default class PopverConfirm extends Component {
    static template = template;
    static components = {
        's-table': Table,
        's-popover': Popover,
    };

    static filters = {
        nodeTypeText(value) {
            return NodeType.getTextFromValue(value) || BecNodeType.getTextFromValue(value) || value;
        },
        rebuildText(value) {
            return value ? '是' : '否';
        },
        productTypeText(value) {
            return PayType.getTextFromValue(value);
        },
        logicalZoneText(value) {
            return window.$zone.getLabel(value);
        },
        frameworkText(value) {
            return Arch.getTextFromValue(value);
        },
        classifyText(value) {
            return Classify.getTextFromValue(value);
        },
        rootDiskStorageTypeText(value) {
            return DiskType.getTextFromValue(value);
        },
        ebsSizeText(value) {
            let text = '';
            if (_.isArray(value)) {
                _.each(value, (item, index) => {
                    text += DiskType.getTextFromValue(item.storageType) + ' ' + item.size + 'GB';
                    if (index !== value.length - 1) {
                        text += ',';
                    }
                });
                return text;
            } else {
                return `${DiskType.getTextFromValue(value.storageType)} ${value.size}GB`;
            }
        },
        eipText(value) {
            if (_.isEmpty(value)) {
                return '';
            }
            let type = null;
            const payConfig = this.data.get('payConfig');
            if (payConfig?.payType === 'prepay') {
                type = EipType.getTextFromValue(EipType.PREPAY);
            } else {
                if (value?.ifBuyEip === '1') {
                    if (value.subProductType) {
                        type = EipType.getTextFromValue(value.subProductType);
                    }
                } else {
                    return '-';
                }
            }
            return type + '（' + (value.bandwidthInMbps || '-') + 'Mbps）';
        },
        adminPassTypeText(value) {
            return UserPassType.getTextFromValue(value);
        },
        typeText(value) {
            return BbcFlavorType.getTextFromValue(value);
        },
        osText(item) {
            const os = item?.bcc || item?.bbc || {};
            return `${os.osName} ${os.osVersion}`;
        },
    };

    static computed = {};

    initData() {
        return {
            bbcCustomTable: {
                schema: [
                    {name: 'id', label: '套餐'},
                    {name: 'cpu', label: 'CPU'},
                    {name: 'memory', label: '内存'},
                    {name: 'disk', label: '磁盘'},
                    {name: 'cpuGhz', label: '处理器主频'},
                ],
                cellBuilder: (row, key) => row[key] || '-',
            },
        };
    }

    inited() {}
}
