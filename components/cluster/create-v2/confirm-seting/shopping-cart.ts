/**
 * @description
 * @file
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {math} from '@baiducloud/billing-sdk';

const {invokeBceSanUI} = decorators;

@invokeBceSanUI
export default class ConfirmSetingShoppingCart extends Component {
    static template = html`
        <div class="cce-cluster-v2-shopping-cart" s-if="showBlb || showBcc || showBbc">
            <!--配置费用-->
            <div class="prices-wrapper">
                <div class="price-item" s-if="showBlb">
                    <div class="price-item-content-wrap">
                        <span class="price-item-content"> {{blbPrice}} </span>
                        <span class="price-item-content"> {{blbBandWithPrice}} </span>
                    </div>
                </div>
                <div class="price-item" s-if="showBcc || showBbc">
                    <div class="price-item-content-wrap">
                        <span class="price-item-content" s-if="showBcc"> {{bccPrice}} </span>
                        <span class="price-item-content" s-if="showBandWidthPrice">
                            {{bandwidthPrice}}
                        </span>
                        <span class="price-item-content" s-if="showTrafficPrice">
                            {{trafficPrice}}
                        </span>
                        <span class="price-item-content" s-if="showBbc"> {{bbcPrice}} </span>
                    </div>
                </div>
            </div>
        </div>
    `;

    static computed = {
        showBlb(): boolean {
            return !!this.data.get('queryBlbPrice.basePrice.money');
        },
        blbPrice(): string {
            return `￥${this.data.get('queryBlbPrice.basePrice.money')}/分钟`;
        },
        blbBandWithPrice(): string {
            const unit = this.data.get('payConfig.payType') === 'prepay' ? '' : '/分钟';
            return `￥${this.data.get('queryBlbPrice.eipBandwidthPrice.money')}${unit}`;
        },
        showBcc(): boolean {
            const queryBccPrice = this.data.get('queryBccPrice');
            return !!queryBccPrice?.basePrice?.money;
        },
        showBbc(): boolean {
            const queryBccPrice = this.data.get('queryBccPrice');
            return !!queryBccPrice?.bbcPrice?.length;
        },
        bccPrice(): string {
            const payType = this.data.get('payConfig.payType');
            const unit = payType === 'prepay' ? '' : '/分钟/个';
            return `￥${this.data.get('queryBccPrice.basePrice.money')}${unit}`;
        },
        bbcPrice(): string {
            const payType = this.data.get('payConfig.payType');
            const price = this.data
                .get('queryBccPrice.bbcPrice')
                ?.reduce((t, c) => math.safeAdd(t, c?.price || 0), 0);
            const unit = payType === 'prepay' ? '' : '/月';
            return `￥${price}${unit}`;
        },
        showBandWidthPrice(): boolean {
            const queryBccPrice = this.data.get('queryBccPrice');
            return !!queryBccPrice?.eipBandwidthPrice?.money;
        },
        bandwidthPrice(): string {
            const queryBccPrice = this.data.get('queryBccPrice');
            const payType = this.data.get('payConfig.payType');
            const unit = payType === 'prepay' ? '' : '/分钟';
            return `￥${queryBccPrice?.eipBandwidthPrice?.money}${unit}`;
        },
        showTrafficPrice(): boolean {
            const queryBccPrice = this.data.get('queryBccPrice');
            return !!queryBccPrice?.eipTrafficPrice?.money;
        },
        trafficPrice(): string {
            const queryBccPrice = this.data.get('queryBccPrice');
            return `￥${queryBccPrice?.eipTrafficPrice?.money}/G`;
        },
    };
}
