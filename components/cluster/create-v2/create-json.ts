/**
 * 自定义参数创建集群
 *
 * @file components/cluster/create-v2/create-json.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {ACEEditor} from '@baiducloud/bce-ui/san';
import {Button} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {html} from '@baiducloud/runtime';

import tips from '@utils/tips';

/* eslint-disable */
const template = html`<div class="cce-cluster-v2-create-json-dialog">
    <div class="create-json-tip" s-if="!isUpgradeGroup">
        根据界面已选配置生成API参数，可自定义参数并提交
        <span s-if="isExpand">扩容集群</span>
        <span s-elif="isUpgrade">升级节点</span>
        <span s-elif="isCreatGroup">创建节点组</span>
        <span s-else>创建集群</span>。
        每次打开编辑器都将重新生成参数，若需保存已编辑内容，请复制到剪切板。
        <a s-if="!isUpgrade && !isCreatGroup" href="${tips.doc.k8sApi}" target="_blank">
            使用API参数创建CCE K8S集群
        </a>
    </div>
    <div class="create-json-tip" s-if="isUpgradeGroup">
        根据界面已选配置生成 API
        参数，可自定义参数并提交创建节点升级。每次打开编辑器都将重新生成参数，若需保存已编辑内容，请复制到剪切板。
        <!--
        <a>使用 API 参数创建节点组升级</a>
        -->
    </div>
    <s-clipboard text="{{value}}">
        <s-button>复制</s-button>
    </s-clipboard>
    <ace-editor
        mode="ace/mode/json"
        theme="ace/theme/twilight"
        show-gutter="{{false}}"
        height="{{height || 400}}"
        wrap="free"
        value="{=value=}"
    />
    <div class="cce-cluster-json-cni-tip" s-if="tip">{{tip}}</div>
</div>`;
/* eslint-enable */

export default class CreateJson extends Component {
    static template = template;

    static components = {
        'ace-editor': ACEEditor,
        's-clipboard': ClipBoard,
        's-button': Button,
    };

    attached() {
        let formData = this.data.get('formData');
        try {
            this.data.set('value', JSON.stringify(formData, null, '\t'));
        } catch {
            /* eslint-disable */
        }
        /* eslint-enable */
    }

    getFormData() {
        return this.data.get('value');
    }

    doSubmit() {
        return Promise.reject(this.data.get('value'));
    }
}
