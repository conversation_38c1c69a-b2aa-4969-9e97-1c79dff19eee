import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <ui-form>
            <ui-form-item
                inline
                label="kubelet数据目录："
                help="volume文件、plugin文件等数据存储目录；若已挂载数据盘，建议存储到数据盘"
                required
            >
                <ui-text-box value="{=formData.kubeletRootDir=}" />
                <div s-if="errorMsg" class="error-msg error-msg-scroll">{{errorMsg}}</div>
            </ui-form-item>

            <ui-form-item
                inline
                label="容器数据目录："
                help="容器、镜像等数据存储目录；若已挂载数据盘，建议存储到数据盘"
            >
                <ui-text-box value="{=formData.dataRoot=}" />
            </ui-form-item>
        </ui-form>
    </template>
`;

@asComponent('@cce-data-root')
@invokeBceSanUI
export default class DataRoot extends Component {
    static template = template;

    static computed = {
        errorMsg() {
            const kubeletRootDir = this.data.get('formData.kubeletRootDir');
            if (!kubeletRootDir) {
                return '请输入kubelete数据目录';
            }
            if (kubeletRootDir.length > 31) {
                return '长度超出限制';
            }
            return '';
        },
    };

    initData() {
        return {
            formData: {
                kubeletRootDir: '/var/lib/kubelet',
                dataRoot: '/home/<USER>/docker',
            },
        };
    }

    attached() {
        const runtimeType = this.data.get('runtimeType');
        const fn = value => {
            const dataRoot = value?.includes('docker')
                ? '/home/<USER>/docker'
                : '/home/<USER>/containerd';
            // 编辑的时候优先使用外面传进来的
            const editDataRoot = value?.includes('docker')
                ? this.data.get('formData.dockerConfig.dockerDataRoot')
                : this.data.get('formData.containerdConfig.dataRoot');
            this.data.set('formData.dataRoot', editDataRoot || dataRoot);
        };
        fn(runtimeType);
        this.watch('runtimeType', value => {
            fn(value);
        });
    }

    getFormData() {
        return this.data.get('formData');
    }

    validateForm() {
        const kubeletRootDir = this.data.get('formData.kubeletRootDir');
        if (!kubeletRootDir) {
            return Promise.reject('请输入kubelete数据目录');
        }
        if (kubeletRootDir.length > 31) {
            return Promise.reject('长度超出限制');
        }
        return Promise.resolve();
    }
}
