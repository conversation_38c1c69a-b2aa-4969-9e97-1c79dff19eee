/**
 * @file components/cluster/create-v2/edge-autonomy.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import { Switch, Tooltip } from '@baidu/sui';
import { OutlinedQuestionSquare } from '@baidu/sui-icon';

const {asComponent} = decorators;

const template = html`<template>
    <s-switch checked="{=value=}" on-change="changeEdgeAutonomyStatus" />
    <s-tooltip placement="right">
        <s-icon-question is-button="{{false}}" color="#999"/>
        <div slot="content" style="width: 230px;">
            开启边缘自治，该工作负载创建的容器组，当其所在的边缘节点与云端断连时，仍然能够保证成功调度和正常运行。
        </div>
    </s-tooltip>
</template>`;


@asComponent('@cee-edge-autonomy')
export default class EdgeAutonomy extends Component {
    static template = template;

    static components = {
        's-switch': Switch,
        's-tooltip': Tooltip,
        's-icon-question': OutlinedQuestionSquare
    }

    // 修改边缘自治开关状态
    changeEdgeAutonomyStatus({value}) {
        const {type, namespace, name, clusterUuid} = this.data.get();
        const payload = {
            type, 
            namespace, 
            name, 
            clusterUuid,
            isEdgeAutonomy: value
        }
        this.$http.changeDetialEdgeAutonomyStatus(payload);
    }
}