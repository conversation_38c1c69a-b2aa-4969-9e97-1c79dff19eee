/**
 * @file CCE 枚举配置文件
 *
 * <AUTHOR>
 */

import {Enum} from '@baiducloud/runtime';

export const BccInstanceType = new Enum(
    {alias: 'CPU', text: 'CPU节点', name: 'CPU节点', value: 'common'},
    {alias: 'GPU', text: 'GPU节点', name: 'GPU节点', value: 'gpuBccImage'},
    {alias: 'KUNLUN', text: '百度昆仑', name: '百度昆仑', value: 'kunlun'},
);

/**
 * 物理服务器套餐类型配置
 *
 * @type {Object}
 */
export let BbcFlavorType = new Enum(
    {alias: 'IO', text: 'IO优化型', value: 'io', arch: 'x86'},
    {alias: 'STORAGE', text: '大数据机型', value: 'storage', arch: 'x86'},
    {alias: 'GPU', text: 'GPU型', value: 'gpu', arch: 'isomerism'},
    {alias: 'FPGA', text: 'FPGA型', value: 'fpga', arch: 'isomerism'},
    {alias: 'GENERAL', text: '通用型', value: 'general', arch: 'x86'},
    {alias: 'HPC', text: '高性能计算型', value: 'hpc', arch: 'isomerism'},
);

export const NodeType = new Enum(
    {
        alias: 'BCC',
        text: '云服务器BCC',
        value: 'BCC',
        instance: BccInstanceType,
        type: 'existed_bcc',
    },
    {
        alias: 'EBC',
        text: '弹性裸金属服务器EBC',
        value: 'EBC',
        instance: BccInstanceType,
        type: 'existed_ebc',
    },
    {
        alias: 'BBC',
        text: '裸金属服务器BBC',
        value: 'BBC',
        instance: BbcFlavorType,
        type: 'existed_bbc',
    },
);

export const ExistNodeType = new Enum(
    {
        alias: 'BCC',
        text: '云服务器BCC',
        value: 'BCC',
        instance: BccInstanceType,
        type: 'existed_bcc',
    },
    {
        alias: 'EBC',
        text: '弹性裸金属服务器EBC',
        value: 'EBC',
        instance: BccInstanceType,
        type: 'existed_ebc',
    },
    {
        alias: 'BBC',
        text: '裸金属服务器BBC',
        value: 'BBC',
        instance: BbcFlavorType,
        type: 'existed_bbc',
    },
);

export const ExistHPASNodeType = new Enum({
    alias: 'HPAS',
    text: '高性能应用服务HPAS',
    value: 'HPAS',
    instance: BccInstanceType,
    type: 'existed_hpas',
});

export const FlavorType = new Enum(
    {alias: 'IO', text: 'IO优化型', value: 'io'},
    {alias: 'STORAGE', text: '大数据机型', value: 'storage'},
    {alias: 'GPU', text: 'GPU型', value: 'gpu'},
    {alias: 'FPGA', text: 'FPGA型', value: 'fpga'},
    {alias: 'GENERAL', text: '通用型', value: 'general'},
);

export const BbcImageType = new Enum(
    {alias: 'COMMON', text: '公共镜像', value: 'bbcCommon'},
    {alias: 'CUSTOM', text: '自定义镜像', value: 'bbcCustom'},
    {alias: 'SHARING', text: '共享镜像', value: 'bbcSharing'},
);

export const ClusterHA = new Enum(
    {
        alias: 'HA1',
        text: '1副本',
        value: 1,
        message:
            '单副本Master存在单点故障风险，若业务可靠性要求较高，建议选择多副本以保证Master的高可用。',
    },
    {alias: 'HA3', text: '3副本', value: 3},
    {alias: 'HA5', text: '5副本', value: 5},
);

export const UserPassType = new Enum(
    {alias: 'CUSTOM', text: '设置密码', value: 'custom'},
    {alias: 'RANDOM', text: '随机生成', value: 'random'},
    {alias: 'KEYPAIR', text: '密钥对', value: 'keypair'},
);

export const newUserPassType = new Enum(
    {alias: 'CUSTOM', text: '用户自定义', value: 'custom'},
    {alias: 'RANDOM', text: '创建后设置', value: 'random'},
    {alias: 'KEYPAIR', text: '密钥对', value: 'keypair'},
);

export const EipLineTypeList = {
    BGP: '标准型BGP',
    BGP_S: '增强型BGP',
    Static: '静态BGP',
    ChinaMobile: '移动单线',
    ChinaTelcom: '电信单线',
    ChinaUnicom: '联通单线',
    BackToOrigin: '回源IP',
    Custom: '定制线路',
    BGP_D: '高防BGP',
};

export let EIPSUBPRODUCTTYPE = {
    ByTraffic: '按流量计费',
    ByBandwidth: '按带宽计费',
};

export const NetworkMode = new Enum(
    {
        alias: 'CNI',
        text: 'VPC-ENI',
        value: 'vpc-secondary-ip-veth',
        desc: '基于弹性网卡实现的容器网络模式，容器网络与云主机网络在同一个 VPC 内',
    },
    {
        alias: 'AUTO_DETECT',
        text: 'VPC路由',
        value: 'vpc-route-veth',
        desc: '基于 VPC 路由实现的容器网络模式，可设置独立平行于 VPC 的容器网段，且不支持1000节点及以上集群规模',
    },
);

export const BecNetworkMode = new Enum({
    alias: 'OVERLAY',
    text: 'Overlay',
    value: 'overlay',
    desc: '基于VXLAN隧道协议和eBPF，是一种在VPC网络之上构建逻辑容器网络的技术，具有扩展性好、安全性高的特点',
});

export const EniNetworkMode = new Enum(
    {alias: 'SHARE', text: '共享', value: 'vpc-secondary-ip-veth'},
    {alias: 'EXCLUSIVE', text: '独占', value: 'vpc-exclusive-eni'},
);

export const CloudNetworkMode = new Enum({
    alias: 'AUTO_DETECT',
    text: '边缘容器网络',
    value: 'vpc-route-auto-detect',
});

export const NetworkAdvancedMode = new Enum(
    {
        alias: 'AUTO_DETECT',
        text: 'auto-detect',
        value: 'vpc-route-auto-detect',
        tip: '该类型为默认类型，通过探测k8s节点镜像内核版本和模块，自动选择ipvlan或veth类型',
        message: '若没有明确特殊需求或没有充分了解的情况下，推荐使用该类型。',
    },
    {
        alias: 'KUBENET',
        text: 'kubenet',
        value: 'kubenet',
        tip: '该类型为k8s原生的网络方案，采用bridge构建容器网络，适用于所有镜像',
        message: '若需要稳定的网络体验，对基本特性没有过多扩展要求，可选择该类型。',
    },
    {
        alias: 'IPVLAN',
        text: 'ipvlan',
        value: 'vpc-route-ipvlan',
        tip: '该类型采用ipvlan构建容器网络，需要镜像内核版本≧4.9且包含ipvlan内核模块',
        message: '若需要高性能的容器网络，可选择该类型。但必须满足镜像条件，否则网卡无法生效。',
    },
    {
        alias: 'VETH',
        text: 'veth',
        value: 'vpc-route-veth',
        tip: '该类型采用veth pair构建容器网络，适用于所有镜像',
        message: '若需要高性能的容器网络，可选择该类型。',
    },
);

export const NetworkModeVpc = new Enum(
    {alias: 'VPC_KUBENET', text: 'VPC路由', value: 'kubenet'},
    {alias: 'VPC_ROUTE_VETH', text: 'VPC路由', value: 'vpc-route-veth'},
    {alias: 'VPC_ROUTE', text: 'VPC路由', value: 'vpc-route'},
    {alias: 'VPC_ROUTE_IPVLAN', text: 'VPC路由', value: 'vpc-route-ipvlan'},
    {alias: 'VPC_ROUTE_AUTO_DETECT', text: 'VPC路由', value: 'vpc-route-auto-detect'},
);

export const NetworkModeCni = new Enum(
    {alias: 'VPC_CNI_1', text: 'VPC-ENI', value: 'vpc-secondary-ip-veth'},
    {alias: 'VPC_CNI_2', text: 'VPC-ENI', value: 'vpc-secondary-ip-ipvlan'},
    {alias: 'VPC_CNI_3', text: 'VPC-ENI', value: 'vpc-secondary-ip-auto-detect'},
    // 存量集群
    {alias: 'VPC_CNI_4', text: 'VPC-ENI', value: 'vpc-cni'},
    {alias: 'VPC_CNI_4_1', text: 'VPC-ENI', value: 'vpc-eni'},
    // 独占弹性网卡
    {alias: 'VPC_CNI_5', text: 'VPC-ENI', value: 'vpc-exclusive-eni'},
);

export const NetworkModeHybrid = new Enum(
    {alias: 'VPC_HYBRID_1', text: 'VPC-Hybrid', value: 'bbc-vpc-secondary-ip-veth'},
    {alias: 'VPC_HYBRID_2', text: 'VPC-Hybrid', value: 'vpc-secondary-ip-ipvlan'},
    {alias: 'VPC_HYBRID_3', text: 'VPC-Hybrid', value: 'vpc-secondary-ip-auto-detect'},
    {alias: 'VPC_HYBRID_4', text: 'VPC-Hybrid', value: 'bbc-vpc-secondary-ip-ipvlan'},
);

export const NetworkType = new Enum(
    {alias: 'AUTO', text: '向导模式', value: 'auto'},
    {alias: 'CUSTOM', text: '自定义', value: 'custom'},
);

export const MasterType = new Enum(
    {
        alias: 'MANAGEDPRO',
        text: '托管Master',
        value: 'managedPro',
        message:
            '集群Master由容器引擎CCE完全托管，具备简单、低成本、高可用的特点，您只需购买Worker节点来运行工作负载即可',
    },
    {
        alias: 'CUSTOM',
        text: '自定义Master',
        value: 'containerizedCustom',
        message:
            '集群Master将会部署在您购买的服务器实例上，可对集群基础设施进行更细粒度的控制，需要自行规划、维护、升级服务器集群',
    },
);

export const CreateType = new Enum(
    {alias: 'CUSTOM', text: '新建节点', value: 'custom'},
    {alias: 'EXIST', text: '使用已有服务器', value: 'exist'},
);

export const EipType = new Enum(
    {alias: 'PREPAY', text: '包年包月计费', value: 'PREPAY'},
    {alias: 'NETRAFFIC', text: '按使用流量计费', value: 'netraffic'},
    {alias: 'BANDWIDTH', text: '按使用带宽计费', value: 'bandwidth'},
    {alias: 'BOS', text: '与BOS合并计费', value: 'PeakBandwidth_Percent_95_A'},
);

export const NodeName = new Enum(
    {alias: 'IPVS', text: '内网 IP', value: false},
    {alias: 'IPTABLES', text: '主机名称', value: true},
);

export const BecNodeType = new Enum({
    alias: 'BEC',
    text: '边缘云服务器BEC',
    value: 'BEC',
});

export const BecExistNodeType = new Enum(
    {
        alias: 'BEC',
        text: '边缘云服务器BEC',
        value: 'BEC',
    },
    {
        alias: 'BEC_EBC',
        text: '边缘裸金属服务器BEC',
        value: 'BEC_EBC',
    },
);

export const enum ClusterTemplateType {
    CUSTOM = 'CUSTOM',
    MANAGEDPRO = 'MANAGEDPRO',
    START = 'START',
    HPC = 'HPC',
    BBC = 'BBC',
    EXIST = 'EXIST',
    SERVERLESS = 'SERVERLESS',
    BEC = 'BEC',
    ARM = 'ARM',
}

export const BmType = new Enum(
    {alias: 'BBC1', text: 'vpc', value: 'bbc1'},
    {alias: 'CLASSIC', text: 'classic', value: 'classic'},
);
