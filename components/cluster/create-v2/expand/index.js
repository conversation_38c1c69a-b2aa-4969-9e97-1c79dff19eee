/**
 * @file components/cluster/create-v2/expand/index.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import * as AsyncValidator from 'async-validator';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {TextBox} from '@baiducloud/bce-ui/san';
import {store, connect} from 'san-store';
import IamSelct from '@components/iam-select';
import {getVpcName} from '../../../../utils/helper';
import {compareVersion} from '../../../../utils/util';
import {createK8sForm} from '../../../../common/biz/helper';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import tips from '../../../../utils/tips';
import {NetworkMode} from '../enums';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';
import Annotations from '../../../../pages/group/create/annotations';
import SecurityGroup from '@pages/cluster/create-v2/components/node/security-group';
import {
    docker,
    containerd_1_6_28,
    containerd_1_7_13,
    containerd_1_6_36,
    containerd_1_7_25,
    containerd_1_5_4,
    BEC_REGION,
} from '@pages/cluster/create-v2/components/cluster-config';
import {MasterType} from '../enums';
import {EnableGpuShare} from '../../../enable-gpu-share';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const Schema = AsyncValidator.default;

const rules = new Schema({});

const $flag = ServiceFactory.resolve('$flag');

const template = /* san */ html`<template>
    <div class="cce-cluster-create-expand">
        <ui-form
            s-ref="form"
            rules="{{rules}}"
            form-data="{=formData=}"
            form-errors="{{formErrors}}"
        >
            <ui-form-item inline label="集群名称：" class="cluster-expand-first-form-item">
                <span class="cluster-create-text-line"> {{instance.clusterName}} </span>
            </ui-form-item>

            <ui-form-item inline label="备注：">
                <span class="cluster-create-text-line"> {{instance.comment || '无'}} </span>
            </ui-form-item>

            <ui-form-item inline label="Kubernetes版本：">
                <span class="cluster-create-text-line"> {{instance.version}} </span>
            </ui-form-item>

            <ui-form-item inline label="VPC网络：">
                <span class="cluster-create-text-line">
                    {{instance.vpcName}}{{instance.vpcCidr ? '（' + instance.vpcCidr + '）' : ''}}
                </span>
            </ui-form-item>

            <ui-form-item inline name="runtimeType" label="容器运行时：">
                <ui-radio-select
                    value="{=formData.runtimeType=}"
                    datasource="{{runtimes}}"
                    radioType="button"
                    on-change="onChangeRuntime"
                />
                <div slot="help" s-if="formData.runtimeType !== 'custom'">
                    <span s-if="formData.runtimeKey === 'docker' && runtimes.length > 1">
                        Docker 将在 Kubernetes 1.24版本废弃，推荐选择 Containerd。
                    </span>
                    <a href="${tips.doc.runtimeType}" target="_blank">
                        如何选择 Kubernetes 集群的容器运行时组件
                    </a>
                </div>
            </ui-form-item>
            <ui-form-item label=" " inline s-if="formData.runtimeType === 'custom'">
                <ui-select
                    value="{=formData.runtimeKey=}"
                    on-change="onChangeRuntimeKey"
                    datasource="{{runtimeTypeOptions}}"
                >
                </ui-select>
                <ui-textbox
                    width="240"
                    value="{=formData.runtimeVersion=}"
                    placeholder="请输入支持的容器运行时版本号"
                ></ui-textbox>
                <span class="error-msg">{{runtimeVersionError}}</span>
                <div slot="help">
                    <span s-if="formData.runtimeKey === 'docker' && runtimes.length > 1">
                        Docker 将在 Kubernetes 1.24版本废弃，推荐选择 Containerd。
                    </span>
                    <a href="${tips.doc.runtimeType}" target="_blank">
                        如何选择 Kubernetes 集群的容器运行时组件
                    </a>
                </div>
            </ui-form-item>

            <ui-form-item inline label="Worker安全组：">
                <ui-radio-select
                    value="{=formData.securitySelectType=}"
                    datasource="{{securitySelectTypeDatasource}}"
                />
                <div s-if="{{formData.securitySelectType === 'already'}}" class="security-des">
                    节点将绑定集群当前关联的安全组
                </div>
                <div
                    class="security-tip"
                    s-if="{{!securityGroups.length && formData.securitySelectType === 'already'}}"
                >
                    温馨提示：检测到集群未关联安全组，系统将自动为集群创建默认安全组（安全组名称为{集群ID}-worker-{随机ID}）绑定Worker节点。<a
                        href="${tips.doc.securityGroup}"
                        target="_blank"
                        >了解更多CCE默认安全组规则</a
                    >
                </div>

                <div class="security-group-wrap">
                    <security-group
                        s-if="{{formData.securitySelectType === 'custom'}}"
                        s-ref="security-group"
                        type="Worker"
                        vpcId="{{vpcId}}"
                        vpcOriginId="{{vpcOriginId}}"
                        isEdit="true"
                        networkInfo="{{networkInfo}}"
                    />
                </div>
            </ui-form-item>
            <ui-form-item
                s-if="{{!securityGroups.length && formData.securitySelectType === 'already' && !isNewBec}}"
                inline
                label="安全组类型："
                ><ui-radio-select
                    value="{=formData.securityType=}"
                    datasource="{{securityTypeDatasource}}"
            /></ui-form-item>

            <ui-form-item inline label="节点配置：">
                <cce-cluster-create-node
                    s-if="instance.vpcId"
                    on-add="onAdd"
                    on-delete="onDelete"
                    s-ref="worker"
                    node-type="Worker"
                    isExpand
                    showPayType
                    existedNodeNum="{{existedNodeNum}}"
                    masterFlavor="{{masterFlavor}}"
                    clusterUuid="{{clusterUuid}}"
                />
            </ui-form-item>

            <advanced-config-container open="{=advancedOpen=}">
                <ui-form-item inline label="节点显存共享：" class="gpu-share-form-item">
                    <enable-gpu-share
                        s-ref="enableGpuShare"
                        clusterUuid="{{clusterUuid}}"
                        selectedNodes="{{selectedNodes}}"
                        value="{=formData.enableGpuShare=}"
                    />
                </ui-form-item>

                <cce-data-root s-ref="dataRoot" runtimeType="{{runtimeType}}" />

                <cce-cluster-create-script s-ref="script" />

                <ui-form-item
                    inline
                    label="封锁节点（cordon）："
                    name="enableCordon"
                    help="开启封锁节点后，节点将处于不可调度状态，新创建的Pod将不会调度到该节点上。若需要取消封锁，请执行kubectl uncordon命令。"
                >
                    <ui-switch checked="{=formData.enableCordon=}" />
                </ui-form-item>

                <cee-resource-reserved2 s-ref="resourceReserved" labelWidth="130px" />

                <cce-flex-tag
                    class="tags-hidden-border"
                    hiddenK8s="{{true}}"
                    hiddenLegend="{{true}}"
                    s-ref="flexTag"
                />

                <iam-select s-if="supportIamSelect" s-ref="iamSelect"></iam-select>

                <label-form
                    class="tag-labels"
                    s-ref="labelsForm"
                    form-data="{{labelFormData}}"
                    isflex="{{true}}"
                />

                <cce-taints-config s-ref="taints" />

                <ui-form-item
                    inline
                    label="注解（Annotations）："
                    name="annotations"
                    help="温馨提示：Annotations（注解）是一种将非标识性元数据附加到对象上的机制，每个Annotation包含键和值两部分。"
                >
                    <cce-annotations s-ref="annotations" />
                </ui-form-item>
            </advanced-config-container>
        </ui-form>
    </div>
</template>`;

@invokeComp(
    '@cce-cluster-create-node',
    '@cce-cluster-create-script',
    '@cce-flex-tag',
    '@cce-taints-config',
    '@cee-resource-reserved2',
    '@cce-data-root',
)
@invokeBceSanUI
class Expand extends Component {
    static template = template;

    static components = {
        'label-form': createK8sForm(),
        'advanced-config-container': AdvancedConfigContainer,
        'cce-annotations': Annotations,
        'security-group': SecurityGroup,
        'iam-select': IamSelct,
        'enable-gpu-share': EnableGpuShare,
        'ui-textbox': TextBox,
    };

    static computed = {
        runtimeVersionError() {
            const runtimeVersion = this.data.get('formData.runtimeVersion');
            const runtimeType = this.data.get('formData.runtimeType');
            if (runtimeType !== 'custom') {
                return '';
            }
            if (!runtimeVersion) {
                return '请输入容器运行时版本';
            } else if (!/^\d+\.\d+\.\d+$/.test(runtimeVersion)) {
                return '输入格式不正确';
            }
            return '';
        },
        // 小于 1.24.4
        lessThan124() {
            const k8sVersion = this.data.get('k8sVersion');
            if (k8sVersion) {
                return compareVersion('1.24.4', this.data.get('k8sVersion'));
            }
            return false;
        },
        runtimeTypeOptions() {
            // 创建集群的时候，没有小于124的，只有存量集群，新建节点组或者编辑节点组的时候，才会有小于124的版本
            // 只有创建集群不展示自定义版本，其他场景都需要展示，小于24的需要展示docker
            const lessThan124 = this.data.get('lessThan124');
            if (lessThan124) {
                return [
                    {
                        text: 'Containerd',
                        value: 'containerd',
                    },
                    {
                        text: 'Docker',
                        value: 'docker',
                    },
                ];
            }
            return [
                {
                    text: 'Containerd',
                    value: 'containerd',
                },
            ];
        },

        runtimes() {
            // 创建集群的时候，没有小于124的，只有存量集群，新建节点组或者编辑节点组的时候，才会有小于124的版本
            // 只有创建集群不展示自定义版本，其他场景都需要展示，小于24的需要展示docker
            const lessThan124 = this.data.get('lessThan124');
            const k8sVersion = this.data.get('k8sVersion');
            let customRuntimeVersion = {
                text: '自定义容器运行时版本',
                value: 'custom',
                key: 'custom',
                version: 'custom',
            };
            let runtimes = $flag.CceCreateRuntimeContainerd
                ? lessThan124
                    ? [docker, containerd_1_6_36, containerd_1_7_25]
                    : [containerd_1_6_36, containerd_1_7_25]
                : [docker];

            // 是否边缘节点
            const isNewBec = window.$context.getCurrentRegion().id === BEC_REGION;
            if (isNewBec) {
                return $flag.CceCreateRuntimeContainerd
                    ? lessThan124
                        ? [docker, containerd_1_6_28, containerd_1_7_13]
                        : [containerd_1_6_28, containerd_1_7_13]
                    : [docker];
            }
            if (k8sVersion?.includes('1.16') || k8sVersion?.includes('1.18')) {
                runtimes = [docker, containerd_1_5_4];
            }
            runtimes.push(customRuntimeVersion);
            return runtimes;
        },
        supportIamSelect() {
            const region = window.$context.getCurrentRegionId();
            const serviceTypes = this.data.get('serviceTypes');
            if (region === 'edge') {
                return false;
            }
            return serviceTypes.includes('BCC') || serviceTypes.includes('EBC');
        },
        selectedNodes() {
            const workerNodeList = this.data.get('workerNodeList');
            if (workerNodeList?.customData?.length) {
                return workerNodeList.customData.map(item => {
                    return {
                        serviceType: item?.serviceType,
                        gpuType: item?.bcc?.selectedFlavor?.isomerismCardType,
                        ...(item?.bcc || item?.bbc || {}),
                    };
                });
            } else if (workerNodeList?.existData?.[0]?.instanceList?.length) {
                return workerNodeList.existData[0].instanceList.map(item => {
                    return {
                        serviceType: workerNodeList.existData[0].nodeType,
                        gpuType: item.gpuCard,
                        ...item,
                    };
                });
            } else {
                return [];
            }
        },
    };

    initData() {
        const runtime = $flag.CceCreateRuntimeContainerd ? containerd_1_6_36 : docker;
        return {
            labelFormData: {},
            formData: {
                enableCordon: false,
                enableGpuShare: false,
                securitySelectType: 'already',
                securityType: 'normal',
                runtimeType: runtime.value,
                runtimeKey: runtime.key,
                runtimeVersion: runtime.version,
            },
            formErrors: null,
            rules,
            advancedOpen: false,
            clusterNewDetail: {},
            editSecurityShow: false,
            securityGroups: [],
            securitySelectTypeDatasource: [
                {text: '使用默认安全组', value: 'already'},
                {text: '使用自定义安全组', value: 'custom'},
            ],
            securityTypeDatasource: [
                {text: '普通安全组', value: 'normal'},
                {text: '企业安全组', value: 'enterprise'},
            ],
            serviceTypes: [],
        };
    }

    inited() {
        this.setIsNewBec();
        this.getClusterExtraInfo();
        this.initLessVersionRuntime();
        this.watch('k8sVersion', value => {
            this.initLessVersionRuntime();
        });
    }

    attached() {
        this.getClusterDetail().then(data => this.getVpcInfo(data));
        this.getClusterDetailV2();
        this.watch('isNewBec', val => {
            if (val) {
                this.data.set('formData.securityType', 'normal');
            }
        });
    }

    initLessVersionRuntime() {
        const value = this.data.get('k8sVersion');
        if (value?.includes('1.16') || value?.includes('1.18')) {
            this.data.set('formData.runtimeType', docker.value);
            this.data.set('formData.runtimeKey', docker.key);
            this.data.set('formData.runtimeVersion', docker.version);
        }
    }

    onChangeRuntime(e) {
        const target = this.data.get('runtimes')?.find(item => item.value === e.value);
        if (target && target.version !== 'custom') {
            this.data.set('formData.runtimeVersion', target.version);
            this.data.set('formData.runtimeKey', target.key);
        } else {
            this.data.set('formData.runtimeVersion', '');
            this.data.set('formData.runtimeKey', 'containerd');
        }
    }
    onChangeRuntimeKey(e) {
        this.data.set('formData.runtimeKey', e.value);
        this.data.set('formData.runtimeVersion', '');
    }

    setIsNewBec() {
        // 是否边缘节点
        const region = window.$context.getCurrentRegionId();
        const isNewBec = region === BEC_REGION;
        this.data.set('isNewBec', isNewBec);
        store.dispatch('setIsNewBec', isNewBec);
        return isNewBec;
    }

    async getClusterDetailV2() {
        const clusterUuid = this.data.get('clusterUuid');
        const res = await this.$http.getClusterDetailV2(clusterUuid);
        const clusterNewDetail = res.result.cluster;
        this.data.set('clusterNewDetail', clusterNewDetail);
        this.data.set('securityGroups', clusterNewDetail?.spec?.nodeDefaultSecurityGroups || []);
        this.setNetWorkInfo(clusterNewDetail);
    }

    setNetWorkInfo(clusterDetailV2) {
        const {spec} = clusterDetailV2;
        let networkInfo = {};
        if (spec?.vpcID) {
            networkInfo.vpcID = spec.vpcID;
        }
        if (spec?.vpcCIDR) {
            networkInfo.vpcCIDR = spec.vpcCIDR;
        }
        if (spec?.containerNetworkConfig?.clusterPodCIDR) {
            networkInfo.podCIDR = spec?.containerNetworkConfig?.clusterPodCIDR;
        }
        if (spec?.containerNetworkConfig?.ipVersion !== 'ipv4') {
            if (spec?.containerNetworkConfig?.clusterPodCIDRIPv6) {
                networkInfo.podCIDRIPv6 = spec?.containerNetworkConfig?.clusterPodCIDRIPv6;
            }
        }
        if (spec?.containerNetworkConfig?.nodePortRangeMax) {
            networkInfo.nodePortRangeMax = spec?.containerNetworkConfig?.nodePortRangeMax;
        }
        if (spec?.containerNetworkConfig?.nodePortRangeMin) {
            networkInfo.nodePortRangeMin = spec?.containerNetworkConfig?.nodePortRangeMin;
        }
        if (spec?.masterConfig?.exposedPublic) {
            networkInfo.exposedPublic = spec?.masterConfig?.exposedPublic;
        }
        this.data.set('networkInfo', networkInfo);
    }

    getClusterDetail() {
        const clusterUuid = this.data.get('clusterUuid');
        return this.$http.detailCluster(clusterUuid).then(data => {
            this.data.set('instance', data);
            let cniMode = data.advancedOptions.cniMode;
            if (cniMode === 'vpc-eni') {
                cniMode = NetworkMode.CNI;
            }
            store.dispatch('setNetworkMode', cniMode);
            store.dispatch('setEBPFEnabled', !!data.ebpfConfiuration?.enabled);
            this.data.set('IPversion', data.ipVersion === 'dualStack');
            store.dispatch('changeIpVersion', data.ipVersion === 'dualStack');
            store.dispatch('onExtendClusterUuid', clusterUuid);
            store.dispatch('changeSelectedVpcId', data.vpcId);
            store.dispatch('setClusterType', data.clusterType);
            store.dispatch('setRuntimeType', data.runtimeType);
            store.dispatch('setRuntimeVersion', data.runtimeVersion);
            store.dispatch('changeK8sVersion', data.version); // 集群版本
            this.getClusterFlavor(data.masterType);

            return data;
        });
    }

    async getClusterFlavor(masterType) {
        if (masterType === MasterType.MANAGEDPRO) {
            const clusterUuid = this.data.get('clusterUuid');
            const {result} = await this.$http.getClusterFlavor(clusterUuid);
            const masterFlavor = result?.clusterScaleInfo?.clusterFlavor;
            const existedNodeNum = result?.clusterScaleInfo?.existedResourceNum?.instanceNum;
            this.data.set('masterFlavor', masterFlavor);
            this.data.set('existedNodeNum', existedNodeNum);
            store.dispatch('setMasterFlavor', masterFlavor);
            store.dispatch('setExistedNodeNum', existedNodeNum);
        } else {
            this.data.set('masterFlavor', null);
            this.data.set('existedNodeNum', null);
            store.dispatch('setMasterFlavor', null);
            store.dispatch('setExistedNodeNum', null);
        }
    }

    getVpcInfo(detail) {
        const isNewBec = this.data.get('isNewBec');
        if (isNewBec) {
            return;
        }
        return this.$http
            .vpcMap({vpcIds: [detail.vpcId]})
            .then(data => {
                const item = _.find(data.result, d => d.shortId === detail.vpcId);
                if (item) {
                    let text =
                        getVpcName(item.name) +
                        (item.cidr ? '（' + item.cidr + '）' : '') +
                        (item.ipv6Cidr ? '（' + item.ipv6Cidr + '）' : '');
                    store.dispatch('changeSelectedVpcItem', {
                        value: item.shortId,
                        text: text,
                        title: text,
                        defaultVpc: item.defaultVpc,
                        cidr: item.cidr,
                        shortId: item.shortId,
                        ipv6Cidr: item.ipv6Cidr,
                        auxiliaryCidr: item.auxiliaryCidr,
                    });
                }
            })
            .catch(() =>
                store.dispatch('changeSelectedVpcItem', {
                    value: detail.vpcId,
                    text: detail.vpcName,
                }),
            );
    }

    onAdd(data) {
        this.checkNodeServiceType(data);
        this.fire('node-change', {
            type: 'worker',
            data,
        });
        this.data.set('error', false);
    }

    onDelete(data) {
        this.checkNodeServiceType(data);
        this.fire('node-change', {
            type: 'worker',
            data,
        });
        this.data.set('error', false);
    }
    checkNodeServiceType(data) {
        let serviceTypes = [];
        if (data?.customData?.length) {
            serviceTypes = data?.customData?.map(item => item.serviceType);
        }
        if (data?.existData?.length) {
            serviceTypes = data?.existData?.map(item => item.nodeType);
        }
        this.data.set('serviceTypes', serviceTypes);
    }

    getFormData() {
        const formData = this.data.get('formData');
        const {runtimeKey: runtimeType, runtimeVersion} = formData;
        const securityGroups = this.data.get('securityGroups');
        const securityGroup = this.ref('security-group');
        const iamRole = this.ref('iamSelect') ? this.ref('iamSelect').getFormData() : {};
        const worker = this.ref('worker');
        const script = this.ref('script');
        const instance = this.data.get('instance');
        const flexTagData = this.ref('flexTag') && this.ref('flexTag').getFormData();
        const {kubeReserved, systemReserved, customKubeletParams} =
            this.ref('resourceReserved').getFormData();
        const bccTags = flexTagData && flexTagData.bccTags;
        let labels = {};
        const tagsLabel = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');
        const tags = _.map(tagsLabel.getValue(), (value, key) => ({key, value}));
        _.each(tags, tag => {
            if (tag.key || tag.value) {
                labels[tag.key] = tag.value;
            }
        });
        labels[`cce.baidubce.com/gpu-share-device-plugin`] = formData.enableGpuShare
            ? 'enable'
            : 'disable';
        delete formData.enableGpuShare;
        const taints = this.ref('taints') && this.ref('taints').getFormData();
        const annotations = this.ref('annotations')?.getFormData() || {};
        const dataRoot = this.ref('dataRoot') && this.ref('dataRoot').getFormData();

        let _securityGroups = [];

        if (formData.securitySelectType === 'custom') {
            const securityGroupData = securityGroup.getFormData();
            const {securityGroupIdList} = securityGroupData;
            _securityGroups = securityGroupIdList;
        } else {
            _securityGroups = securityGroups;
        }

        return _.extend(
            {},
            instance,
            formData,
            script ? script.getFormData() : {},
            {workers: worker.getFormData()},
            {labels},
            {tags: bccTags},
            {relationTag: flexTagData.relationTag || false},
            {securityGroups: _securityGroups},
            {kubeReserved},
            {systemReserved},
            {customKubeletParams},
            {taints},
            {annotations},
            {runtimeType},
            {runtimeVersion},
            dataRoot,
            {iamRole},
        );
    }

    validateForm() {
        const form = this.ref('form');
        const worker = this.ref('worker');
        const flexTag = this.ref('flexTag');
        const tags = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');
        const taints = this.ref('taints');
        const annotations = this.ref('annotations');
        const resourceReserved = this.ref('resourceReserved');
        const securityGroup = this.ref('security-group');
        const securitySelectType = this.data.get('formData.securitySelectType');
        const dataRoot = this.ref('dataRoot');
        const runtimeVersionError = this.data.get('runtimeVersionError');

        const promiseArr = [
            form?.validateForm(),
            worker.validateForm(),
            Promise.all([
                flexTag.validateForm(),
                tags.validateForm(),
                taints.validateForm(),
                annotations.validateForm(),
                securityGroup ? securityGroup.validateForm() : Promise.resolve(),
                runtimeVersionError ? Promise.reject() : Promise.resolve(),
                dataRoot ? dataRoot.validateForm() : Promise.resolve(),
                resourceReserved.validateForm(),
            ]).catch(() => {
                const advancedOpen = this.data.get('advancedOpen');
                if (!advancedOpen) {
                    this.data.set('advancedOpen', true);
                }

                return Promise.reject();
            }),
        ];

        if (securitySelectType === 'custom') {
            promiseArr.push(securityGroup.validateForm());
        }

        return Promise.all(promiseArr);
    }

    getClusterExtraInfo() {
        const clusterUuid = this.data.get('clusterUuid');
        return this.$http.getClusterExtraInfoV2(clusterUuid).then(({result}) => {
            if (result?.eniSubnets?.length) {
                const eniVPCSubnetIDs = {};
                result.eniSubnets.forEach(element => {
                    if (!eniVPCSubnetIDs[element.availableZone]) {
                        eniVPCSubnetIDs[element.availableZone] = [element.subnetID];
                    } else {
                        eniVPCSubnetIDs[element.availableZone].push(element.subnetID);
                    }
                });
                store.dispatch('setEniVPCSubnetIDs', eniVPCSubnetIDs);
            }
        });
    }
}

@asComponent('@cce-cluster-create-expand')
export default class ExpandStore extends connect.san(StoreMap)(Expand) {}
