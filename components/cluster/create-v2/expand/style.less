.cce-cluster-create-expand {
    margin-top: 20px;
    padding: 20px;
    width: 1048px;
    background-color: #fff;
    border-radius: 4px;

    .security-item {
        display: inline-block;
        margin-right: 16px;
        line-height: 30px;
    }
    a {
        cursor: pointer;
    }
    .full-item-content {
        .bui-table-cell-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    .bui-form {
        .bui-form-item-inline.cluster-expand-first-form-item {
            margin-top: 0;
        }

        .bui-form-item-inline {
            .bui-form-item-label {
                width: 130px;
            }
        }
        .iam-select-container {
            .s-form-item-label {
                width: 142px;
                padding-left: 11px;
                text-align: left;
            }
        }
    }

    .tag-labels {
        .bui-as-form-1 {
            padding: 0;
            margin: 0;
        }

        .bui-form .bui-form-item-inline .bui-form-item-label {
            width: 130px !important;
        }

        .bui-as-form {
            .bui-as-form-row {
                margin: 0;

                .bui-form-item-tags {
                    margin-bottom: 0;
                }

                .bui-form-item {
                    margin: 0;
                }
            }
        }
    }
    .bui-as-form-1 .cce-add-tag .bui-form-item-inline .bui-form-item-label {
        width: auto !important;
    }
    .tags-hidden-border .bui-form .bui-form-item:first-child {
        margin-bottom: 0;
    }

    .share-gpu-item .video-memory-share-radio {
        line-height: 30px;
        height: 30px;
    }
    .security-des {
        margin: 12px 0;
        color: #84868c;
    }
    .security-tip {
        width: 700px;
        color: #ff9326;
    }
    .security-group-wrap {
        margin-top: 12px;
    }
    .error-msg {
        color: #f33e3e;
    }
}
