import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Form, Input, InputNumber} from '@baidu/sui';
import jsyaml from 'js-yaml';

const template = html`<template>
    <s-form class="p2p-image" s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
        <s-form-item
            prop="eccrInstanceIDs"
            label="实例ID："
            help="容器镜像服务CCR实例，当前仅支持一个实例"
        >
            <s-input type="text" width="400" value="{=formData.eccrInstanceIDs=}" />
        </s-form-item>
        <s-form-item
            prop="totalRateLimit"
            label="带宽限速："
            help="节点拉取镜像的总带宽限制（包括上行和下行带宽），默认值512MiB/s，可根据实际节点调整"
        >
            <s-input-number
                width="400"
                min="{{1}}"
                max="{{10240}}"
                value="{=formData.totalRateLimit=}"
            ></s-input-number>
            <span>MiB/s</span>
        </s-form-item>
        <s-form-item prop="dfdaemonPort" label="端口：" help="默认为60001，输入范围为11000~61000">
            <s-input-number
                width="400"
                min="{{11000}}"
                max="{{61000}}"
                value="{=formData.dfdaemonPort=}"
            ></s-input-number>
        </s-form-item>
    </s-form>
</template>`;

@decorators.asComponent('@p2p-image')
export default class P2PImage extends Component {
    static template = template;
    static components = {
        's-input': Input,
        's-textarea': Input.TextArea,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
    };
    initData() {
        return {
            formData: {
                eccrInstanceIDs: '',
                dfdaemonPort: 60001,
                totalRateLimit: 512,
            },
            rules: {
                eccrInstanceIDs: [{required: true, message: '请输入实例ID'}],
                dfdaemonPort: [{required: true, message: '请输入端口'}],
                totalRateLimit: [{required: true, message: '请输入带宽限速'}],
            },
        };
    }
    initFormData(comp) {
        const data = jsyaml.load(comp.instance.params);
        const dfdaemonPort = data?.dragonfly?.dfdaemon?.containerPort || 60001;
        const totalRateLimitStr = data?.dragonfly?.dfdaemon?.config?.download?.totalRateLimit;
        const totalRateLimit = parseInt(totalRateLimitStr, 10) || 512;
        const eccrInstanceIDs = data?.eccrInstanceIDs || [];
        eccrInstanceIDs && this.data.set('formData.eccrInstanceIDs', eccrInstanceIDs.join(','));
        dfdaemonPort && this.data.set('formData.dfdaemonPort', dfdaemonPort);
        totalRateLimit && this.data.set('formData.totalRateLimit', totalRateLimit);
    }
    getFormData() {
        const {eccrInstanceIDs, dfdaemonPort, totalRateLimit} = this.data.get('formData');
        const data = {
            eccrInstanceIDs: [eccrInstanceIDs],
            dfdaemonPort,
            totalRateLimit: `${totalRateLimit}Mi`,
        };
        return jsyaml.safeDump(data);
    }
    validateForm() {
        return this.ref('form')?.validateFields();
    }
}
