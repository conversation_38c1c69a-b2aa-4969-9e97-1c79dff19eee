import './components.less';
import _ from 'lodash';
import {Component, defineComponent} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {
    Tabs,
    Alert,
    Search,
    Loading,
    Button,
    Drawer,
    Dropdown,
    Checkbox,
    Dialog,
    Notification,
    Tooltip,
} from '@baidu/sui';
import {TipButton, Tip} from '@baidu/sui-biz';
import {OutlinedQuestionSquare, OutlinedRefresh} from '@baidu/sui-icon';
import emptyImg from '../../../../pages/cluster/list/empty.png';
import jsyaml from 'js-yaml';
import {getTicketUrl} from '../../../../utils/util';
import {STS_ROLE_BCI_PARAM} from '../../../../utils/constants';
import {GpuEnableSGPUType, CompStatus, ClusterType} from '../../../../utils/enums';
import {MasterType, NetworkModeVpc} from '../enums';
import HelmStroage from './helm-stroage';
import GpuConfig from './gpu-config';
import Volcano from './volcano';
import NpuConfig from './npu-config';
import Paddleflow from './paddleflow';
import FluidConfig from './fluid-config';
import IngressConfig from './ingress-config';
import ErrAutoFix from './err-autofix';
import DeschedulerConfig from './descheduler-config';
import P2pImage from './P2pImage';
import CredentialImage from './credential-image';
import HaloletConfig from './halolet';
import PfsStroage from './pfs-storage';
import QuotaSchedulerConfig from './quota-scheduler-config';

const $flag = ServiceFactory.resolve('$flag');

const edgeSupportComps = [
    'cce-pro-apiserver',
    'cce-pro-kube-scheduler',
    'cce-pro-controller-manager',
    'cce-coredns',
    'cce-lb-controller',
    'cce-vpc-native-cni',
    'cce-csi-pfs-plugin',
    'cce-gpu-manager',
    'cce-aibox',
    'cce-rdma-plugin',
    'cce-volcano',
    'cce-fluid',
    'cce-npu-manager',
    // 'cce-log-operator',
    'cce-node-problem-detector',
    'cce-node-remedier',
];

const edgeEnableComps = [
    'cce-gpu-manager',
    'cce-aibox',
    'cce-rdma-plugin',
    'cce-volcano',
    'cce-fluid',
    'cce-npu-manager',
    // 'cce-log-operator',
    'cce-node-problem-detector',
    'cce-node-remedier',
];

/* eslint-disable max-len */
const template = html`<template>
    <link
        rel="stylesheet"
        type="text/css"
        href="https://code.bdstatic.com/npm/github-markdown-css@3.0.1/github-markdown.css"
    />
    <div class="cce-helm-components">
        <template s-if="!!clusterUuid">
            <s-alert skin="warning">
                <div>
                    您可以通过安装扩展组件来使用CCE集群提供的增强功能
                    <template s-if="!$flag.CceSupportXS">
                        ，更多信息请见
                        <a href="https://cloud.baidu.com/doc/CCE/s/3kp7zv91j" target="_blank"
                            >组件概述
                        </a>
                    </template>
                    。
                </div>
                <div>
                    建议通过控制台管理您的组件，如采用其他方式可能会出现不可预知的问题，请知悉。
                </div>
                <div s-if="canUpdateComp.length">
                    当前集群中<span class="can-update-comp">{{canUpdateCompText}}</span
                    >组件已发布新的版本，请升级。
                </div>
            </s-alert>
        </template>
        <div class="action-bar">
            <s-button
                s-if="!!clusterUuid"
                on-click="getCompData"
                class="refresh-comp {{canUpdateComp.length ? 'with-update-info' : ''}}"
            >
                <s-icon-refresh is-button="{{false}}" />
            </s-button>
            <s-search
                s-if="!!clusterUuid"
                placeholder="请输入组件进行搜索"
                class="comp-search"
                value="{=searchValue=}"
            />
            <s-tabs active="{=active=}">
                <s-tabpane s-for="i in tabs" label="{{i.label}}" key="{{i.key}}"></s-tabpane>
            </s-tabs>
        </div>
        <s-loading loading s-if="loading" class="helm" size="large" />
        <div class="helm-card-list">
            <div class="empty" s-if="currentComps.length === 0 && !loading">
                <img src="${emptyImg}" />
                <div>{{debounceSearchValue ? '无搜索结果' : '暂无数据'}}</div>
            </div>
            <template s-for="comp in currentComps">
                <div
                    class="helm-comp-item {{comp.disabled ? 'disabled' : ''}} {{comp.selected ? 'selected' : ''}} {{comp.invalid ? 'invalid' : ''}}"
                >
                    <div class="title" on-click="check(comp)">
                        <template s-if="!clusterUuid">
                            <s-tooltip
                                content="需要在集群创建之后进行安装"
                                s-if="comp.name === 'cce-gpu-manager' || comp.name === 'cce-paddleflow'"
                            >
                                <s-checkbox
                                    checked="{{comp.selected}}"
                                    disabled="{{comp.disabled}}"
                                />
                            </s-tooltip>
                            <s-checkbox
                                s-else
                                checked="{{comp.selected}}"
                                disabled="{{comp.disabled}}"
                            />
                        </template>
                        <span>{{comp.title}}</span>
                        <span s-if="comp.meta.managed" class="is-manager"> 托管 </span>
                        <span
                            class="helm-status {{comp | getCompStatusCLass}}"
                            s-if="comp.instance.status.phase"
                        >
                            {{comp | getCompStatusText | raw | empty}}
                        </span>
                        <s-tooltip
                            placement="top"
                            s-if="(comp.instance.status.phase === 'Abnormal' ||  comp.instance.status.phase === 'Failed' ||  comp.instance.status.phase === 'Not Found' || comp.instance.status.phase === 'Partially Ready') && (comp.instance.status.message || comp.instance.status.code || comp.instance.status.traceID)"
                            class="warning-tip"
                        >
                            <s-icon-question is-button="{{false}}" color="#999" />
                            <div slot="content" style="width: 300px;">
                                <p>请提交工单，并附上完整的提示信息。</p>
                                <p s-if="comp.instance.status.message">
                                    <b>message：</b>{{comp.instance.status.message}}
                                </p>
                                <p s-if="comp.instance.status.code">
                                    <b>code：</b>{{comp.instance.status.code}}
                                </p>
                                <p s-if="comp.instance.status.traceID">
                                    <b>traceID：</b>{{comp.instance.status.traceID}}
                                </p>
                            </div>
                        </s-tooltip>
                    </div>
                    <div class="desc" title="{{comp.meta.shortIntroduction || comp.desc}}">
                        <b>[{{comp.meta.required?'系统组件':'可选组件'}}] </b>
                        {{comp.meta.shortIntroduction || comp.desc || '--'}}
                    </div>
                    <div
                        s-if="!$flag.CceSupportXS || (!clusterUuid && comp.withParams)"
                        class="doc-link"
                    >
                        <a
                            s-if="!$flag.CceSupportXS"
                            href="{{comp.doc}}"
                            target="_blank"
                            on-click="stopPropagation"
                        >
                            组件介绍
                        </a>

                        <s-button
                            s-if="!clusterUuid && comp.withParams"
                            skin="stringfy"
                            on-click="config($event, comp)"
                            disabled="{{comp | configDisabled}}"
                        >
                            配置
                        </s-button>
                    </div>
                    <div class="version" s-if="clusterUuid">
                        <template s-if="comp.instance.installedVersion">
                            <span> 当前版本：{{comp.instance.installedVersion}} </span>
                            <span class="update-version">
                                {{comp | getLatestVersion | raw | empty}}
                            </span>
                        </template>
                    </div>
                    <div s-if="clusterUuid" class="operation">
                        <s-loading
                            s-if="comp.status === 'Loading'"
                            loading
                            size="small"
                            class="helm-loading"
                        />

                        <s-tipbutton
                            s-if="!comp.instance && comp.status !== 'Loading' && clusterUuid"
                            isDisabledVisibile="{{true}}"
                            disabled="{{!comp.meta.installInfo.allowInstall || comp.disabled}}"
                            skin="stringfy"
                            on-click="instanll(comp)"
                        >
                            <div slot="content">
                                {{comp.meta.installInfo.message || '暂不支持该操作'}}
                            </div>
                            安装
                        </s-tipbutton>

                        <s-dropdown
                            class="more-operation-btn"
                            placement="bottomRight"
                            trigger="click"
                            s-if="comp.instance && comp.status !== 'Loading' && !installDrawerStatus"
                        >
                            <div
                                slot="overlay"
                                class="comp-btn-wrap"
                                s-if="comp.type !== 'core' || comp.name === 'cce-virtual-kubelet'"
                            >
                                <!--核心组件cce-virtual-kubelet可以卸载-->
                                <s-button
                                    s-if="(comp.instance.status.phase === 'Running' || comp.instance.status.phase === 'Deployed' || comp.instance.status.phase === 'Partially Ready') && comp.name !== 'cce-ingress-controller' && comp.name !== 'cce-vpc-native-cni'&& comp.name !== 'cce-network-v2' && comp.name !== 'cce-lb-controller' && !comp.meta.managed"
                                    skin="stringfy"
                                    on-click="redirectInstance(comp)"
                                >
                                    查看实例
                                </s-button>

                                <s-tipbutton
                                    s-if="comp.name === 'cce-ingress-nginx-controller'"
                                    isDisabledVisibile="{{true}}"
                                    disabled="{{!comp.meta.installInfo.allowInstall}}"
                                    skin="stringfy"
                                    on-click="instanll(comp)"
                                >
                                    <div slot="content">{{comp.meta.installInfo.message}}</div>
                                    新增实例
                                    <s-tip
                                        class="cce-cluster-helm-ingress-tip"
                                        placement="right"
                                        content="集群中可部署多套独立的Nginx Ingress Controller，以满足不同的服务访问场景"
                                    />
                                </s-tipbutton>

                                <template s-if="comp.instance && comp.withParams">
                                    <!-- GPU Manager 支持查看配置不支持修改  -->
                                    <s-button
                                        s-if="comp.name === 'cce-gpu-manager'"
                                        skin="stringfy"
                                        on-click="view(comp)"
                                    >
                                        查看配置
                                    </s-button>
                                    <s-tipbutton
                                        s-else-if="comp.name !== 'cce-ingress-nginx-controller'"
                                        isDisabledVisibile="{{true}}"
                                        disabled="{{!comp.instance.updateInfo.allowUpdate}}"
                                        skin="stringfy"
                                        on-click="update(comp)"
                                    >
                                        <div slot="content">
                                            {{comp.instance.updateInfo.message}}
                                        </div>
                                        修改配置
                                    </s-tipbutton>
                                </template>

                                <s-tipbutton
                                    s-if="comp.instance && comp.name !== 'cce-ingress-nginx-controller'"
                                    isDisabledVisibile="{{true}}"
                                    disabled="{{!comp.instance.upgradeInfo.allowUpgrade}}"
                                    skin="stringfy"
                                    on-click="onUpgrade(comp)"
                                >
                                    <div slot="content">
                                        <span s-if="comp.isLatest"> 已是最新版本 </span>
                                        <span s-else-if="comp.instance.upgradeInfo.message">
                                            {{comp.instance.upgradeInfo.message}}
                                        </span>
                                        <span s-else>
                                            当前无可升级的版本，如有需要请提<a
                                                href="{{ticketUrl}}"
                                                target="_blank"
                                                >工单</a
                                            >
                                        </span>
                                    </div>
                                    升级
                                </s-tipbutton>
                                <s-tipbutton
                                    s-if="comp.instance && comp.meta.logging && comp.meta.logging.capable && !comp.meta.logging.enabled"
                                    isDisabledVisibile="{{true}}"
                                    disabled="{{checkLogOperator}}"
                                    skin="stringfy"
                                    on-click="openLog(comp, true)"
                                >
                                    <div slot="content">
                                        <span> 当前未安装CCE Log Operator日志组件 </span>
                                        <a
                                            href="javascript:void(0)"
                                            on-click="instanllBlsLogOperator(comp)"
                                            >立即安装</a
                                        >
                                    </div>
                                    开启日志
                                </s-tipbutton>
                                <s-tipbutton
                                    s-if="comp.instance && comp.meta.logging && comp.meta.logging.enabled"
                                    isDisabledVisibile="{{true}}"
                                    skin="stringfy"
                                    on-click="redirectComponentLogs(comp.name === 'cce-virtual-kubelet' ? 'control':'components', comp)"
                                >
                                    查看日志
                                </s-tipbutton>
                                <s-tipbutton
                                    s-if="comp.instance && comp.meta.logging && comp.meta.logging.enabled"
                                    isDisabledVisibile="{{true}}"
                                    skin="stringfy"
                                    on-click="openLog(comp, false)"
                                >
                                    关闭日志
                                </s-tipbutton>
                                <s-tipbutton
                                    s-if="comp.instance && comp.name !== 'cce-ingress-nginx-controller' && !comp.meta.required"
                                    isDisabledVisibile="{{true}}"
                                    disabled="{{!comp.instance.uninstallInfo.allowUninstall}}"
                                    skin="stringfy"
                                    on-click="uninstanll(comp)"
                                >
                                    <div slot="content">
                                        {{comp.instance.uninstallInfo.message}}
                                    </div>
                                    卸载
                                </s-tipbutton>
                                <s-button
                                    s-if="comp.instance && comp.name === 'cce-virtual-kubelet'"
                                    skin="stringfy"
                                    on-click="redirectMonitor('节点监控', '虚拟节点资源（BCI overview）')"
                                >
                                    查看监控
                                </s-button>
                            </div>
                            <div slot="overlay" class="comp-btn-wrap" s-else>
                                <s-button skin="stringfy" on-click="redirectMonitor">
                                    查看监控
                                </s-button>
                                <s-button
                                    skin="stringfy"
                                    on-click="redirectComponentLogs('control')"
                                >
                                    查看日志
                                </s-button>
                            </div>
                            <s-button skin="normal-stringfy"> ... </s-button>
                        </s-dropdown>
                    </div>
                </div>
            </template>
        </div>

        <div s-if="!clusterUuid" class="selected-comp">
            <label>已选：</label>
            <span>{{selectedComp}}</span>
        </div>
        <s-drawer
            title="{{compInfoDrawerTitle}}"
            size="{{600}}"
            open="{=compInfoDrawerStatus=}"
            direction="right"
        >
            <div class="markdown-body">{{compInfoDrawerContent | raw | empty}}</div>
        </s-drawer>

        <s-drawer
            title="{{installDrawerTitle}}"
            class="install-drawer"
            size="{{drawerWitdth}}"
            open="{=installDrawerStatus=}"
            direction="right"
            maskClose="{{false}}"
            mask
        >
            <div s-if="installDrawerStatus" class="component-params">
                <ingress-config
                    clusterUuid="{{clusterUuid}}"
                    s-ref="cce-ingress-nginx-controller"
                    s-if="currentInstallComp === 'cce-ingress-nginx-controller'"
                />
                <helm-stroage
                    s-ref="cce-csi-bos-plugin"
                    cce-csi-bos-plugin
                    s-if="currentInstallComp === 'cce-csi-bos-plugin'"
                />
                <helm-stroage
                    s-ref="cce-csi-cds-plugin"
                    cce-csi-cds-plugin
                    s-if="currentInstallComp === 'cce-csi-cds-plugin'"
                />
                <helm-stroage
                    s-ref="cce-csi-pfs-plugin"
                    cce-csi-pfs-plugin
                    s-if="currentInstallComp === 'cce-csi-pfs-plugin'"
                />
                <p2p-image
                    s-ref="cce-p2p-accelerator"
                    s-if="currentInstallComp === 'cce-p2p-accelerator'"
                />
                <credential-image
                    s-ref="cce-credential-controller"
                    installDrawerEdit="{{installDrawerEdit}}"
                    s-if="currentInstallComp === 'cce-credential-controller'"
                />
                <gpu-config
                    s-ref="cce-gpu-manager"
                    s-if="currentInstallComp === 'cce-gpu-manager'"
                    clusterUuid="{{clusterUuid}}"
                    masterType="{{masterType}}"
                    isView="{{isView}}"
                    isUpgrade="{{isUpgrade}}"
                />
                <npu-config
                    s-ref="cce-npu-manager"
                    s-if="currentInstallComp === 'cce-npu-manager'"
                />
                <volcano s-ref="cce-volcano" s-if="currentInstallComp === 'cce-volcano'" />
                <paddleflow
                    s-if="currentInstallComp === 'cce-paddleflow'"
                    s-ref="cce-paddleflow"
                    clusterUuid="{{clusterUuid}}"
                />
                <fluid-config s-if="currentInstallComp === 'cce-fluid'" s-ref="cce-fluid" />
                <err-autofix
                    s-ref="cce-node-remedier"
                    s-if="currentInstallComp === 'cce-node-remedier'"
                />
                <descheduler-config
                    s-ref="cce-descheduler"
                    s-if="currentInstallComp === 'cce-descheduler'"
                    clusterUuid="{{clusterUuid}}"
                />
                <halolet-config
                    s-ref="cce-qos-agent"
                    s-if="currentInstallComp === 'cce-qos-agent'"
                    clusterUuid="{{clusterUuid}}"
                />
                <pfs-storage
                    s-ref="cce-csi-pfsl2-plugin"
                    s-if="currentInstallComp === 'cce-csi-pfsl2-plugin'"
                    clusterUuid="{{clusterUuid}}"
                    installDrawerEdit="{{installDrawerEdit}}"
                    isUpgrade="{{isUpgrade}}"
                />
                <quota-scheduler-config
                    s-ref="cce-node-problem-detector"
                    s-if="currentInstallComp === 'cce-node-problem-detector'"
                    clusterUuid="{{clusterUuid}}"
                    on-confirmDisabled="confirmDisabledChange"
                    installDrawerEdit="{{installDrawerEdit}}"
                    titleName="CCE Node Problem Detector"
                />
                <quota-scheduler-config
                    s-ref="cce-coredns"
                    s-if="currentInstallComp === 'cce-coredns'"
                    on-confirmDisabled="confirmDisabledChange"
                    installDrawerEdit="{{installDrawerEdit}}"
                    clusterUuid="{{clusterUuid}}"
                    titleName="CoreDNS"
                />
            </div>

            <div class="operate">
                <s-button on-click="cancelInstall" disabled="{{comfirming}}"> 取消 </s-button>
                <s-button
                    skin="primary"
                    on-click="onConfirm"
                    class="ml20"
                    disabled="{{comfirming || confirmDisabled}}"
                >
                    确认
                </s-button>
            </div>
        </s-drawer>
    </div>
</template>`;
/* eslint-disable max-len */

const rdmaInstallTip = defineComponent({
    template: `
        <template>
            <div>确定安装CCE RDMA Device Plugin组件吗?</div>
            <div class="enhanced-tip">操作系统内核版本要求4.9以上，否则可能影响RDMA功能使用！</div>
        </template>
    `,
});

@decorators.asComponent('@cce-helm-components')
export default class HelmComp extends Component {
    static template = template;
    static components = {
        's-tipbutton': TipButton,
        's-checkbox': Checkbox,
        's-alert': Alert,
        's-search': Search,
        's-tip': Tip,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-loading': Loading,
        's-button': Button,
        's-drawer': Drawer,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        's-icon-question': OutlinedQuestionSquare,
        'helm-stroage': HelmStroage,
        'gpu-config': GpuConfig,
        'npu-config': NpuConfig,
        'volcano': Volcano,
        'paddleflow': Paddleflow,
        'fluid-config': FluidConfig,
        'ingress-config': IngressConfig,
        'err-autofix': ErrAutoFix,
        's-dropdown': Dropdown,
        'descheduler-config': DeschedulerConfig,
        'p2p-image': P2pImage,
        'halolet-config': HaloletConfig,
        'pfs-storage': PfsStroage,
        'quota-scheduler-config': QuotaSchedulerConfig,
        'credential-image': CredentialImage,
    };
    static filters = {
        configDisabled(comp) {
            const diabledOnCreateClusterComps = this.data.get('diabledOnCreateClusterComps');
            const k8sVersion = this.data.get('k8sVersion');
            return diabledOnCreateClusterComps.includes(comp.name) || /^1\.22\./.test(k8sVersion)
                ? 'true'
                : '';
        },
        getLatestVersion(comp) {
            return comp.isLatest ? '' : '最新版本：' + (comp.meta.latestVersion || '-');
        },
        getCompStatusText(comp) {
            const phase = comp?.instance?.status?.phase;
            return CompStatus.getTextFromValue(phase);
        },
        getCompStatusCLass(comp) {
            const phase = comp?.instance?.status?.phase;
            return CompStatus.toArray().find(e => e.value === phase)?.klass;
        },
    };
    static computed = {
        isEdgeCluster() {
            const clusterType =
                this.data.get('clusterType') || window.$context.getCurrentRegionId();
            return clusterType === 'edge';
        },
        currentComps() {
            const active = this.data.get('active');
            const isEdgeCluster = this.data.get('isEdgeCluster');
            const debounceSearchValue = (this.data.get('debounceSearchValue') || '')
                .trim()
                .toLowerCase();
            let components = this.data.get('components');

            if (isEdgeCluster) {
                components = components.map(e => ({
                    ...e,
                    disabled: !edgeEnableComps.includes(e.name),
                }));
            }
            // 在这里处理一下前端禁用的逻辑
            components.forEach(e => {
                const checkList = [
                    {name: 'cce-log-operator', service: 'BLS'},
                    {name: 'cce-csi-pfsl2-plugin', service: 'PFS'},
                    {name: 'cce-virtual-kubelet', service: 'BCI'},
                ];
                const checkItem = checkList.find(item => item.name === e.name);
                if (
                    checkItem &&
                    e.meta?.installInfo &&
                    window.$context.SERVICE_TYPE[checkItem.service]
                ) {
                    const serviceInfo = window.$context.SERVICE_TYPE[checkItem.service];
                    const regionMap = serviceInfo.region;
                    const region = window.$context.getCurrentRegionId();
                    e.meta.installInfo.allowInstall = !!regionMap[region];
                    e.meta.installInfo.message = !regionMap[region]
                        ? `安装组件所依赖的${serviceInfo.serviceDisplayName}未开通集群所在地域`
                        : '';
                }
            });

            if (active !== 'all') {
                components = components.filter(e => e.type === active);
            }

            // 新建集群时的逻辑
            if (!this.data.get('clusterUuid')) {
                const configList = this.data.get('configList');
                const k8sVersion = this.data.get('k8sVersion');
                const diabledOnCreateClusterComps = this.data.get('diabledOnCreateClusterComps');

                components.forEach(comp => {
                    comp.disabled =
                        comp.disabled ||
                        diabledOnCreateClusterComps.includes(comp.name) ||
                        /^1\.22\./.test(k8sVersion);
                    const selected = configList.some(item => item.name === comp.name);
                    comp.selected = selected;
                    comp.invalid = selected && comp.withParams && !comp.values;
                });
            }

            if (debounceSearchValue) {
                components = components.filter(e => {
                    const name = e?.name?.toLowerCase() || '';
                    const title = e?.title?.toLowerCase() || '';
                    return name === debounceSearchValue || title.includes(debounceSearchValue);
                });
                return components;
            } else {
                return components;
            }
        },
        drawerWitdth() {
            const currentInstallComp = this.data.get('currentInstallComp');
            return [
                'cce-paddleflow',
                'cce-ingress-nginx-controller',
                'cce-node-problem-detector',
                'cce-coredns',
                'cce-csi-pfsl2-plugin',
            ].includes(currentInstallComp)
                ? 900
                : 600;
        },
        selectedComp() {
            const configList = this.data.get('configList');

            return configList.map(e => e.title).join('，') || '-';
        },
        canUpdateCompText() {
            return this.data.get('canUpdateComp')?.join('、');
        },
        // 判断bls日志组件是否安装
        checkLogOperator(name) {
            let components = this.data.get('components');
            const item = components.find(e => e?.name === 'cce-log-operator');
            // 已安装返回false, 未安装返回true
            return !item?.instance;
        },
    };
    initData() {
        return {
            diabledOnCreateClusterComps: ['cce-paddleflow', 'cce-gpu-manager'], // 创建集群时不能安装的组件，因为依赖集群ID
            loading: false,
            compInfoDrawerStatus: false,
            installDrawerStatus: false,
            active: 'all',
            tabs: [
                {label: '全部', key: 'all'},
                {label: '核心组件', key: 'core'},
                {label: '网络', key: 'network'},
                {label: '存储', key: 'storage'},
                {label: '监控和日志', key: 'log'},
                {label: '镜像', key: 'image'},
                {label: '调度', key: 'dispatch'},
                {label: '云原生AI', key: 'ai'},
                {label: '其他', key: 'others'},
            ],
            components: [], // 存接口返回值
            // 存前端显示模板，因为接口中没有title，没有是否需要配置参数
            templates: [
                {
                    name: 'cce-pro-apiserver',
                    title: 'Kube APIServer',
                    type: 'core',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/dlxbsgqz3',
                },
                {
                    name: 'cce-pro-kube-scheduler',
                    title: 'Kube Scheduler',
                    type: 'core',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/7lxbswbxn',
                },
                {
                    name: 'cce-pro-controller-manager',
                    title: 'Kube Controller Manager',
                    type: 'core',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Dlxbucndc',
                },
                {
                    name: 'cce-virtual-kubelet',
                    title: 'CCE Virtual Kubelet',
                    type: 'core',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/km50s8nty',
                },
                {
                    name: 'cce-ingress-controller',
                    title: 'CCE Ingress Controller',
                    type: 'network',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/plarucjor',
                },
                {
                    name: 'cce-ingress-nginx-controller',
                    title: 'CCE Ingress Nginx Controller',
                    type: 'network',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/ylarud6g6',
                },
                // {
                //     name: 'cce-calico-felix',
                //     title: 'CCE Calico Felix',
                //     type: 'network',
                //     doc: 'https://cloud.baidu.com/doc/CCE/s/zlc78mutl',
                // },
                {
                    name: 'node-local-dns',
                    title: 'NodeLocal DNSCache',
                    type: 'network',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Vlo8arcgh',
                },
                {
                    name: 'cce-coredns',
                    title: 'CoreDNS',
                    type: 'network',
                    // 后端有点问题，先屏蔽
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Blto9nx8o',
                },
                {
                    name: 'cce-lb-controller',
                    title: 'CCE LB Controller',
                    type: 'network',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/ilugll8z1',
                },
                {
                    name: 'cce-vpc-native-cni',
                    title: 'CCE Network Plugin',
                    type: 'network',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/0lugoc52h',
                },
                {
                    name: 'cce-network-v2',
                    title: 'CCE Network Plugin',
                    type: 'network',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/0lugoc52h',
                },
                {
                    name: 'cce-csi-bos-plugin',
                    title: 'CCE CSI BOS Plugin',
                    type: 'storage',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/3lc791wdn',
                },
                {
                    name: 'cce-csi-cds-plugin',
                    title: 'CCE CSI CDS Plugin',
                    type: 'storage',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Llc7917cx',
                },
                {
                    name: 'cce-csi-pfs-plugin',
                    title: 'CCE CSI PFS Plugin',
                    type: 'storage',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/slc792j5c',
                },
                {
                    name: 'cce-log-operator',
                    title: 'CCE Log Operator',
                    type: 'log',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Glc8zpk34',
                },
                {
                    name: 'cce-node-problem-detector',
                    title: 'CCE Node Problem Detector',
                    type: 'log',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Qlfq88ert',
                },
                {
                    name: 'cce-node-remedier',
                    title: 'CCE Node Remedier',
                    type: 'log',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/blgkja64g',
                },
                {
                    name: 'cce-onepilot',
                    title: 'CCE Onepilot',
                    type: 'log',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Hm9gikl2q',
                },
                {
                    name: 'cce-image-accelerate',
                    title: 'CCE Image Accelerate',
                    type: 'image',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Slc78q5tw',
                },
                {
                    name: 'cce-p2p-accelerator',
                    title: 'CCE P2P Accelerate',
                    type: 'image',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/slfhveurf',
                },
                {
                    name: 'cce-credential-controller',
                    title: 'CCE Credential Controller',
                    type: 'image',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/4m0kru8g5',
                },
                {
                    name: 'cce-gpu-manager',
                    title: 'CCE GPU Manager',
                    type: 'ai',
                    withParams: true,
                    desc: 'GPU架构感知等高级调度，支持GPU算力和显存的共享与隔离',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/1kp80bcb4',
                },
                {
                    name: 'cce-aibox',
                    title: 'CCE Deep Learning Frameworks Operator',
                    type: 'ai',
                    withParams: false,
                    desc: '提供分布式模型训练、部署等功能，支持TensorFlow、Pytorch、Mxnet、PaddlePaddle框架',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Dkp814hyo',
                },
                {
                    name: 'cce-rdma-plugin',
                    title: 'CCE RDMA Device Plugin',
                    type: 'ai',
                    desc: '高性能实例之间的网络通信，提供高性能网络',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Qkp81mtzj',
                },
                {
                    name: 'cce-volcano',
                    title: 'CCE AI Job Scheduler',
                    type: 'ai',
                    withParams: true,
                    desc: '适配AI训练场景，支持批量调度，队列管理等',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/akp81ro8j',
                },
                {
                    name: 'cce-fluid',
                    title: 'Fluid',
                    type: 'ai',
                    withParams: true,
                    desc: '基于kubernetes原生的开源分布式数据编排和加速引擎,主要用于云原生场景下的大数据、AI应用等',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/gl5jdmkir',
                },
                {
                    name: 'cce-paddleflow',
                    title: 'CCE PaddleFlow Pipeline',
                    type: 'ai',
                    withParams: true,
                    desc: '支持提交工作流任务',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Cl77kb4td',
                },
                {
                    name: 'cce-npu-manager',
                    title: 'CCE NPU Manager',
                    type: 'ai',
                    withParams: false,
                    desc: '一系列NPU Device Plugin的集合，结合配套的Scheduler可以实现复杂场景下的GPU资源调度能力',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/wlc78tiqt',
                },
                {
                    name: 'cce-ascend-mindxdl',
                    title: 'CCE Ascend Mindx DL',
                    type: 'ai',
                    withParams: false,
                    desc: '支持昇腾AI处理器的深度学习组件',
                    doc: 'https://cloud.baidu.com/doc/CCE/s/hlqkk2rtp',
                },
                {
                    name: 'cce-descheduler',
                    title: 'CCE Descheduler',
                    type: 'dispatch',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Ylimqq4mo',
                },
                {
                    name: 'cce-qos-agent',
                    title: 'CCE QoS Agent',
                    type: 'others',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Plnx0oz6p',
                },
                {
                    name: 'cce-cronhpa-controller',
                    title: 'CCE CronHPA Controller',
                    type: 'others',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Hlutkuln9',
                },
                {
                    name: 'cce-backup-controller',
                    title: 'CCE BackUp Controller',
                    type: 'others',
                    withParams: false,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/wm025i813',
                },
                {
                    name: 'cce-csi-pfsl2-plugin',
                    title: 'CCE CSI PFS L2 Plugin',
                    type: 'storage',
                    withParams: true,
                    doc: 'https://cloud.baidu.com/doc/CCE/s/Llwabc982',
                },
            ],
            configList: [], // 创建集群时使用
            canUpdateComp: [], // 可以升级的组件
            $flag,
            ticketUrl: getTicketUrl(),
            confirmDisabled: false,
            searchValue: '',
            debounceSearchValue: '',
        };
    }

    inited() {
        this.checkStsRole();
    }

    async attached() {
        this.initActive();
        await this.filterByWhiteList();
        this.getCompData();
        this.resetCompsByK8s();
        this.initDrawerData();
        this.debounceSearch();
    }

    initActive() {
        const active = this.data.get('route.query.active');
        const tabkeys = this.data.get('tabs').map(e => e.key);
        if (tabkeys.includes(active)) {
            this.data.set('active', active);
        }
    }

    resetCompsByK8s() {
        this.watch('k8sVersion', () => {
            this.data.set('configList', []);
            this.data.set('components', _.cloneDeep(this.data.get('components')));
        });
    }

    initDrawerData() {
        // 监听抽屉弹出、关闭，初始化数据
        this.watch('installDrawerStatus', () => {
            this.data.set('confirmDisabled', false);
        });
    }

    debounceSearch() {
        const handler = _.debounce(data => {
            this.data.set('debounceSearchValue', data);
        }, 500);
        this.watch('searchValue', data => {
            handler(data);
        });
    }

    async filterByWhiteList() {
        let {tabs, templates, masterType, clusterType} = this.data.get();
        const region = window.$context.getCurrentRegionId();

        if ($flag.CceSupportXS) {
            tabs = tabs.filter(e => e.key !== 'log');
            templates = templates.filter(e => e.type !== 'log');
        }

        if (masterType !== MasterType.MANAGEDPRO) {
            if (clusterType === ClusterType.NORMAL) {
                // VK组件在托管集群和独立集群需要展示
                templates = templates.filter(
                    e => e.type !== 'core' || e.name === 'cce-virtual-kubelet',
                );
            } else {
                tabs = tabs.filter(e => e.key !== 'core');
                templates = templates.filter(e => e.type !== 'core');
            }
        }

        // cce-onepilot 仅支持北京
        if (region !== 'bj') {
            templates = templates.filter(e => e.name !== 'cce-onepilot');
        }

        this.data.set('tabs', _.cloneDeep(tabs));
        this.data.set('templates', _.cloneDeep(templates));

        // 创建集群的时候只展示云原生AI组件，且不展示paddleflow
        if (!this.data.get('clusterUuid')) {
            this.data.set('tabs', [{label: '云原生AI', key: 'ai'}]);
            this.data.set('active', 'ai');
            this.data.set(
                'templates',
                templates.filter(e => e.type === 'ai'),
            );
        }
    }

    /**
     * 获取组件数据
     *
     * @param silence 是否静默处理（不显示loading状态）
     * @param refreshNames 是否需要刷新组件名称列表
     * @returns 异步获取组件数据的Promise对象
     */
    async getCompData(silence, refreshNames) {
        const clusterUuid = this.data.get('clusterUuid');
        const isEdgeCluster = this.data.get('isEdgeCluster');
        const segments = [];
        const templates = this.data
            .get('templates')
            .filter(e => !(isEdgeCluster && !edgeSupportComps.includes(e.name)));
        // 存在 refreshNames 参数，则使用 refreshNames 参数请求接口
        if (refreshNames && refreshNames.length) {
            segments.push(refreshNames.join(','));
        } else {
            // 将 names 数组分成六份
            const spliceValue = 6;
            const names = templates.map(e => e.name);
            const segmentSize = Math.ceil(names.length / spliceValue); // 计算每个部分的大小
            for (let i = 0; i < spliceValue; i++) {
                const start = i * segmentSize;
                const end = start + segmentSize;
                segments.push(names.slice(start, end).join(','));
            }
        }

        if (segments.length) {
            if (clusterUuid) {
                silence !== true && this.data.set('loading', true);
                try {
                    // 分别请求六份的数据
                    const requests = segments.map(segment =>
                        this.$http.getCompsData(clusterUuid, segment),
                    );
                    const results = await Promise.all(requests);
                    // 合并六个结果
                    const dataItems = results.reduce((acc, data) => {
                        return acc.concat(data.result.items);
                    }, []);

                    this.data.set('loading', false);
                    if (dataItems) {
                        let components = _.cloneDeep(templates);

                        // 这个两个组件分v1和v2，根据返回值选其一显示
                        const networkPluginComp = {v1: 'cce-vpc-native-cni', v2: 'cce-network-v2'};
                        const networkV1 = dataItems.find(
                            e => e.meta?.name === networkPluginComp.v1,
                        );
                        const networkV2 = dataItems.find(
                            e => e.meta?.name === networkPluginComp.v2,
                        );
                        if (networkV1 && networkV2) {
                            // 这俩谁有instance就显示谁
                            if (networkV2.instance) {
                                components = components.filter(
                                    e => e.name !== networkPluginComp.v1,
                                );
                            } else if (networkV1.instance) {
                                components = components.filter(
                                    e => e.name !== networkPluginComp.v2,
                                );
                            }
                            // 都没有的话优先v2
                            if (!networkV2.instance && !networkV1.instance) {
                                components = components.filter(
                                    e => e.name !== networkPluginComp.v1,
                                );
                            }
                        }

                        if (refreshNames && refreshNames.length) {
                            // 如果是局部刷新，就用已有的数据
                            components = _.cloneDeep(this.data.get('components'));
                        }

                        let isReloadNames = []; // 是否有loading中的，5秒后再刷一次
                        const canUpdateComp = [];
                        dataItems.forEach(item => {
                            const target = components.find(e => e.name === item?.meta?.name);
                            if (target) {
                                target.meta = item?.meta;
                                if (item?.instance) {
                                    target.instance = item.instance;
                                    if (
                                        [
                                            CompStatus.Installing,
                                            CompStatus.Uninstalling,
                                            CompStatus.Upgrading,
                                            CompStatus.Deleting,
                                        ].includes(item.instance.status?.phase)
                                    ) {
                                        target.status = 'Loading'; // 展示转圈并持续调用接口
                                        isReloadNames.push(target.name);
                                    } else {
                                        target.status = null;
                                    }

                                    if (
                                        item.instance.installedVersion &&
                                        item?.meta?.latestVersion
                                    ) {
                                        if (
                                            item.instance.installedVersion ===
                                            item.meta.latestVersion
                                        ) {
                                            target.isLatest = true;
                                        } else {
                                            // 不是最新版本则提示可升级
                                            canUpdateComp.push(target.title);
                                        }
                                    } else {
                                        target.isLatest = true; // 没有明确当前和最新的就当最新的
                                    }
                                } else {
                                    target.status = null;
                                    target.instance = null;
                                }
                            }
                        });
                        this.data.set('components', components);
                        if (isReloadNames.length) {
                            const timer = setTimeout(() => {
                                this.getCompData(true, isReloadNames); // 静默刷新
                            }, 5000);
                            timer = null;
                        }
                        this.data.set('canUpdateComp', canUpdateComp);
                    }
                } catch (error) {
                    this.data.set('loading', false);
                }
            } else {
                const components = _.cloneDeep(this.data.get('templates'));
                this.data.set('components', components);
            }
        }
    }

    showCompInfo(name) {
        const target = this.data.get('templates').find(e => e.name === name);
        const compInfoDrawerContent = target?.meta?.detailIntroduction;
        this.data.set('compInfoDrawerContent', compInfoDrawerContent);
        this.data.set('compInfoDrawerStatus', true);
        this.data.set('compInfoDrawerTitle', target?.title);
    }

    setItemLoading(compName) {
        const components = _.cloneDeep(this.data.get('components'));
        const target = components.find(e => e.name === compName);
        target.status = 'Loading';
        this.data.set('components', components);
    }
    // coreDns安装bls日志组件
    async instanllBlsLogOperator(comp) {
        const clusterUuid = this.data.get('clusterUuid');
        const item = this.data.get('components').find(e => e?.name === 'cce-log-operator');
        await Dialog.confirm({
            content: `确定安装${item.title}组件吗?`,
        });
        const params = {
            name: item.name,
        };
        try {
            this.setItemLoading(comp.name);
            this.setItemLoading(item.name);
            await this.$http.installComp(clusterUuid, params);
            Notification.success('安装成功');
        } catch (error) {}
        this.getCompData(true, [comp.name, item.name]);
    }

    async instanll(comp) {
        // 安装组件
        const clusterUuid = this.data.get('clusterUuid');
        if (!comp.withParams) {
            const contentMap = {
                'cce-rdma-plugin': rdmaInstallTip,
            };
            let okText = '确认';
            let title = '提示';
            // 安装vk组件，需要关联开通bci服务
            if (!this.data.get('bciStsRole') && comp.name === 'cce-virtual-kubelet') {
                title = '关联开通容器实例（BCI）服务';
                okText = '开通BCI服务 并 安装CCE Virtual Kublet组件';
                contentMap['cce-virtual-kubelet'] =
                    '首次使用容器实例（BCI）前需要您开通服务并授权相关权限： 包括访问您的镜像仓库以拉取容器镜像，访问日志服务以存储您的容器日志，以及其他必要的服务访问权限。';
            }
            await Dialog.confirm({
                okText,
                title,
                content: contentMap[comp.name] || `确定安装${comp.title}组件吗?`,
            });
            const params = {
                name: comp.name,
            };
            try {
                this.setItemLoading(comp.name);
                // cce-virtual-kubelet组件，需要内置一些参数
                if (comp.name === 'cce-virtual-kubelet') {
                    !this.data.get('bciStsRole') && (await this.openStsRole());
                    await this.initCompParams(params);
                }
                await this.$http.installComp(clusterUuid, params);
                Notification.success('安装成功');
                comp.name === 'cce-backup-controller' && this.fire('changeBackCompStatus', true);
            } catch (error) {}
            this.getCompData(true, [comp.name]);
        } else {
            this.data.set('currentInstallComp', comp.name);
            this.data.set('installDrawerStatus', true);
            this.data.set('installDrawerEdit', false);
            this.data.set('isView', false);
            this.data.set('isUpgrade', false);
            this.data.set('installDrawerTitle', '安装组件' + comp.title);
        }
    }

    redirectInstance(comp) {
        // 区分下，cce-paddleflow 是安装在 paddleflow命名空间下的，其他的组件安装在 kube-system 下
        const nameUrlMap = {
            'cce-paddleflow': 'paddleflow',
            'cce-backup-controller': 'backupController',
        };
        redirect(
            `#/cce/helm/instance/list?keywordType=${nameUrlMap[comp.name] || 'ChartName'}&keyword=${
                comp.name
            }`,
        );
    }
    redirectMonitor(folder = '控制面组件监控', dashboard = 'APIserver监控') {
        const clusterUuid = this.data.get('clusterUuid');
        redirect(
            `#/cce/monitor/container?clusterUuid=${clusterUuid}&&folder=${folder}&dashboard=${dashboard}`,
        );
    }
    redirectComponentLogs(logcenterType, comp) {
        const clusterUuid = this.data.get('clusterUuid');
        let url = `#/cce/monitor/log/center?clusterUuid=${clusterUuid}&logcenterType=${logcenterType}`;
        if (logcenterType === 'control' && comp) {
            url += `&componentName=${comp.name}`;
        }
        redirect(url);
    }
    cancelInstall() {
        this.data.set('installDrawerStatus', false);
    }
    async onConfirm() {
        // 查看配置则直接返回
        if (this.data.get('isView') && !this.data.get('isUpgrade')) {
            this.data.set('installDrawerStatus', false);
            return;
        }

        const clusterUuid = this.data.get('clusterUuid');
        const currentInstallComp = this.data.get('currentInstallComp');
        const compRef = this.ref(currentInstallComp);
        await compRef.validateForm();
        const yaml = compRef.getFormData();

        if (clusterUuid) {
            this.data.set('comfirming', true);
            try {
                // 升级
                if (this.data.get('isView') && this.data.get('isUpgrade')) {
                    await this.upgrade(currentInstallComp, yaml);
                } else {
                    const installDrawerEdit = this.data.get('installDrawerEdit');

                    if (installDrawerEdit) {
                        await this.$http.updateComp(clusterUuid, {
                            name: currentInstallComp,
                            params: yaml,
                        });
                    } else {
                        await this.$http.installComp(clusterUuid, {
                            name: currentInstallComp,
                            params: yaml,
                        });
                    }

                    Notification.success(installDrawerEdit ? '修改成功' : '安装成功');
                    this.setItemLoading(currentInstallComp);
                    this.getCompData(true, [currentInstallComp]);
                }

                this.data.set('comfirming', false);
                this.data.set('installDrawerStatus', false);
            } catch (error) {
                this.data.set('comfirming', false);
            }
        } else {
            this.data.set('installDrawerStatus', false);
            const configList = this.data.get('configList');
            const components = this.data.get('components');
            const target = configList.find(e => e.name === currentInstallComp);
            if (target) {
                target.values = yaml;
            }
            const _target = components.find(e => e.name === currentInstallComp);
            if (_target) {
                _target.values = yaml;
                _target.invalid = false;
            }
            this.data.set('configList', _.cloneDeep(configList));
            this.data.set('components', _.cloneDeep(components));
        }
    }

    async uninstanll(comp) {
        const clusterUuid = this.data.get('clusterUuid');
        const logUnInstallTip = defineComponent({
            template: html`
                <template>
                    <div>确定卸载${comp.title}组件吗?</div>
                    <div>
                        卸载组件不会删除对应的日志集和日志信息，如需删除请前往<a
                            href="https://console.bce.baidu.com/bls/#/bls/log/list"
                            target="_blank"
                            >日志服务控制台</a
                        >进行删除。
                    </div>
                </template>
            `,
        });
        const credentialUnInstallTip = defineComponent({
            template: html`
                <template>
                    <div>确定卸载${comp.title}组件吗?</div>
                    <div>
                        卸载组件后，原镜像访问凭据会在24小时后自动过期，已有自动关联的工作负载将无法获取镜像，请谨慎操作。
                        如您需要手动配置镜像拉取的secret ，请<a
                            href="https://cloud.baidu.com/doc/CCE/s/4jxppg756"
                            target="_blank"
                            >参考文档</a
                        >。
                    </div>
                </template>
            `,
        });
        const unInstallTipMap = {
            'cce-log-operator': logUnInstallTip,
            'cce-credential-controller': credentialUnInstallTip,
        };
        const content = unInstallTipMap[comp.name] || `确定卸载${comp.title}组件吗?`;
        await Dialog.confirm({
            content,
        });
        const params = {
            name: comp.name,
        };
        try {
            this.setItemLoading(comp.name);
            await this.$http.uninstallComp(clusterUuid, params);
            Notification.success('卸载成功');
            comp.name === 'cce-backup-controller' && this.fire('changeBackCompStatus', false);
        } catch (error) {}
        this.getCompData(true, [comp.name]);
    }

    /**
     * 打开或关闭组件日志
     *
     * @param comp 组件对象
     * @param type 是否开启，true为开启，false为关闭
     * @returns Promise
     */
    async openLog(comp, type) {
        const clusterUuid = this.data.get('clusterUuid');
        const constext = type ? '开启' : '关闭';
        const closeText = `关闭组件不会删除对应的日志集及组件日志，但是不支持继续将${comp.title}上报到日志服务BLS中。`;
        await Dialog.warning({
            title: `${comp.title}${constext}日志`,
            content: `请确认是否${constext}组件日志？${type ? '' : closeText}`,
        });
        const params = {
            addonName: comp.name,
            enable: type,
        };
        try {
            this.setItemLoading(comp.name);
            await this.$http.updateCompLogStatus(clusterUuid, params);
            Notification.success(`${constext}成功`);
        } catch (error) {}
        this.getCompData(true, [comp.name]);
    }

    async onUpgrade(comp) {
        // 升级组件
        // 非云原生AI组件升级，有参数的组件需要打开参数配置面板回显并完成提交进行升级
        // AI组件cce-gpu-manager 自定义逻辑升级，其他AI组件仅提示是否升级，不用打开参数面板

        // 特殊逻辑：GPU Manage 组件升级时需检查当前版本参数
        if (comp.name === 'cce-gpu-manager') {
            // 从 values 接口获取参数
            const data = await this.getHelmValues('cce-gpu-manager');

            if (!data) {
                Notification.error('获取组件参数失败，请重试');
                return;
            }

            const valid = this.checkGPUManagerUpgradeParams(data);

            if (!valid) {
                // 包含无效参数时，弹出右侧抽屉展示相关参数
                this.view(comp, true, data);

                return;
            }
        }

        const updateMessageContent = defineComponent({
            template: html` <div>
                <s-alert skin="warning" showIcon="{{false}}">
                    <div slot="description">
                        <div>
                            1、组件版本将由 ${comp.instance?.installedVersion} 升级至
                            ${comp.instance?.upgradeInfo?.nextVersion}。
                        </div>
                        <div>
                            2、建议您升级前通过版本迭代记录查看变更内容和影响，具体信息请见
                            <a href="${comp.doc}" target="_blank">组件介绍</a>。
                        </div>
                        <div>
                            3、您通过其他途径（如kubectl)修改的参数配置将会被原先的系统默认值覆盖，需要升级后重新设置。
                        </div>
                        <div s-if="{{comp.name === 'cce-gpu-manager'}}">
                            4、升级完成后，集群组件会自动更新至最新版本，开启GPU显存共享的节点不会立即更新，需参考
                            <a href="${comp.doc}" target="_blank">组件介绍</a>
                            文档进行手动排空升级。
                        </div>
                    </div>
                </s-alert>
                <p>您确定升级【${comp.title}】组件吗?</p>
            </div>`,
            components: {
                's-alert': Alert,
            },
        });
        if (comp.type === 'ai') {
            // ai组件
            await Dialog.confirm({
                title: '升级确认',
                content: updateMessageContent,
            });
            this.upgrade(comp.name);
        } else {
            // 有参数组件
            if (comp.withParams) {
                this.data.set('currentInstallComp', comp.name);
                this.data.set('installDrawerStatus', true);
                this.data.set('installDrawerEdit', false);
                this.data.set('isView', true);
                this.data.set('isUpgrade', true);
                this.data.set('installDrawerTitle', '升级组件' + comp.title);
                this.nextTick(() => {
                    this.ref(comp.name)?.initFormData(comp);
                });
            } else {
                // 无参数组件
                await Dialog.confirm({
                    title: '升级确认',
                    content: updateMessageContent,
                });
                this.upgrade(comp.name);
            }
        }
    }

    async upgrade(compName, params) {
        const clusterUuid = this.data.get('clusterUuid');
        const payload = {
            name: compName,
        };
        // cce-virtual-kubelet组件，需要内置一些参数
        if (compName === 'cce-virtual-kubelet') {
            await this.initCompParams(payload);
        }

        if (params) {
            payload.params = params;
        }

        try {
            this.setItemLoading(compName);
            await this.$http.upgradeComp(clusterUuid, payload);
            Notification.success('升级成功');
        } catch (error) {}

        this.getCompData(true, [compName]);
    }

    async update(comp) {
        // 修改配置
        this.data.set('currentInstallComp', comp.name);
        this.data.set('installDrawerStatus', true);
        this.data.set('installDrawerEdit', true);
        this.data.set('isView', false);
        this.data.set('installDrawerTitle', '修改组件' + comp.title);
        this.nextTick(() => {
            this.ref(comp.name)?.initFormData(comp);
        });
    }

    view(comp, isUpgrade = false, compData) {
        // 查看配置
        this.data.set('currentInstallComp', comp.name);
        this.data.set('installDrawerStatus', true);
        this.data.set('installDrawerEdit', false);
        this.data.set('isView', true);
        this.data.set('isUpgrade', isUpgrade);
        this.data.set('installDrawerTitle', comp.title);

        if (isUpgrade && comp.name === 'cce-gpu-manager') {
            this.nextTick(() => {
                this.ref(comp.name)?.initFormData(compData);
            });
        }
        if (comp.name === 'cce-csi-pfsl2-plugin') {
            this.nextTick(() => {
                this.ref(comp.name)?.initFormData(comp);
            });
        }
    }

    checkGPUManagerUpgradeParams(data) {
        // 参数均有效时，返回 true
        if (
            ['GiB', 'MiB'].includes(data.GPUShareMemoryUnit) &&
            // 隔离性最优型 GPU显存共享单位只能选 GiB
            !(data.EnableSGPU === GpuEnableSGPUType.Quarantine && data.GPUShareMemoryUnit === 'MiB')
        ) {
            return true;
        }

        return false;
    }

    check(comp) {
        const clusterUuid = this.data.get('clusterUuid');
        const diabledOnCreateClusterComps = this.data.get('diabledOnCreateClusterComps');
        if (!clusterUuid) {
            const k8sVersion = this.data.get('k8sVersion');
            if (diabledOnCreateClusterComps.includes(comp.name) || /^1\.22\./.test(k8sVersion)) {
                return;
            }
            const configList = this.data.get('configList');
            const target = configList.find(e => e.name === comp.name);
            if (target) {
                this.data.set(
                    'configList',
                    configList.filter(e => e.name !== comp.name),
                );
            } else {
                configList.push({
                    name: comp.name,
                    title: comp.title,
                    withParams: comp.withParams,
                });
                this.data.set('configList', _.cloneDeep(configList));
            }
            this.data.set('components', _.cloneDeep(this.data.get('components')));
        }
    }

    config(e, comp) {
        // 配置
        const clusterUuid = this.data.get('clusterUuid');
        if (!clusterUuid) {
            e.stopPropagation();
            const clusterUuid = this.data.get('clusterUuid');
            if (!clusterUuid && comp.withParams) {
                this.data.set('currentInstallComp', comp.name);
                this.data.set('installDrawerStatus', true);
                this.data.set('installDrawerEdit', false);
                this.data.set('isView', false);
                this.data.set('installDrawerTitle', comp.title);
            }
        }
    }

    getFormData() {
        const clusterUuid = this.data.get('clusterUuid');
        if (!clusterUuid) {
            const configList = this.data.get('configList');
            const plugins = configList.map(e => e.name);
            const pluginsConfig = {};
            configList.forEach(comp => {
                pluginsConfig[comp.name] = {
                    namespaces: comp.name === 'cce-paddleflow' ? 'paddleflow' : 'kube-system',
                    values: comp.values || null,
                };
            });
            return {
                plugins,
                pluginsConfig,
            };
        } else {
            return {};
        }
    }
    async validateForm() {
        const clusterUuid = this.data.get('clusterUuid');
        if (!clusterUuid) {
            const configList = this.data.get('configList');
            if (configList.length) {
                const target = configList.find(e => e.withParams && !e.values);
                if (target) {
                    Notification.error(`请配置已选择组件${target.name}的参数`);
                    return Promise.reject();
                } else {
                    return Promise.resolve();
                }
            } else {
                return Promise.resolve();
            }
        } else {
            return Promise.resolve();
        }
    }

    getHelmValues(name, namespace = 'kube-system', all = 'true') {
        return this.$http
            .getInstanceRevisionValue(
                {
                    clusterUuid: this.data.get('clusterUuid'),
                    name,
                    namespace,
                    all,
                },
                {'x-silent': true},
            )
            .then(res => {
                const data = jsyaml.load(res?.result?.data);

                return data;
            })
            .catch(err => {
                return null;
            });
    }

    confirmDisabledChange(value) {
        this.data.set('confirmDisabled', value);
    }
    async initCompParams(params) {
        const paramsData = {};
        const clusterDetail = this.data.get('clusterDetail');
        const clusterDetailV2 = this.data.get('clusterDetailV2');
        const cniMode = _.get(clusterDetail, '.advancedOptions.cniMode', 'kubenet');
        const securityGroupId = _.get(clusterDetailV2, '.spec.nodeDefaultSecurityGroups[0].id', '');
        if (NetworkModeVpc.getTextFromValue(cniMode)) {
            const subnetList = await this.$http.getSubnetList({
                vpcId: clusterDetail.vpcId,
                subnetTypes: [1],
            });
            if (!subnetList?.result?.length) {
                Notification.error('当前VPC中无可用子网');
                return Promise.reject();
            }
            if (!securityGroupId) {
                Notification.error('当前集群未配置默认安全组');
                return Promise.reject();
            }
            paramsData.bci = {
                subnets: subnetList.result.map(e => ({
                    zone: e.az,
                    subnetID: e.shortId,
                })),
                securityGroupID: securityGroupId,
            };
        }
        params.params = jsyaml.safeDump(paramsData);
    }
    checkStsRole() {
        const AllRegion = window.$context.getEnum('AllRegion');
        return this.$http
            .checkStsRole({roleName: 'BceServiceRole_bci'}, {region: AllRegion.BJ})
            .then(res => {
                const data = res.result;
                if (data && data.id) {
                    this.data.set('bciStsRole', true);
                } else {
                    this.data.set('bciStsRole', false);
                }
            })
            .catch(() => this.data.set('bciStsRole', false));
    }
    openStsRole() {
        return this.$http
            .openIamService(
                {
                    ...STS_ROLE_BCI_PARAM[window.$context.isOnline() ? 'online' : 'offline'],
                    accountId: window.$context.getUserId(),
                },
                {region: 'bj'},
            )
            .then(res => {
                this.data.set('bciStsRole', true);
            });
    }
}
