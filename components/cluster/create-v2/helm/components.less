.cce-helm-components {
    padding: 14px 20px;
    display: block;
    .s-loading.helm {
        width: calc(~'100% - 40px');
        height: 100%;
        position: absolute;
        z-index: 9999;
        .s-loading-wrap {
            position: absolute;
            top: 15%;
            left: 48%;
        }
    }
    .s-alert-skin-warning {
        padding: 10px;
        height: auto;
    }
    .s-tabnav-scroll {
        .s-tabnav-nav {
            border-bottom: none !important;
        }
    }
    .s-tabs-line.s-tabs-horizontal > .s-tabnav .s-tabnav-nav-item {
        margin-right: 26px;
    }
    .can-update-comp {
        color: #30bf13;
    }
    .action-bar {
        position: relative;
        .refresh-comp {
            position: absolute;
            right: 0px;
            top: 6px;
            z-index: 999;
            padding: 0 8px;
        }
        .comp-search {
            position: absolute;
            right: 40px;
            top: 6px;
            z-index: 999;
        }
    }

    .helm-card-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .empty {
            width: 100%;
            text-align: center;
            margin-top: 100px;
        }
        .helm-comp-item {
            flex-basis: calc(~'(50% - 10px)');
            flex-shrink: 0;
            flex-grow: 0;
            border: 1px solid #ebebeb;
            margin-bottom: 20px;
            overflow: hidden;
            position: relative;

            .title {
                line-height: 40px;
                padding: 0 14px;
                background-color: #f5f5f5;
                font-size: 14px;
                cursor: pointer;
                .status {
                    float: right;
                    line-height: 40px;
                }
                .warning-tip {
                    float: right;
                    .s-icon {
                        width: 18px;
                    }
                }
            }
            .desc {
                padding: 0 14px;
                height: 40px;
                line-height: 40px;
                margin-top: 10px;
                text-indent: 0px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .doc-link {
                padding-left: 14px;
            }
            .version {
                float: left;
                line-height: 40px;
                margin-left: 14px;
                .update-version {
                    color: green;
                    margin-left: 10px;
                }
            }
            .operation {
                float: right;
                line-height: 40px;
                margin-right: 14px;
                .s-button {
                    padding: 0;
                    margin-left: 10px;
                }
                .more-operation-btn {
                    line-height: 20px;
                    button {
                        transform: scale(1.2);
                        font-weight: bold;
                    }
                }
            }
            .helm-status {
                float: right;
                font-size: 13px;
                line-height: 20px;
                margin-top: 10px;
                color: white;
                padding: 0px 8px;
                border-radius: 10px;
                &.success {
                    background-color: #30bf13;
                }
                &.warning {
                    background-color: #ffa500;
                }
                &.normal {
                    background-color: #0066ff;
                }
                &.error {
                    background-color: #f33e3e;
                }
                &.partial {
                    background-color: #f8d7a3;
                }
                &.unknown {
                    background-color: #bebebe;
                    color: black;
                }
            }
            .is-manager {
                background-color: #30bf13;
                color: white;
                font-size: 13px;
                padding: 3px 10px;
                border-radius: 4px;
                margin-left: 4px;
            }
            .helm-loading {
                margin-top: 10px;
            }
        }
    }
}

// 创建集群时的组件安装
.create-helm-wrap {
    .cce-helm-components {
        .s-tabs-horizontal > .s-tabnav .s-tabnav-nav-item {
            font-size: 12px;
        }
        .s-tabnav-scroll {
            .s-tabnav-nav {
                border-bottom: 1px solid #d4d6d9 !important;
            }
        }
        .helm-card-list {
            .helm-comp-item {
                flex-basis: 366px;
                border: 1px solid #d4d6d9;
                border-radius: 6px;
                padding-bottom: 16px;
                margin-bottom: 16px;

                .title {
                    padding: 0 16px;
                    display: flex;
                    align-items: center;
                }

                .desc {
                    padding: 0 16px;
                    margin-top: 12px;
                    height: 44px;
                    line-height: 22px;
                    color: #5c5f66;
                    white-space: normal;
                }

                .doc-link {
                    padding-left: 16px;
                    margin-top: 8px;
                    display: flex;
                    align-items: center;
                }

                &.disabled {
                    background: #f7f7f9;
                    border: 1px solid #d4d6d9;
                    border-radius: 6px;

                    .title {
                        background: #dedede;
                        cursor: not-allowed;
                    }

                    .title,
                    .desc {
                        color: #b8babf;
                    }
                }

                &.selected {
                    border-color: #2468f2;

                    &.invalid {
                        border-color: #ff3c3f;
                    }
                }
            }
        }

        .selected-comp {
            label {
                color: #5c5f66;
            }

            span {
                margin-left: 10px;
            }
        }
    }
}

.install-drawer {
    .s-drawer-content {
        position: relative;
        padding-bottom: 0px !important;
        height: calc(~'100% - 56px') !important;
        display: flex;
        flex-direction: column;

        .component-params {
            flex: 1;
            overflow: auto;
            .s-icon-button {
                padding: 0 7px;
                margin-left: 5px;
                svg {
                    position: relative;
                    top: -1px;
                }
            }
        }
        .operate {
            text-align: right;
            border-top: 1px solid #ccc;
            padding: 10px 20px 0 0;
            height: 50px;
            background: #fff;
            margin: 0 -24px;
        }
        .helm-stroage {
            .s-form-item {
                margin-bottom: 24px;
            }
            .helm-stroage-help {
                width: 400px;
                padding-top: 4px;
                color: #84868c;
            }
            .s-form-item-label > label {
                padding-left: 7px;
            }
            .default-target-delete {
                line-height: 32px;
                color: #b8babf;
                text-decoration: line-through;
            }
            .default-target-info {
                line-height: 32px;
            }
            .default-target-disabled {
                line-height: 32px;
                color: #b8babf;
            }
        }
        .helm-stroage-alert {
            padding: 6px 16px;
            margin-bottom: 24px;
        }
        .p2p-image {
            .s-form-item {
                margin-bottom: 20px;
            }
            .s-form-item-label {
                width: 80px;
            }
            .s-form-item-help {
                width: 400px;
            }
        }
        .gpu-config {
            .s-alert {
                margin-bottom: 20px;
            }
            .s-form-item {
                margin-bottom: 20px;
            }
            .s-form-item-label {
                width: 120px;
                text-align: left;
            }
            .s-form-item-control-wrapper {
                margin-top: 4px;
            }
            .s-radio {
                margin-right: 30px;
            }
            .s-tip {
                vertical-align: text-bottom;
            }
        }
        .deep-learning {
            .s-form-item-label {
                width: 80px;
                text-align: left;
            }
            .s-checkbox {
                display: block;
                margin-top: 10px;
            }
            .s-tip {
                vertical-align: text-bottom;
            }

            .form-item-fault-tolerant > .s-row-flex {
                align-items: center;
            }
        }
        .volcano {
            .s-alert-desc-content {
                color: #ffa500;
            }
            .s-form-item-label {
                width: 100px;
                text-align: left;
            }
            .s-radio {
                margin-right: 30px;
                margin-top: 6px;
            }
            .s-checkbox {
                margin-top: 10px;
                margin-bottom: 30px;
                margin-right: 20px;
            }
            .s-form-item .s-row-flex {
                flex-wrap: nowrap;
            }
            .s-form-item-control-wrapper {
                flex: 1;
            }
            .job-mod {
                background-color: #f7f7f9;
                padding: 8px 8px 12px;

                &:last-child {
                    margin-top: 20px;
                }

                .s-form-item-help:last-child {
                    padding-bottom: 0;
                }

                .s-radio-group {
                    margin-right: 0;
                    margin-left: 24px;
                }

                .s-radio {
                    margin-right: 0;
                }
            }
        }
        .npu-config {
            .s-alert {
                margin-bottom: 20px;
            }
            .s-form-item-label {
                width: 100px;
                text-align: left;
            }
            .s-radio {
                margin-right: 30px;
                margin-top: 4px;
            }
            .s-tip {
                vertical-align: text-bottom;
            }
        }
        .paddleflow {
            .s-form-item-label {
                width: 100px;
                text-align: left;
            }
            .datatype-raido {
                margin-top: 6px;
                .s-radio {
                    margin-right: 20px;
                }
            }
            .service-tip {
                position: absolute;
                right: -20px;
                top: 6px;
            }
            .paddleflow-tip {
                position: absolute;
                right: 0;
                top: 6px;
            }
            .net-msg {
                margin-bottom: 10px;
                color: #f38900;
            }
            .net-item {
                margin-bottom: 20px;
                .net-item-label {
                    display: inline-block;
                    width: 100px;
                }
            }
            .s-table {
                width: 700px;
            }
            .required-item {
                .s-form-item-label {
                    &::before {
                        content: '*';
                        color: #f97878;
                    }
                }
                &.error {
                    .s-input {
                        // border-color: #f97878;
                    }
                }
            }
        }
        .cce-cluster-helm-ingress-config {
            height: calc(~'100% - 60px');
            overflow: auto;
            .s-alert {
                height: auto;
            }
            .s-form-item-label {
                text-align: left;
            }
            .bui-form .bui-form-item-inline .bui-form-item-label {
                width: 140px;
            }
            .cce-add-tag {
                & > .bui-form-item-inline > .bui-form-item-content {
                    width: 700px;
                }
            }
            .container > .s-row-flex > .s-form-item-control-wrapper {
                width: 700px;
            }
            .eip-config-panel {
                .bui-form-item-label {
                    width: 140px !important;
                }
                .multi-line-item {
                    margin-left: 0;
                    margin-top: 30px;
                }
            }
        }
        .descheduler-config-form {
            .s-form-item-label {
                width: 100px;
                text-align: left;
            }
            .prometheus-address .s-form-item-control-wrapper {
                flex: 1;
            }
            .scheduling-strategy-item {
                .scheduling-strategy-wrap {
                    padding: 10px;
                    background: #f2f2f2;
                    & > .s-row {
                        margin-left: 0 !important;
                        margin-bottom: 10px;
                        &:last-of-type {
                            margin: 0;
                        }
                    }
                    .scheduling-strategy-label {
                        height: 32px;
                        line-height: 32px;
                        color: #5c5f66;
                    }
                    .tips {
                        color: #84868c;
                    }
                }
                .s-form-item {
                    margin-bottom: 0;
                }
                .s-form-item-control-wrapper {
                    flex: 1;
                }
                .s-input {
                    background: #fff;
                    .s-input-prefix {
                        white-space: nowrap;
                    }
                }
                .align-items-center {
                    display: flex;
                    align-items: center;
                }
            }
        }

        .halolet-form {
            margin-top: 18px;

            .enable-priority {
                .s-form-item-control {
                    padding-top: 6px;
                }
            }
        }
    }
}
.comp-btn-wrap {
    width: 90px;
    padding: 4px 0;
    border-radius: 4px;
    -webkit-box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
    box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
    background-color: #fff;
    .s-button {
        margin: 3px 0;
    }
    .cce-cluster-helm-ingress-tip {
        margin-left: 2px;
    }
}

.gpu-manager-upgrade-alert {
    width: 450px;
}
