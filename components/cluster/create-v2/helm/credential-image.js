import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {Form, Input, Select, Link, Radio, Alert, Button} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import jsyaml from 'js-yaml';
const $flag = ServiceFactory.resolve('$flag');

const template = html`<template>
    <s-alert skin="warning" s-if="installDrawerEdit" class="top-alert">
        更新组件参数配置，自动创建的访问凭据存在24小时自动过期风险。
    </s-alert>
    <s-form
        class="credential-image"
        s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-align="left"
    >
        <s-form-item prop="eccrInstanceIDs" label="关联实例：">
            <div slot="help">
                仅支持同一地域下的CCR实例<s-link
                    skin="primary"
                    href="/ccr/#/ccr/company/instance/list"
                    target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                >
                    <span>去创建CCR企业版实例</span>
                </s-link>
            </div>
            <s-select
                width="380"
                datasource="{{instanceList}}"
                value="{=formData.eccrInstanceIDs=}"
            />
            <s-button class="s-icon-button ml-10" on-click="getCCRInstanceList"
                ><s-icon-refresh class="button-icon" is-button="{{false}}"
            /></s-button>
        </s-form-item>
        <div
            class="error-tip"
            s-if="{{installDrawerEdit && formData.eccrInstanceIDs && formData.eccrInstanceIDs !== defaultInstanceId}}"
        >
            更改关联实例后，原关联实例的镜像访问凭据会在24小时后自动过期，已有自动关联的工作负载将无法获取镜像，请谨慎操作。
        </div>
        <s-form-item label="免密配置:" class="scheduling-strategy-item">
            <s-alert skin="warning" class="memory-containe-alert">
                注意：配置后请不要在工作负载YAML中手动配置ImagePullSecret，否则将无法自动使用此访问凭证
            </s-alert>
            <div class="memory-container">
                <s-form-item prop="autoCredential" label="访问凭据：">
                    <s-radio-group value="{=formData.autoCredential=}">
                        <s-radio value="auto">自动创建访问凭据 </s-radio>
                        <s-radio value="custom"
                            >指定访问凭据
                            <s-tip
                                class="inline-tip"
                                skin="question"
                                placement="top"
                                content="系统将使用指定用户的访问凭据，请确保用户及访问凭据持续有效，否则无法拉取镜像"
                        /></s-radio>
                    </s-radio-group>
                </s-form-item>
                <div s-if="formData.autoCredential === 'custom'" class="custom-credentials">
                    <s-form-item prop="username" label="">
                        <s-input
                            width="140"
                            placeholder="请输入用户名"
                            value="{=formData.username=}"
                        />
                    </s-form-item>
                    <s-form-item prop="password" label="">
                        <s-input
                            type="password"
                            placeholder="请输入密码"
                            width="140"
                            autocomplete="new-password"
                            showPasswordIcon
                            value="{=formData.password=}"
                        />
                    </s-form-item>
                </div>
                <s-form-item
                    prop="namespaces"
                    label="命名空间："
                    help="默认为集群全部命名空间，用星号表示，支持指定命名空间，多个用英文逗号“,”分隔"
                >
                    <s-input width="310" value="{=formData.namespaces=}" />
                </s-form-item>
                <s-form-item
                    prop="serviceAccounts"
                    label="ServiceAccount："
                    help="默认为命名空间下全部ServiceAccount，用星号表示，指定多个ServiceAccount用英文逗号“,”分隔"
                >
                    <s-input width="310" value="{=formData.serviceAccounts=}" />
                </s-form-item>
            </div>
        </s-form-item>
    </s-form>
</template>`;

@decorators.asComponent('@credential-image')
export default class CredentialImage extends Component {
    static template = template;
    static components = {
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-link': Link,
        's-tip': Tip,
        's-alert': Alert,
        's-button': Button,
        's-icon-refresh': OutlinedRefresh,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
    };
    initData() {
        return {
            $flag,
            instanceList: [],
            installDrawerEdit: false,
            defaultInstanceId: '',
            formData: {
                autoCredential: 'auto',
                eccrInstanceIDs: '',
                namespaces: '*',
                serviceAccounts: '*',
            },
            rules: {
                eccrInstanceIDs: [{required: true, message: '请关联CCR企业版实例'}],
                namespaces: [
                    {required: true, message: '请输入命名空间'},
                    {
                        pattern:
                            /^(\*|([a-z0-9]([-a-z0-9]*[a-z0-9])?)(,([a-z0-9]([-a-z0-9]*[a-z0-9])?))*)$/,
                        message: '格式不符合规则，请重新输入',
                    },
                ],
                serviceAccounts: [
                    {required: true, message: '请输入ServiceAccount'},
                    {
                        pattern:
                            /^(\*|([a-z0-9]([-a-z0-9]*[a-z0-9])?)(,([a-z0-9]([-a-z0-9]*[a-z0-9])?))*)$/,
                        message: '格式不符合规则，请重新输入',
                    },
                ],
                username: [{required: true, message: '请输入用户名'}],
                password: [{required: true, message: '请输入密码'}],
            },
        };
    }
    inited() {
        this.getCCRInstanceList();
    }
    async getCCRInstanceList() {
        const {result} = await this.$http.getCCRInstanceList({pageSize: 100});
        const instances = result?.instances || [];
        if (instances.length > 0) {
            this.data.set(
                'instanceList',
                instances.map(item => ({
                    value: item.id,
                    text: `${item.name}/${item.id}`,
                })),
            );
            this.nextTick(() => {
                const defaultInstance =
                    this.data.get('defaultInstanceId') || instances[0]?.id || '';
                this.data.set('formData.eccrInstanceIDs', defaultInstance);
            });
        }
    }
    initFormData(comp) {
        const data = jsyaml.load(comp.instance.params);
        const config = data?.config || {};
        const formData = {
            autoCredential: config.autoCredential ? 'auto' : 'custom',
            namespaces: config.namespaces,
            serviceAccounts: config.serviceAccounts,
        };

        if (
            config.autoCredential === false &&
            config.eccrInstanceInfos &&
            config.eccrInstanceInfos.length
        ) {
            formData.username = config.eccrInstanceInfos[0].username;
            formData.password = config.eccrInstanceInfos[0].password;
        }
        if (config?.eccrInstanceInfos && config?.eccrInstanceInfos[0]?.instanceID) {
            this.data.set('defaultInstanceId', config.eccrInstanceInfos[0].instanceID);
        }

        this.data.set('formData', formData);
    }
    getFormData() {
        const {eccrInstanceIDs, autoCredential, username, password, namespaces, serviceAccounts} =
            this.data.get('formData');
        const data = {
            autoCredential: autoCredential === 'auto',
            namespaces: namespaces === '*' ? `"${namespaces}"` : namespaces,
            serviceAccounts: serviceAccounts === '*' ? `"${serviceAccounts}"` : serviceAccounts,
            eccrInstanceInfos: [],
        };
        const info = {
            instanceID: eccrInstanceIDs,
        };
        if (autoCredential === 'custom') {
            info.username = username;
            info.password = password;
        }
        data.eccrInstanceInfos.push(info);
        return jsyaml.safeDump(data);
    }
    validateForm() {
        return this.ref('form')?.validateFields();
    }
}
