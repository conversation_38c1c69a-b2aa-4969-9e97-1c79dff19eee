import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Alert, Checkbox, Form, Radio, Switch} from '@baidu/sui';
import {Tip, TipRadio} from '@baidu/sui-biz';
import {AiJobKindType} from '../../../../utils/enums';
import jsyaml from 'js-yaml';

const template = /* san */ html`
    <div class="deep-learning">
        <s-form>
            <!--<s-form-item label="框架：">
                <s-checkbox-group value="{=kinds=}">
                    <s-checkbox s-for="item in kindTypes" value="{{item.value}}">
                        <span>{{item.text}}</span>
                        <s-tip s-if="item.tip" content="{{item.tip}}" />
                    </s-checkbox>
                </s-checkbox-group>
            </s-form-item>-->
            <s-alert skin="info" showIcon="{{false}}">
                <div slot="description">
                    提供开箱即用的深度学习任务提交能力，默认集成 TensorFlow、Pytorch、Mxnet和PaddlePaddle 框架。
                </div>
            </s-alert>
            <s-form-item label="AI任务容错：" class="form-item-fault-tolerant">
                <s-switch checked="{=faultTolerant=}" disabled="{{disableEdit}}" on-change="onChange" />
            </s-form-item>
            <s-form-item s-if="faultTolerant" label="Etcd副本数：">
                <s-radio-group
                    s-if="disableEdit"
                    value="{=etcdCount=}"
                    radioType="button"
                >
                    <s-radio label="1个" value="{{1}}" disabled />
                    <s-radio label="3个" value="{{3}}" disabled />
                </s-radio-group>
                <s-tipradio-group
                    s-else
                    value="{=etcdCount=}"
                    radioType="button"
                >
                    <s-tipradio
                        label="1个"
                        value="{{1}}"
                        isDisabledVisibile
                        disabled="{{disableEdit}}"
                    />
                    <s-tipradio
                        label="3个"
                        value="{{3}}"
                        isDisabledVisibile
                        disabled="{{slaveVmCount < 3 || disableEdit}}"
                        content="集群内节点数量小于3，无法部署3副本。"
                    />
                </s-tipradio-group>
            </s-form-item>
            <s-alert skin="info" showIcon="{{false}}">
                <div slot="description">
                    说明：<br>
                    1、目前仅 Pytorch 框架的 AI 任务支持容错能力，同时需要在创建 AI 任务中开启<br>
                    2、AI 任务需有 CheckPoint 机制<br>
                    3、开启 AI 任务容错，会在集群内部署 etcd 组件，用于维护训练拓扑信息，支持在 AI 任务中断后，重新启动训练。<br>
                    4、Etcd 采用 StatefulSet 的部署方式部署在集群内。为了保障数据的高可用，当集群内的节点个数不少于3个时，Etcd默认为3副本，单节点上仅运行一个副本。Etcd 存储采用 local PV 的存储卷，保存在 /tmp 路径下。 
                </div>
            </s-alert>
        </s-form>
    </div>
`;
export default class DeepLearning extends Component {
    static template = template;
    static components = {
        's-form-item': Form.Item,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-tip': Tip,
        's-switch': Switch,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tipradio': TipRadio,
        's-tipradio-group': TipRadio.RadioGroup,
        's-alert': Alert,
    };

    initData() {
        return {
            kindTypes: AiJobKindType.toArray(),
            kinds: [],
            faultTolerant: false,
            etcdCount: 1,
        };
    }

    async inited() {
        this.getClusterDetail();
    }

    validateForm() {
        return Promise.resolve();
    }
    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);

        if (data) {
            const {FaultTolerant, Replicas} = data.Etcd || {};
            this.data.set('faultTolerant', !!FaultTolerant);
            this.data.set('etcdCount', Replicas || 1);

            // AI容错选项仅支持开启，不支持关闭
            if (FaultTolerant) {
                this.data.set('disableEdit', true);
            }
        }
    }
    getFormData() {
        const {faultTolerant, etcdCount} = this.data.get();
        const data = {};

    
        data['FaultTolerant'] = faultTolerant;
        if (faultTolerant) {
            data['Replicas'] = etcdCount;
        }

        return jsyaml.safeDump({Etcd: data});
    }

    async getClusterDetail() {
        const clusterUuid = this.data.get('clusterUuid');
        const result = await this.$http.getClusterDetail(clusterUuid);

        this.data.set('slaveVmCount', result.slaveVmCount);
        this.data.set('etcdCount', result.slaveVmCount < 3 ? 1 : 3);
    }
}
