/**
 * @file components/cluster/create-v2/helm/delete.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Dialog} from '@baidu/sui';
import {OutlinedExclamationCircle} from '@baidu/sui-icon';

const template = /* san */ html`
    <template>
        <s-dialog s-ref="dialog" open="{=open=}" title="卸载确认" class="s-dialog-instance">
            <div class="s-dialog-icon">
                <s-outlined-exclamation-circle class="s-dialog-icon-warning" />
            </div>
            <div class="s-dialog-text">
                <div s-if="isAiJob" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，AI任务批量调度、队列管理将不可用，您确定卸载吗？
                    </div>
                    <div class="helm-release-info">
                        同时会删除云原生AI中的队列和任务，删除后不能恢复，请谨慎操作。
                    </div>
                </div>
                <div s-if="isAiBox" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，分布式模型训练、部署等功能将不可用，您确定卸载吗？
                    </div>
                    <div class="helm-release-info">
                        同时会删除云原生AI中的任务，删除后不能恢复，请谨慎操作。
                    </div>
                </div>
                <div s-if="isGpu" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，GPU架构感知等高级调度、GPU资源算力和显存共享与隔离将不可用，您确定卸载吗？
                    </div>
                </div>
                <div s-if="isRdma" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，新创建的Pod将无法通过高性能RDMA网络通信，您确定卸载吗？
                    </div>
                </div>
                <div s-if="isFluid" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，正在使用数据集的AI任务将会受到影响，您确定卸载吗？
                    </div>
                    <div class="helm-release-info">
                        同时会删除集群中已创建的数据集，删除后不能恢复，请谨慎操作。
                    </div>
                </div>
                <div s-if="isAccelerate" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件{{item.title}}卸载之后，部署业务时将全量下载容器数据并解压，容器启动时间可能较长，您确定卸载吗？
                    </div>
                </div>
                <div s-if="isNpuManager" class="cce-cluster-helm-release-confirm">
                    <div>您确定卸载组件{{item.title}}吗？</div>
                </div>
                <div s-if="isPaddleflow" class="cce-cluster-helm-release-confirm">
                    <div>
                        组件Paddleflow卸载之后，Paddleflow客户端工作流功能将不可用，您确定卸载吗？
                    </div>
                    <div class="helm-release-info">
                        同时会删除集群中已创建的Paddleflow工作流任务相关数据，删除后不能恢复，请谨慎操作。
                    </div>
                </div>
            </div>
            <div slot="footer">
                <s-button width="46" on-click="onClose">取消</s-button>
                <s-button width="46" skin="primary" on-click="onConfirm">确认</s-button>
            </div>
        </s-dialog>
    </template>
`;
export default class extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-dialog': Dialog,
        's-outlined-exclamation-circle': OutlinedExclamationCircle,
    };
    static computed = {
        isAiJob() {
            const item = this.data.get('item');
            return item.name === 'cce-volcano';
        },
        isAiBox() {
            const item = this.data.get('item');
            return item.name === 'cce-aibox';
        },
        isGpu() {
            const item = this.data.get('item');
            return item.name === 'cce-gpu-manager';
        },
        isRdma() {
            const item = this.data.get('item');
            return item.name === 'cce-rdma-plugin';
        },
        isFluid() {
            const item = this.data.get('item');
            return item.name === 'cce-fluid';
        },
        isNpuManager() {
            const item = this.data.get('item');
            return item.name === 'cce-npu-manager';
        },
        isAccelerate() {
            const item = this.data.get('item');
            return item.name === 'cce-image-accelerate';
        },
        isPaddleflow() {
            const item = this.data.get('item');
            return item.name === 'cce-paddleflow';
        },
    };
    initData() {
        return {
            open: true,
        };
    }
    onClose() {
        this.data.set('open', false);
    }
    onConfirm() {
        this.fire('confirm');
    }
}
