import {Component} from 'san';
import jsyaml from 'js-yaml';
import {Form, Input, Radio, Select, Row, Col, Switch} from '@baidu/sui';

const template = /* san */`
<template>
    <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" class="descheduler-config-form">
        <s-form-item label="监控方式:" prop="mode">
            <s-radio-group
                radioType="button"
                datasource="{{modeDatasource}}"
                value="{=formData.mode=}"
            />
        </s-form-item>
        <s-form-item
            s-if="formData.mode === '1'"
            label="CProm实例:"
            prop="cprom"
        >
            <s-select
                width="250"
                datasource="{{cpromDatasource}}"
                value="{=formData.cprom=}"
            />
            <div slot="help">
                通过
                <a href="https://cloud.baidu.com/doc/CProm/s/vl0gco3gf" target="_blank">Prometheus监控服务CProm</a>
                获取集群节点真实负载情况
            </div>
        </s-form-item>
        <s-form-item
            class="prometheus-address"
            s-if="formData.mode === '2'"
            label="查询地址:"
            prop="prometheusAddress"
        >
            <s-input
                width="360"
                value="{=formData.prometheusAddress=}"
                placeholder="请输入Prometheus数据查询地址，如https://prometheus:9090"
            />
            <div slot="help">集群内可访问Prometheus地址，需先根据组件使用说明文档配置指标采集规则再填写当前数据源</div>
        </s-form-item>
        <s-form-item label="调度策略:" class="scheduling-strategy-item">
            <div class="scheduling-strategy-wrap">
                <s-row type="flex" gutter="10">
                    <s-col class="scheduling-strategy-label" span="5">利用率阈值:</s-col>
                    <s-col span="9">
                        <s-form-item prop="thresholdsCpu">
                            <s-input
                                width="60"
                                type="number"
                                value="{=formData.thresholdsCpu=}"
                                step="1"
                                prefix="CPU"
                                suffix="%"
                                on-change="onChangeValidator('thresholdsCpu', 'targetCpu')"
                            />
                        </s-form-item>
                    </s-col>
                    <s-col span="9">
                        <s-form-item prop="thresholdsMemory">
                            <s-input
                                width="60"
                                type="number"
                                value="{=formData.thresholdsMemory=}"
                                step="1"
                                prefix="内存"
                                suffix="%"
                                on-change="onChangeValidator('thresholdsMemory', 'targetMemory')"
                            />
                        </s-form-item>
                    </s-col>
                </s-row>
                <s-row type="flex" gutter="10">
                    <s-col class="scheduling-strategy-label" span="5">目标利用率:</s-col>
                    <s-col span="9">
                        <s-form-item prop="targetCpu">
                            <s-input
                                width="60"
                                type="number"
                                value="{=formData.targetCpu=}"
                                step="1"
                                prefix="CPU"
                                suffix="%"
                                on-change="onChangeValidator('targetCpu', 'thresholdsCpu')"
                            />
                        </s-form-item>
                    </s-col>
                    <s-col span="9">
                        <s-form-item prop="targetMemory">
                            <s-input
                                width="60"
                                type="number"
                                value="{=formData.targetMemory=}"
                                step="1"
                                prefix="内存"
                                suffix="%"
                                on-change="onChangeValidator('targetMemory', 'thresholdsMemory')"
                            />
                        </s-form-item>
                    </s-col>
                </s-row>
                <s-row gutter="10">
                    <s-col class="tips">
                        过去五分钟内，节点的 CPU 平均利用率或者内存平均使用率超过设定阈值，组件会判断节点为高负载节点，执行 Pod 驱逐逻辑，并尽量通过重调度使节点负载降到目标利用率以下
                    </s-col>
                </s-row>
                <s-row gutter="10">
                    <s-col class="scheduling-strategy-label" span="9">重调度挂载PVC Pod:</s-col>
                    <s-col span="15" class="align-items-center">
                        <s-form-item prop="ignorePvcPods">
                            <s-switch checked="{=formData.ignorePvcPods=}" />
                        </s-form-item>
                    </s-col>
                </s-row>
                <s-row gutter="10">
                    <s-col class="scheduling-strategy-label" span="9">重调度挂载本地存储Pod:</s-col>
                    <s-col span="15" class="align-items-center">
                        <s-form-item prop="evictLocalStoragePods">
                            <s-switch checked="{=formData.evictLocalStoragePods=}" />
                        </s-form-item>
                    </s-col>
                </s-row>
            </div>
        </s-form-item>
    </s-form>
</template>
`;

export default class DeschedulerConfig extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-select': Select,
        's-input': Input,
        's-row': Row,
        's-col': Col,
        's-switch': Switch
    };
    initData() {
        const validateCpuAndMemory = (field, value, callback) => {
            if (value <= 0) {
                return callback('请输入大于0的值');
            }
            if (value > 100) {
                return callback('请输入小于100的值');
            }
            return callback();
        };
        const validatorCpu = (field, value, callback) => {
            const {thresholdsCpu, targetCpu} = this.data.get('formData') || {};
            return +thresholdsCpu <= +targetCpu ? callback('利用率阈值需要大于目标利用率') : callback();
        };
        const validatorMemory = (field, value, callback) => {
            const {thresholdsMemory, targetMemory} = this.data.get('formData') || {};
            return +thresholdsMemory <= +targetMemory ? callback('利用率阈值需要大于目标利用率') : callback();
        };
        return {
            modeDatasource: [
                {label: '托管Prometheus', value: '1'},
                {label: '自建Prometheus', value: '2'}
            ],
            cpromDatasource: [],
            formData: {
                mode: '1',
                cprom: '', // cprom 实例
                prometheusAddress: '', // 查询地址
                thresholdsCpu: 75, // 阈值 cpu
                thresholdsMemory: 65, // 阈值 内存
                targetCpu: 55, // 目标利用率 cpu
                targetMemory: 45, // 目标利用率 内存
                ignorePvcPods: false, // 是否忽略配置了PVC的Pod
                evictLocalStoragePods: false, // 是否驱逐配置了本地存储的pod
            },
            rules: {
                cprom: [
                    {required: true, message: '请选择'},
                ],
                prometheusAddress: [
                    {required: true, message: '请输入'},
                    {pattern: /^(https?\:\/\/).+$/, message: '请输入正确的格式'},
                ],
                thresholdsCpu: [
                    {validator: validateCpuAndMemory},
                    {validator: validatorCpu},
                ],
                thresholdsMemory: [
                    {validator: validateCpuAndMemory},
                    {validator: validatorMemory},
                ],
                targetCpu: [
                    {validator: validateCpuAndMemory},
                    {validator: validatorCpu},
                ],
                targetMemory: [
                    {validator: validateCpuAndMemory},
                    {validator: validatorMemory},
                ]
            }
        };
    }

    inited() {
        this.requestCpromInstances();
    }

    async requestCpromInstances() {
        const clusterUuid = this.data.get('clusterUuid');
        try {
            if (!clusterUuid) {
                return;
            }
            const res = await this.$http.getCpromMonitor(clusterUuid);
            const data = res?.result?.items?.map(item => {
                const {instanceID, instanceName} = item?.instance?.spec || {};
                return {value: instanceID, text: instanceName};
            }) || [];
            this.data.set('cpromDatasource', data);
        } catch (err) {
            this.data.set('cpromDatasource', []);
        }
    }

    // input改变时，触发验证联动
    async onChangeValidator(key, ...rest) {
        try {
            // 需要验证所有的表单控件，如果只验证其中一个控件，是不会走catch的
            await this.ref('form').validateResult();
            await this.ref('form').validateFields(rest);
        } catch (errors) {
            // 验证联动，如果当前表单控件验证没问题后，还需要验证其他的表单控件
            if (!errors[key]) {
                await this.ref('form').validateFields(rest);
            }
        }
    }

    validateForm() {
        return this.ref('form').validateFields();
    }

    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);
        if (data) {
            const {cmdOptions, deschedulerPolicy} = data;
            const {
                utilizationThresholds,
                targetUtilizationThresholds
            } = deschedulerPolicy?.strategies?.NodeRealUtilization?.params?.nodeHotlineThresholds || {};

            // 取数据
            const {
                prometheusAddress = '',
                cpromInstanceId: cprom = ''
            } = cmdOptions || {};
            const {ignorePvcPods, evictLocalStoragePods = false} = deschedulerPolicy || {};
            const {
                cpuUsageRatio: thresholdsCpu = 75,
                memUsageRatio: thresholdsMemory = 65
            } = utilizationThresholds || {};
            const {
                cpuUsageRatio: targetCpu = 55,
                memUsageRatio: targetMemory = 45
            } = targetUtilizationThresholds || {};

            const formData = {
                mode: cprom ? '1' : '2',
                cprom, // cprom 实例
                // 如果之前选择的是cprom实例，后端会把prometheusAddress地址解析出来，返给前端，前端不需要展示
                prometheusAddress: cprom ? '' : prometheusAddress, // 查询地址
                thresholdsCpu, // 阈值 cpu
                thresholdsMemory, // 阈值 内存
                targetCpu, // 目标利用率 cpu
                targetMemory, // 目标利用率 内存
                // ignorePvcPods 如果存在这个字段，并且为false，开关打开，否则，关闭
                ignorePvcPods: ignorePvcPods === false, // 是否忽略配置了PVC的Pod
                evictLocalStoragePods, // 是否驱逐配置了本地存储的pod
            };
            this.data.merge('formData', formData);
        }
    }

    getFormData() {
        const {
            mode,
            cprom, prometheusAddress,
            thresholdsCpu, thresholdsMemory,
            targetCpu, targetMemory,
            ignorePvcPods, evictLocalStoragePods
        } = this.data.get('formData') || {};

        const values = {
            cmdOptions: {
                prometheusAddress: mode === '2' ? prometheusAddress : '',
                cpromInstanceId: mode === '1' ? cprom : ''
            },
            deschedulerPolicy: {
                evictLocalStoragePods,
                strategies: {
                    NodeRealUtilization: {
                        params: {
                            nodeHotlineThresholds: {
                                utilizationThresholds: {
                                    cpuUsageRatio: +thresholdsCpu,
                                    memUsageRatio: +thresholdsMemory
                                },
                                targetUtilizationThresholds: {
                                    cpuUsageRatio: +targetCpu,
                                    memUsageRatio: +targetMemory
                                }
                            }
                        }
                    }
                }
            }
        };
        // ignorePvcPods 这个字段，当开关关闭的时候，不用传递，开关打开，传递 ignorePvcPods: false
        if (ignorePvcPods) {
            values.deschedulerPolicy.ignorePvcPods = false;
        }
        return jsyaml.safeDump(values);
    }
}