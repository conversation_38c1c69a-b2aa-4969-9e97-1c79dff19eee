import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Form, Input, InputNumber, Button, Select, Checkbox} from '@baidu/sui';
import jsyaml from 'js-yaml';

const template = html`<template>
    <s-form
        class="err-autofix"
        s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-align="left"
    >
        <s-form-item
            prop="matchLabels"
            label="自愈范围："
            help="通过k8s标签匹配节点自愈范围，输入格式为key:value，支持多个中间使用”,“分隔"
        >
            <s-input type="text" width="420" value="{=formData.matchLabels=}" />
        </s-form-item>
        <s-form-item label="自愈规则：">
            <ul>
                <li s-for="item,index in autofixRules">
                    <div class="item">
                        <span>自愈条件：</span>
                        <s-select
                            value="{{item.value}}"
                            datasource="{{options}}"
                            width="330"
                            on-change="ruleChange($event, index)"
                        />
                    </div>
                    <div class="item action">
                        <span>自愈操作：</span>
                        <s-checkbox
                            label="封锁节点"
                            checked="{{item.action.CordonNode}}"
                            on-change="actionChange($event, 'CordonNode', index)"
                        />
                        <s-checkbox
                            label="驱逐Pod"
                            checked="{{item.action.DrainNode}}"
                            on-change="actionChange($event, 'DrainNode', index)"
                        />
                        <s-checkbox
                            label="重启容器运行时"
                            checked="{{item.action.RestartContainerRuntime}}"
                            on-change="actionChange($event, 'RestartContainerRuntime', index)"
                        />
                        <s-checkbox
                            class="ml63"
                            label="重启Kubelet"
                            checked="{{item.action.RestartKubelet}}"
                            on-change="actionChange($event, 'RestartKubelet', index)"
                        />
                        <s-checkbox
                            label="重启服务器（支持BCC/EBC）"
                            checked="{{item.action.RebootNode}}"
                            on-change="actionChange($event, 'RebootNode', index)"
                        />
                    </div>
                    <div class="item">
                        推荐操作：<span>{{item.desc}}</span>
                        <s-button
                            skin="stringfy"
                            on-click="deleteRule(index)"
                            class="del"
                            s-if="autofixRules.length > 1"
                        >
                            删除
                        </s-button>
                    </div>
                </li>
            </ul>
            <s-button skin="stringfy" on-click="addRule" class="add">新增规则</s-button>
            <div s-if="isRepeat" class="repeat-msg">
                存在重复规则 <span s-if="noSteps">，且</span>
            </div>
            <div s-if="noSteps" class="not-steps-msg">每个自愈条件至少选择一个自愈操作</div>
        </s-form-item>
        <s-form-item label="自愈策略：">
            <ul class="tactics">
                <li>
                    <div class="tactics-item">
                        <span class="label">每天最大节点数：</span>
                        <s-input-number
                            width="250"
                            value="{=formData.maxNodesPerDay=}"
                            min="1"
                            max="10"
                        />
                        <span class="unit">个</span>
                        <div class="help">每天最多执行自愈操作节点个数</div>
                    </div>
                    <div class="tactics-item">
                        <span class="label">每小时最大节点数：</span>
                        <s-input-number
                            width="250"
                            value="{=formData.maxNodesPerHour=}"
                            min="1"
                            max="5"
                        />
                        <span class="unit">个</span>
                        <div class="help">每小时最多执行自愈操作节点个数</div>
                    </div>
                    <div class="tactics-item">
                        <span class="label">并发节点数：</span>
                        <s-input-number
                            width="250"
                            value="{=formData.maxProcessingNodes=}"
                            min="1"
                            max="1"
                            disabled
                        />
                        <span class="unit">个</span>
                        <div class="help">同时执行自愈操作节点个数</div>
                    </div>
                    <div class="tactics-item">
                        <span class="label">冷却时间：</span>
                        <s-input-number
                            width="250"
                            value="{=formData.minIntervalPerNode=}"
                            min="1"
                            max="360"
                        />
                        <span class="unit">分钟</span>
                        <div class="help">同一节点自愈结束后再次执行自愈操作时间间隔</div>
                    </div>
                </li>
            </ul>
        </s-form-item>
        <s-form-item prop="ak" label="Access Key：" s-if="showAccess">
            <s-input type="text" width="420" value="{=formData.ak=}" />
        </s-form-item>
        <s-form-item prop="sk" label="Secret Key：" s-if="showAccess">
            <s-input
                type="password"
                width="420"
                value="{=formData.sk=}"
                autocomplete="new-password"
            />
        </s-form-item>
        <a href="/iam/#/iam/accesslist" target="_blank" class="aksk" s-if="showAccess">获取AK/SK</a>
    </s-form>
</template>`;

@decorators.asComponent('@err-autofix')
export default class HelmStroage extends Component {
    static template = template;
    static components = {
        's-input': Input,
        's-textarea': Input.TextArea,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-button': Button,
        's-select': Select,
        's-checkbox': Checkbox,
    };
    static computed = {
        showAccess() {
            const autofixRules = this.data.get('autofixRules');
            return !!autofixRules.find(
                e =>
                    e.action.RebootNode ||
                    e.action.RestartContainerRuntime ||
                    e.action.RestartKubelet,
            );
        },
    };
    initData() {
        return {
            isRepeat: false,
            noSteps: true,
            options: [
                {
                    value: 'ContainerRuntimeUnhealthy',
                    desc: '重启容器运行时',
                },
                {
                    value: 'FrequentKubeletRestart',
                    desc: '无（仅审计）',
                },
                {
                    value: 'FrequentContainerRuntimeRestart',
                    desc: '无（仅审计）',
                },
                {
                    value: 'CorruptDockerOverlay2',
                    desc: '封锁节点 | 驱逐节点Pod',
                },
                {
                    value: 'InodesPressure',
                    desc: '无（仅审计）',
                },
                {
                    value: 'KernelDeadlock',
                    desc: '封锁节点 | 驱逐Pod | 重启服务器（支持BCC/EBC）',
                },
                {
                    value: 'ReadonlyFilesystem',
                    desc: '封锁节点 | 驱逐Pod | 重启服务器（支持BCC/EBC）',
                },
                {
                    value: 'GPUUnhealthy',
                    desc: '封锁节点',
                },
                {
                    value: 'NICUnhealthy',
                    desc: '封锁节点',
                },
                {
                    text: 'MemoryUnhealthy（仅支持 EBC）',
                    value: 'MemoryUnhealthy',
                    desc: '封锁节点',
                },
                {
                    value: 'CPUUnhealthyExt',
                    desc: '封锁节点',
                },
                {
                    value: 'MemoryUnhealthyExt',
                    desc: '封锁节点',
                },
                {
                    value: 'MainboardUnhealthyExt',
                    desc: '封锁节点',
                },
                {
                    value: 'KubeletUnhealthy',
                    desc: '重启Kubelet',
                },
            ],
            autofixRules: [
                {
                    value: 'ContainerRuntimeUnhealthy',
                    desc: '重启容器运行时',
                    action: {
                        CordonNode: false,
                        DrainNode: false,
                        RebootNode: false,
                        RestartContainerRuntime: false,
                        RestartKubelet: false,
                    },
                },
            ],
            formData: {
                matchLabels: '',
                ak: '',
                sk: '',
                maxNodesPerDay: 10, // 每天最大维修节点数
                maxNodesPerHour: 3, // 每小时最多维修节点个数
                maxProcessingNodes: 1, //  并发节点数
                minIntervalPerNode: 10, // 维修单个节点的冷却时间
            },
            rules: {
                matchLabels: [
                    {required: true, message: '请输入自愈范围'},
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                if (value.includes('=')) {
                                    return callback(
                                        '输入格式为key:value，支持多个中间使用”,“分隔，不包含字符’=‘',
                                    );
                                }
                                let flag = true; // false 为不通过
                                const list = value.split(',');
                                list.forEach(e => {
                                    if (e.split(':').length !== 2) {
                                        // 只能包含1个冒号
                                        flag = false;
                                    }
                                });
                                if (!flag) {
                                    return callback(
                                        '输入格式为key:value，支持多个中间使用”,“分隔，不包含字符’=‘',
                                    );
                                } else {
                                    return callback();
                                }
                            } else {
                                return callback('请输入自愈范围');
                            }
                        },
                    },
                ],
                ak: [{required: true, message: '请输入Access Key'}],
                sk: [{required: true, message: '请输入Secret Key'}],
            },
        };
    }

    attached() {
        this.watchRules();
    }

    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);
        const {policy, rules, provider} = data;
        if (policy?.nodeSelector?.matchLabels) {
            let matchLabelsList = [];
            Object.keys(policy.nodeSelector.matchLabels).forEach(key => {
                matchLabelsList.push(`${key}:${policy.nodeSelector.matchLabels[key]}`);
            });
            if (matchLabelsList.length) {
                this.data.set('formData.matchLabels', matchLabelsList.join(','));
            }
        }
        const {maxNodesPerDay, maxNodesPerHour, maxProcessingNodes, minIntervalPerNode} =
            policy || {};
        this.data.set('formData.maxNodesPerDay', parseInt(maxNodesPerDay, 10));
        this.data.set('formData.maxNodesPerHour', parseInt(maxNodesPerHour, 10));
        this.data.set('formData.maxProcessingNodes', parseInt(maxProcessingNodes, 10));
        this.data.set('formData.minIntervalPerNode', parseInt(minIntervalPerNode, 10));
        if (rules) {
            // 这个是用来兼容老版组件，这个几个条件没有steps就加上默认steps
            const hackCondition = {
                CorruptDockerOverlay2: ['CordonNode', 'DrainNode'],
                KernelDeadlock: ['CordonNode', 'DrainNode', 'RebootNode'],
                ReadonlyFilesystem: ['CordonNode', 'DrainNode', 'RebootNode'],
                GPUUnhealthy: ['CordonNode'],
                NICUnhealthy: ['CordonNode'],
            };
            const autofixRules = rules.map(e => {
                if (!e.steps && hackCondition[e.condition]) {
                    e.steps = hackCondition[e.condition];
                }
                return {
                    ...(this.data.get('options').find(v => v.value === e.condition) || {}),
                    action: {
                        CordonNode: e?.steps?.includes('CordonNode') || false,
                        DrainNode: e?.steps?.includes('DrainNode') || false,
                        RestartContainerRuntime:
                            e?.steps?.includes('RestartContainerRuntime') || false,
                        RestartKubelet: e?.steps?.includes('RestartKubelet') || false,
                        RebootNode: e?.steps?.includes('RebootNode') || false,
                    },
                };
            });
            if (autofixRules.length) {
                this.data.set('autofixRules', autofixRules);
                if (
                    !!autofixRules.find(
                        e =>
                            e.action.RebootNode ||
                            e.action.RestartContainerRuntime ||
                            e.action.RestartKubelet,
                    )
                ) {
                    this.data.set('formData.ak', provider?.cce?.ak || '');
                    this.data.set('formData.sk', provider?.cce?.sk || '');
                }
            }
        }
    }

    getFormData() {
        const {formData, autofixRules} = this.data.get();
        const {
            matchLabels = '',
            maxNodesPerDay,
            maxNodesPerHour,
            maxProcessingNodes,
            minIntervalPerNode,
            ak = '',
            sk = '',
        } = formData;
        const orderList = [
            'CordonNode',
            'DrainNode',
            'RestartContainerRuntime',
            'RestartKubelet',
            'RebootNode',
        ];
        const rules = autofixRules.map(e => ({
            condition: e.value,
            steps: Object.keys(e.action)
                .filter(k => e.action[k])
                .sort((a, b) => orderList.indexOf(a) - orderList.indexOf(b)),
        }));
        const matchLabelsObj = {};
        matchLabels.split(',').forEach(item => {
            const [k, v = ''] = item.split(':');
            matchLabelsObj[k] = v;
        });
        const data = {
            policy: {
                nodeSelector: {
                    matchLabels: matchLabelsObj,
                },
                maxNodesPerDay,
                maxNodesPerHour,
                maxProcessingNodes,
                minIntervalPerNode: `${minIntervalPerNode}m`,
            },
            rules,
        };
        if (this.data.get('showAccess')) {
            data.provider = {
                cce: {
                    ak,
                    sk,
                },
            };
        }

        return jsyaml.safeDump(data);
    }

    validateForm() {
        return Promise.all([this.validateRules(), this.ref('form')?.validateFields()]);
    }

    validateRules() {
        return new Promise((resolve, reject) => {
            const autofixRules = this.data.get('autofixRules');
            let isRepeat = false;
            let noSteps = false;
            autofixRules.forEach((item, index) => {
                if (autofixRules.find((e, i) => e.value === item.value && i !== index)) {
                    isRepeat = true;
                    this.data.set('isRepeat', true);
                }
                const steps = Object.entries(item.action || {});
                if (!steps.length || steps.every(e => e[1] === false)) {
                    noSteps = true;
                }
            });
            this.data.set('isRepeat', isRepeat);
            this.data.set('noSteps', noSteps);
            if (isRepeat || noSteps) {
                return reject('规则校验失败');
            } else {
                return resolve();
            }
        });
    }

    watchRules() {
        this.watch('autofixRules', () => {
            this.validateRules();
        });
    }

    ruleChange(e, index) {
        const target = this.data.get('options').find(v => v.value === e.value);
        if (target) {
            this.data.splice('autofixRules', [index, 1, {...target, action: {}}]);
        }
    }

    actionChange(e, actionKey, index) {
        const checkedValue = e.value;
        const target = this.data.get('autofixRules')[index];
        target.action[actionKey] = checkedValue;

        if (
            ['DrainNode', 'RebootNode', 'RestartContainerRuntime', 'RestartKubelet'].includes(
                actionKey,
            ) &&
            checkedValue
        ) {
            target.action['CordonNode'] = true;
        }
        if (actionKey === 'CordonNode' && !checkedValue) {
            target.action['DrainNode'] = false;
            target.action['RebootNode'] = false;
            target.action['RestartContainerRuntime'] = false;
            target.action['RestartKubelet'] = false;
        }
        this.data.splice('autofixRules', [index, 1, target]);
    }

    addRule() {
        this.data.push('autofixRules', {
            value: 'ContainerRuntimeUnhealthy',
            desc: '重启容器运行时',
            action: {},
        });
    }

    deleteRule(index) {
        this.data.splice('autofixRules', [index, 1]);
    }
}
