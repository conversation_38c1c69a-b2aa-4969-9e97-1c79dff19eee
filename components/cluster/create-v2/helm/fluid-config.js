import {Component} from 'san';
import {Form, Input} from '@baidu/sui';
import jsyaml from 'js-yaml';

/**
 * Fluid 组件配置
 */
export default class FluidConfig extends Component {
    static template = /* san */`
        <template>
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item
                    prop="kubeletRootPath"
                    label="KubeletRootPath："
                    help="用户节点kubelet数据目录，支持录入多个，以回车换行"
                >
                    <s-textarea width="400" height="70" value="{=formData.kubeletRootPath=}"></s-textarea>
                </s-form-item>
            </s-form>
        </template>
    `;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-textarea': Input.TextArea,
    };

    initData() {
        return {
            formData: {
                kubeletRootPath: `/home/<USER>/kubelet\n/data/kubelet\n/var/lib/kubelet`,
            },
            rules: {
                kubeletRootPath: [{required: true, message: '请输入kubeletRootPath'}],
            }
        };
    }

    // 修改配置 初始化表单
    initFormData(comp) {
        const data = jsyaml.load(comp.instance.params);
        const kubeletRootPath = data?.csi?.kubelet?.nodes?.map(e => e.kubeletRootPath).join('\n');
        this.data.set('formData.kubeletRootPath', kubeletRootPath);
    }

    // 获取表单数据
    getFormData() {
        const {kubeletRootPath} = this.data.get('formData');

        // 处理 kubeletRootPath
        let kubeletRootPathList = [];
        if (kubeletRootPath) {
            kubeletRootPathList = [...new Set(kubeletRootPath.split('\n').filter(e => e))];
        }
        const nodes = kubeletRootPathList.map(path => ({
            kubeletRootPath: path,
            kubeletRootPathAffinity: true,
        }));

        // 配置对象
        const data = {
            csi: {
                kubelet: {
                    nodes,
                },
            },
        };
        return jsyaml.safeDump(data);
    }

    validateForm() {
        return this.ref('form')?.validateFields();
    }
}
