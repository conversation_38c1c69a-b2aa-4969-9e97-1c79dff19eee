import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Link, Loading, Radio, Switch, Alert} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {OutlinedQuestion} from '@baidu/sui-icon';
import {GpuEnableSGPUType} from '../../../../utils/enums';
import jsyaml from 'js-yaml';
import {MasterType} from '../enums';

const template = /* san */ html`
    <template class="gpu-config">
        <s-loading s-if="loading" loading="{{true}}" />
        <s-form s-else>
            <s-form-item prop="gpuShare" label="组件类型：" class="gpu-share-form-item">
                <s-radio-group class="gpu-share-radio-group" value="{=formData.enableSGPU.value=}">
                    <s-radio
                        s-for="item in formData.enableSGPU.datasource"
                        value="{{item.value}}"
                        disabled
                    >
                        <span>{{item.text}}</span>
                        <s-tip s-if="{{item.tip}}" placement="left" class="gpu-share-tip">
                            <s-question color="#999" />
                            <div slot="content">
                                目前隔离最优型仅支持部分操作系统，详见<a
                                    href="https://cloud.baidu.com/doc/CCE/s/1kp80bcb4"
                                    target="_blank"
                                    >CCE用户文档</a
                                >，如有其他适配需求请提交工单。
                            </div>
                        </s-tip>
                    </s-radio>
                </s-radio-group>
                <div s-if="!formData.enableSGPU.value">
                    <s-link skin="info">{{getGpuEnableSGPUDesc}}</s-link>
                </div>
            </s-form-item>
            <s-form-item
                prop="shareMemoryUnit"
                label="GPU显存共享单位："
                class="gpu-share-form-item"
            >
                <s-radio-group
                    class="gpu-share-unit-radio-group"
                    value="{=formData.shareMemoryUnit.value=}"
                >
                    <s-radio value="GiB" disabled>GiB</s-radio>
                    <s-radio s-if="showMiB" value="MiB" disabled>
                        MiB
                        <s-tip placement="top" class="gpu-share-tip">
                            <s-question color="#999" />
                            <div slot="content">
                                指GPU显存最小分配粒度，如选择了GiB，则每个任务最少可使用1GB显存，如选择了MiB，则每个任务最少可使用1MiB显存；两种方式均支持按百分比等比使用显存
                            </div>
                        </s-tip>
                    </s-radio>
                </s-radio-group>
            </s-form-item>
            <s-form-item prop="IgnoreDeviceType" label="精细化调度：" class="ai-job-refine">
                <s-switch checked="{=formData.IgnoreDeviceType=}" disabled />
                <s-tip
                    class="ai-job-refine-tip"
                    content="开启精细化调度后，创建队列和容器时均支持选择详细的GPU型号，关闭精细化调度后创建队列和容器时仅支持输入配额，不支持选择具体的GPU型号"
                />
            </s-form-item>
        </s-form>
    </template>
`;
export default class GpuConfig extends Component {
    static template = template;
    static components = {
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tip': Tip,
        's-question': OutlinedQuestion,
        's-link': Link,
        's-switch': Switch,
        's-alert': Alert,
        's-loading': Loading,
    };
    static computed = {
        getGpuEnableSGPUDesc() {
            // 获取 GPU 组件类型的描述
            const enableSGPU = this.data.get('formData.enableSGPU');
            const temp = enableSGPU.datasource.find(v => v.value === enableSGPU.value);
            return temp?.desc;
        },

        showMiB() {
            // 隔离性最优型 GPU显存共享单位下掉MiB选项
            return this.data.get('formData.enableSGPU').value !== GpuEnableSGPUType.Quarantine;
        },
    };
    initData() {
        return {
            formData: {
                enableSGPU: {
                    datasource: GpuEnableSGPUType.toArray('Quarantine'),
                    value: true,
                },
                IgnoreDeviceType: true, // 精细化调度, 忽略设备型号
                shareMemoryUnit: {
                    // 共享单位
                    value: 'GiB',
                },
            },
        };
    }

    inited() {
        if (this.data.get('isView') && !this.data.get('isUpgrade')) {
            // 查看时获取组件数据
            this.getCompData();
        }
    }

    validateForm() {
        return true;
    }

    initFormData(data) {
        // 升级时初始化参数
        this.data.set('formData.shareMemoryUnit.value', 'GiB');
        this.data.set('formData.enableSGPU.value', data.EnableSGPU);
        this.data.set('formData.IgnoreDeviceType', !data.IgnoreDeviceType);
    }

    getFormData() {
        const {enableSGPU, IgnoreDeviceType, shareMemoryUnit} = this.data.get('formData');
        const region = window?.$context?.getCurrentRegionId();
        const pointMap = {
            bj: {
                GatewayEndpoint: 'cce-gateway.bj.baidubce.com',
                CceV2Endpoint: 'cce.bj.baidubce.com',
            },
            gz: {
                GatewayEndpoint: 'cce-gateway.gz.baidubce.com',
                CceV2Endpoint: 'cce.gz.baidubce.com',
            },
            hkg: {
                GatewayEndpoint: 'cce-gateway.hkg.baidubce.com',
                CceV2Endpoint: 'cce.hkg.baidubce.com',
            },
            nj: {
                GatewayEndpoint: 'cce-gateway.nj.baidubce.com',
                CceV2Endpoint: 'cce.nj.baidubce.com',
            },
            sz: {
                GatewayEndpoint: 'cce-gateway.su.baidubce.com',
                CceV2Endpoint: 'cce.su.baidubce.com',
            },
            wh: {
                GatewayEndpoint: 'cce-gateway.fwh.baidubce.com',
                CceV2Endpoint: 'cce.fwh.baidubce.com',
            },
            bd: {
                GatewayEndpoint: 'cce-gateway.bd.baidubce.com',
                CceV2Endpoint: 'cce.bd.baidubce.com',
            },
        };
        const data = {
            EnableHook: this.data.get('masterType') === MasterType.CUSTOM,
            EnableSGPU: enableSGPU.value,
            IgnoreDeviceType: !IgnoreDeviceType,
            GPUShareMemoryUnit: shareMemoryUnit.value,
            Region: region,
            ClusterID: this.data.get('clusterUuid'),
            GatewayEndpoint: pointMap[region]?.GatewayEndpoint,
            CceV2Endpoint: pointMap[region]?.CceV2Endpoint,
        };
        const ValidData = {};
        Object.keys(data).forEach(key => {
            (data[key] || data[key] === false) && (ValidData[key] = data[key]);
        });
        return jsyaml.safeDump(ValidData);
    }

    getCompData() {
        this.data.set('loading', true);

        this.$http
            .getInstanceRevisionValue(
                {
                    clusterUuid: this.data.get('clusterUuid'),
                    name: 'cce-gpu-manager',
                    namespace: 'kube-system',
                    all: 'true',
                },
                {'x-silent': true},
            )
            .then(res => {
                const data = jsyaml.load(res?.result?.data) || {};

                return data;
            })
            .catch(err => {
                return {};
            })
            .then(data => {
                if (data.EnableSGPU === false) {
                    this.data.set(
                        'formData.enableSGPU.datasource',
                        GpuEnableSGPUType.toArray('Performance'),
                    );
                }
                this.data.set('formData.shareMemoryUnit.value', data.GPUShareMemoryUnit);
                this.data.set('formData.enableSGPU.value', data.EnableSGPU);
                this.data.set('formData.IgnoreDeviceType', !data.IgnoreDeviceType);
                this.data.set('loading', false);
            });
    }
}
