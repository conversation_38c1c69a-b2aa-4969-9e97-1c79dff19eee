import {Component} from 'san';
import jsyaml from 'js-yaml';
import {Alert, Form, InputNumber, Switch} from '@baidu/sui';

const template = /* san */`
<div>
    <s-alert skin="warning">
        仅支持使用 BaiduLinux 3 作为操作系统
    </s-alert>
    <s-form
        s-ref="form"
        data="{=formData=}"
        class="halolet-form"
        label-col="{{{span: 6}}}"
        wrapper-col="{{{span: 18}}}"
        label-align="left"
    >
        <s-form-item
            label="CPU驱逐水位："
            prop="cpu.highPercent"
            required
            help="离线负载驱逐水位线，节点CPU使用率超过将会驱逐节点上所有的离线负载。"
        >
            <s-input-number
                width="100"
                value="{=formData.cpu.highPercent=}"
                step="1"
                min="{{1}}"
                max="{{100}}"
            />
            <span class="unit">%</span>
        </s-form-item>
        <s-form-item
            label="CPU压制水位："
            prop="cpu.bestEffortMaxCores"
            required
            help="离线负载压制水位线，节点CPU使用率超过将会压制节点上的离线负载，保持CPU使用率在水位线附近。"
        >
            <s-input-number
                width="100"
                value="{=formData.cpu.bestEffortMaxCores=}"
                step="1"
                min="{{1}}"
                max="{{100}}"
            />
            <span class="unit">%</span>
        </s-form-item>
        <s-form-item
            label="最大内存使用率："
            prop="memory.highPercent"
            required
            help="节点内存使用率水位线，超过将会驱逐节点上的离线负载，直到符合水位线要求或驱逐完所有离线负载。"
        >
            <s-input-number
                width="100"
                value="{=formData.memory.highPercent=}"
                step="1"
                min="{{1}}"
                max="{{100}}"
            />
            <span class="unit">%</span>
        </s-form-item>
        <s-form-item
            label="离线最大内存使用率："
            prop="memory.bestEffortMax"
            required
            help="离线负载内存使用率水位线，超过将会驱逐节点上的离线负载，直到符合水位线要求或驱逐完所有离线负载。"
        >
            <s-input-number
                width="100"
                value="{=formData.memory.bestEffortMax=}"
                step="1"
                min="{{1}}"
                max="{{100}}"
            />
            <span class="unit">%</span>
        </s-form-item>
        <s-form-item
            label="最大入网流量："
            prop="net.inHigh"
            required
            help="节点入网流量水位线。"
        >
            <s-input-number
                width="100"
                value="{=formData.net.inHigh=}"
                step="1"
                min="{{1}}"
            />
            <span class="unit">MB/s</span>
        </s-form-item>
        <s-form-item
            label="离线最大入网流量："
            prop="net.bestEffortInHigh"
            required
            help="离线负载入网流量水位线，并对已有离线负载进行网络压制。"
        >
            <s-input-number
                width="100"
                value="{=formData.net.bestEffortInHigh=}"
                step="1"
                min="{{1}}"
            />
            <span class="unit">MB/s</span>
        </s-form-item>
        <s-form-item
            label="最大出网流量："
            prop="net.outHigh"
            required
            help="节点出网流量水位线。"
        >
            <s-input-number
                width="100"
                value="{=formData.net.outHigh=}"
                step="1"
                min="{{1}}"
            />
            <span class="unit">MB/s</span>
        </s-form-item>
        <s-form-item
            label="离线最大出网流量："
            prop="net.bestEffortOutHigh"
            required
            help="离线负载出网流量水位线，并对已有离线负载进行网络压制。"
        >
            <s-input-number
                width="100"
                value="{=formData.net.bestEffortOutHigh=}"
                step="1"
                min="{{1}}"
            />
            <span class="unit">MB/s</span>
        </s-form-item>
        <s-form-item
            label="启用离线带宽抢占："
            prop="net.enablePriority"
            required
            help="开启后，当整机带宽资源不足时，在线任务可抢占离线任务带宽。"
            class="enable-priority"
        >
            <s-switch checked="{=formData.net.enablePriority=}" />
        </s-form-item>
    </s-form>
</template>
`;

export default class Halolet extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-alert': Alert,
        's-input-number': InputNumber,
        's-switch': Switch
    };
    initData() {
        return {
            formData: {
                cpu: {
                    highPercent: 80, // cpu 销毁水位
                    bestEffortMaxCores: 60, // cpu 压制水位
                },
                memory: {
                    highPercent: 80, // 最大内存使用率
                    bestEffortMax: 60, // 离线最大内存使用率
                },
                net: {
                    inHigh: 102400, // 最大入网流量
                    bestEffortInHigh: 102400, // 离线最大入网流量
                    outHigh: 102400, // 最大出网流量
                    bestEffortOutHigh: 102400, // 离线最大出网流量
                    enablePriority: false, // 启用离线带宽抢占
                },
            },
        };
    }

    validateForm() {
        return this.ref('form').validateFields();
    }

    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);

        if (data) {
            this.data.merge('formData', data.globalSLA || {});
        }
    }

    getFormData() {
        const data = this.data.get('formData');

        return jsyaml.safeDump({globalSLA: data});
    }
}