import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Form, Input, InputNumber, Link} from '@baidu/sui';
import jsyaml from 'js-yaml';

const template = html/* san */`<template>
    <s-form
        class="helm-stroage"
        s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-align="left"
    >
        <s-form-item
            prop="kubeletRootPath"
            label="KubeletRootPath："
            help="用户节点kubelet数据目录，支持录入多个，以回车换行"
        >
            <s-textarea width="400" height="70" value="{=formData.kubeletRootPath=}"></s-textarea>
        </s-form-item>
        <s-form-item
            s-if="!cceCsiPfsPlugin"
            prop="maxVolumesPerNode"
            label="maxVolumesPerNode："
            help="{{maxVolumesPerNodeHelp}}"
        >
            <s-input-number
                width="400"
                min="{{0}}"
                value="{=formData.maxVolumesPerNode=}"
            ></s-input-number>
        </s-form-item>
    </s-form>
</template>`;

@decorators.asComponent('@helm-stroage')
export default class HelmStroage extends Component {
    static template = template;
    static components = {
        's-input': Input,
        's-textarea': Input.TextArea,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-link': Link,
    };
    initData() {
        return {
            formData: {
                maxVolumesPerNode: 5,
                kubeletRootPath: `/home/<USER>/kubelet\n/data/kubelet\n/var/lib/kubelet`,
            },
            rules: {
                kubeletRootPath: [{required: true, message: '请输入kubeletRootPath'}],
                maxVolumesPerNode: [{required: true, message: '请输入maxVolumesPerNode'}],
            },
            isConfig: false, // 是否是配置
        };
    }

    static computed = {
        maxVolumesPerNodeHelp() {
            const cceCsiCdsPlugin = this.data.get('cceCsiCdsPlugin');
            return cceCsiCdsPlugin ? '集群中每个节点最多可挂载的cds数量' : '集群中每个节点最多可挂载的BOS PV数量';
        }
    }

    initFormData(comp) {
        this.data.set('isConfig', true);
        const data = jsyaml.load(comp.instance.params);
        const {cceCsiPfsPlugin} = this.data.get();
        if (cceCsiPfsPlugin) {
            const kubeletRootPath = data?.nodes?.map(e => e.kubeletRootPath).join('\n');
            this.data.set('formData.kubeletRootPath', kubeletRootPath);
        } else {
            this.data.set('formData.maxVolumesPerNode', Number(data?.maxVolumesPerNode) || 5);
            const kubeletRootPath = data?.cluster?.nodes?.map(e => e.kubeletRootPath).join('\n');
            kubeletRootPath && this.data.set('formData.kubeletRootPath', kubeletRootPath);
        }
    }
    getFormData() {
        const {cceCsiPfsPlugin, formData} = this.data.get();
        const {kubeletRootPath, maxVolumesPerNode} = formData;
        let kubeletRootPathList = [];
        if (kubeletRootPath) {
            kubeletRootPathList = [...new Set(kubeletRootPath.split('\n').filter(e => e))];
        }
        const nodes = kubeletRootPathList.map(path => ({
            kubeletRootPath: path,
            kubeletRootPathAffinity: true,
        }));
        if (cceCsiPfsPlugin) {
            const data = {
                nodes,
            };
            return jsyaml.safeDump(data);
        } else {
            const data = {
                maxVolumesPerNode,
                cluster: {
                    nodes,
                },
            };
            return jsyaml.safeDump(data);
        }
    }
    validateForm() {
        return this.ref('form')?.validateFields();
    }
}
