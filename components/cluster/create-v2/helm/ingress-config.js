/**
 * @file components/cluster/create-v2/helm/ingress-config.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Input, Select, Alert, Radio, Button, InputNumber} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {Tip} from '@baidu/sui-biz';
import jsyaml from 'js-yaml';

import {TaintsConfig} from '../../taints-config';
import EipConfig from '../master/eip';

const template = html`
    <div class="cce-cluster-helm-ingress-config">
        <s-alert skin="info" showIcon="{{false}}">
            <label>温馨提示：</label>
            <div>
                集群中可部署多个不同配置的 Nginx Ingress Controller，以满足不同的服务访问场景。
                在创建 Ingress 资源对象时选择 IngressClass，即可为其指定相应的 Nginx Ingess
                Controller。
            </div>
        </s-alert>
        <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" isRequired="{{required}}">
            <s-form-item
                prop="ingressClass"
                label="IngressClass名称："
                help="长度为1-18个字符，只能包含数字、小写字母和“-”，以字母开头且尾部只能是字母或数字。"
            >
                <s-input value="{=formData.ingressClass=}" placeholder="请输入IngressClass名称">
                </s-input>
                <s-tip
                    placement="right"
                    content="创建Ingress时将根据IngressClass名称指定选择的Nginx Ingress Controller。"
                />
            </s-form-item>
            <s-form-item
                prop="namespace"
                label="命名空间："
                help="Nginx Ingress Controller将监听指定命名空间下的所有Ingress资源。"
            >
                <s-select
                    value="{=formData.namespace=}"
                    width="202"
                    filterable="{{true}}"
                    filterPlaceholder="请输入命名空间进行查询"
                >
                    <s-option
                        s-for="item in namespaceList"
                        value="{{item.value}}"
                        label="{{item.label}}"
                    >
                    </s-option>
                </s-select>
            </s-form-item>
            <s-form-item prop="kind" label="部署方式：">
                <s-radio-group
                    class="ingress-radio-group"
                    value="{=formData.kind=}"
                    radioType="button"
                >
                    <s-radio label="Deployment部署" value="Deployment" />
                </s-radio-group>
            </s-form-item>
            <s-form-item
                prop="replicaCount"
                label="副本数："
                help="为保证高可用，建议您设置副本数为3，以预防单个Pod失败的情况"
            >
                <s-input-number
                    precision="0"
                    min="1"
                    step="1"
                    value="{=formData.replicaCount=}"
                    width="125"
                >
                </s-input-number>
            </s-form-item>
            <taints-config s-ref="taintsConfig" labels="{{formData.taints}}" />
            <s-form-item
                class="container"
                label="容器配额："
                help="request用于资源预分配，若节点资源少于申请资源量，容器将创建失败。limit用于限制容器使用的资源上限，避免异常情况下消耗过多的节点资源。"
            >
                <div class="container-quota">
                    <s-form-item prop="rCpu" label="CPU：">
                        <s-input addonBefore="request" value="{=formData.rCpu=}" width="60">
                        </s-input>
                    </s-form-item>
                    <label class="resources-separator">-</label>
                    <s-form-item prop="lCpu">
                        <s-input addonBefore="limit" value="{=formData.lCpu=}" width="60">
                        </s-input>
                    </s-form-item>
                    <label class="resources-suffix">核</label>
                </div>
                <div class="container-quota resources-item">
                    <s-form-item prop="rMemory" label="内存：">
                        <s-input addonBefore="request" value="{=formData.rMemory=}" width="60">
                        </s-input>
                    </s-form-item>
                    <label class="resources-separator">-</label>
                    <s-form-item prop="lMemory">
                        <s-input addonBefore="limit" value="{=formData.lMemory=}" width="60">
                        </s-input>
                    </s-form-item>
                    <label class="resources-suffix">MiB</label>
                </div>
            </s-form-item>
            <taints-config
                class="tolerations-config"
                s-ref="tolerationsConfig"
                labels="{{formData.tolerations}}"
                type="tolerations"
            />
            <s-form-item
                prop="service"
                label="服务访问："
                help="自动为Nginx Ingress负载创建一个LoadBalancer Service，若选择公网访问，将在自动创建的BLB实例上挂载EIP。"
            >
                <s-radio-group
                    class="ingress-radio-group"
                    value="{=formData.service=}"
                    radioType="button"
                >
                    <s-radio label="公网" value="eip" />
                    <s-radio label="内网" value="internalIp" />
                </s-radio-group>
            </s-form-item>
            <eip-config
                s-if="isEip"
                ifBuyEip="{{'1'}}"
                serviceType="{{'EIP'}}"
                s-ref="eipConfig"
                labelWidth="{{120}}"
            />
        </s-form>
    </div>
`;
export default class extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-input-number': InputNumber,
        's-select': Select,
        's-option': Select.Option,
        's-button': Button,
        's-icon-refresh': OutlinedRefresh,
        's-alert': Alert,
        's-tip': Tip,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        'taints-config': TaintsConfig,
        'eip-config': EipConfig,
    };
    static computed = {
        isEip() {
            const service = this.data.get('formData.service');
            return service === 'eip';
        },
    };
    initData() {
        return {
            required: false,
            formData: {
                namespace: '',
                kind: 'Deployment',
                replicaCount: 3,
                taints: [],
                tolerations: [],
                rCpu: 0.25,
                rMemory: 256,
                lCpu: 0.5,
                lMemory: 1024,
                service: 'eip',
            },
            rules: {
                ingressClass: [
                    {required: true, message: '请输入IngressClass名称'},
                    {
                        validator: (rule, value, callback) => {
                            const pattern = /^[a-z](([-a-z0-9/-]){0,16}[a-z0-9])?$/;
                            if (!pattern.test(value)) {
                                return callback('IngressClass名称格式不规范');
                            }
                            callback();
                        },
                    },
                ],
                rCpu: [
                    {required: true, message: '请输入'},
                    {
                        validator: (rule, value, callback) => {
                            const pattern = /^[0-9]+(.[0-9]{1,3})?$/;
                            if (!pattern.test(value)) {
                                return callback('请输入正数');
                            }
                            value = parseFloat(value);
                            if (value > Math.pow(2, 63) - 1) {
                                return callback('超过最大值');
                            }
                            const {lCpu} = this.data.get('formData');
                            if (parseFloat(lCpu) < value) {
                                return callback('limit需大于request');
                            }
                            callback();
                        },
                    },
                ],
                lCpu: [
                    {required: true, message: '请输入'},
                    {
                        validator: (rule, value, callback) => {
                            const pattern = /^[0-9]+(.[0-9]{1,3})?$/;
                            if (!pattern.test(value)) {
                                return callback('请输入正数');
                            }
                            value = parseFloat(value);
                            if (value > Math.pow(2, 63) - 1) {
                                return callback('超过最大值');
                            }
                            const {rCpu} = this.data.get('formData');
                            if (value < parseFloat(rCpu)) {
                                return callback('limit需大于request');
                            }
                            callback();
                        },
                    },
                ],
                rMemory: [
                    {required: true, message: '请输入'},
                    {
                        validator: (rule, value, callback) => {
                            const pattern = /^[1-9]\d*$/;
                            if (!pattern.test(value)) {
                                return callback('请输入正整数');
                            }
                            value = parseFloat(value);
                            if (value > Math.pow(2, 63) - 1) {
                                return callback('超过最大值');
                            }
                            const {lMemory} = this.data.get('formData');
                            if (parseFloat(lMemory) < value) {
                                return callback('limit需大于request');
                            }
                            callback();
                        },
                    },
                ],
                lMemory: [
                    {required: true, message: '请输入'},
                    {
                        validator: (rule, value, callback) => {
                            const pattern = /^[1-9]\d*$/;
                            if (!pattern.test(value)) {
                                return callback('请输入正整数');
                            }
                            value = parseFloat(value);
                            if (value > Math.pow(2, 63) - 1) {
                                return callback('超过最大值');
                            }
                            const {rMemory} = this.data.get('formData');
                            if (value < parseFloat(rMemory)) {
                                return callback('limit需大于request');
                            }
                            callback();
                        },
                    },
                ],
            },
            namespaceList: [],
        };
    }
    attached() {
        this.listAppNamespace();
    }
    async listAppNamespace() {
        const {clusterUuid} = this.data.get();
        try {
            const payload = {clusterUuid};
            const {result} = await this.$http.listAppNamespace(payload);

            let namespaceList = _.uniq(
                _.map(_.get(result, 'namespaces', []), item => _.get(item, 'objectMeta.name', '')),
            );
            namespaceList = _.map(namespaceList, item => ({
                label: item,
                value: item,
            }));
            namespaceList.unshift({label: '全部命名空间', value: ''});
            this.data.set('namespaceList', namespaceList);
        } catch (e) {}
    }
    validateForm() {
        const form = this.ref('form');
        const eipConfig = this.ref('eipConfig');
        const taintsConfig = this.ref('taintsConfig');
        const tolerationsConfig = this.ref('tolerationsConfig');
        return Promise.all([
            form.validateFields(),
            taintsConfig.validateForm(),
            tolerationsConfig.validateForm(),
            eipConfig ? eipConfig.validateForm() : Promise.resolve(),
        ]);
    }
    getValue(field) {
        const form = this.ref('form');
        return form.getFieldValues([field]);
    }
    getFormData() {
        const form = this.ref('form');
        const tolerationsConfig = this.ref('tolerationsConfig');
        const {ingressClass, namespace, kind, replicaCount, rCpu, lCpu, rMemory, lMemory, service} =
            form.getFieldValues([
                'ingressClass',
                'namespace',
                'replicaCount',
                'kind',
                'rCpu',
                'lCpu',
                'rMemory',
                'lMemory',
                'limits',
                'service',
            ]);
        const tolerations = tolerationsConfig.getFormData();
        let payload = {
            fullnameOverride: ingressClass + '-ngx-control',
            controller: {
                ingressClass,
                scope: {
                    enabled: !!namespace,
                    namespace: namespace || '',
                },
                kind,
                replicaCount,
                resources: {
                    requests: {
                        cpu: rCpu,
                        memory: rMemory + 'Mi',
                    },
                    limits: {
                        cpu: lCpu,
                        memory: lMemory + 'Mi',
                    },
                },
                tolerations,
                service: {
                    annotations: {},
                },
            },
        };

        if (service === 'internalIp') {
            payload.controller.service.annotations[
                'service.beta.kubernetes.io/cce-load-balancer-internal-vpc'
            ] = true;
        } else {
            const eipConfig = this.ref('eipConfig');
            const eipFormData = eipConfig.getFormData();
            payload.controller.service.annotations[
                'service.beta.kubernetes.io/cce-elastic-ip-billing-method'
            ] = eipFormData.subProductType === 'netraffic' ? 'ByTraffic' : 'ByBandwidth';
            payload.controller.service.annotations[
                'service.beta.kubernetes.io/cce-elastic-ip-bandwidth-in-mbps'
            ] = eipFormData.bandwidthInMbps;
        }
        return jsyaml.safeDump(payload);
    }
    initFormData(comp) {
        const data = jsyaml.load(comp.instance.params);
        return;
    }
}
