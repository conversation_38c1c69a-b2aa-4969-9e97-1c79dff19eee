/**
 * @file components/cluster/create-v2/helm/introduce.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Loading} from '@baidu/sui';

const template = html`
    <template>
        <s-drawer
            s-ref="drawer"
            open="{=open=}"
            title="{{item.title}}"
            size="{{600}}"
            on-close="onClose"
            class="cce-cluster-ai-helm-introduce-drawer">
            <s-loading s-if="!content" size="large" loading="{{true}}"></s-loading>
            <div s-else class="markdown-body">{{content | raw | empty}}</div>
        </s-drawer>
    </template>
`;
export default class extends Component {
    static template = template;
    static components = {
        's-drawer': Drawer,
        's-loading': Loading
    };
    initData() {
        return {
            open: false
        };
    }
    attached() {
        this.getHelmReadme();
    }
    getHelmReadme() {
        const item = this.data.get('item');
        return this.$http.getPublicChartsReadme({
            chartname: item.name,
            version: item.version || item.latestVersion || ''
        })
            .then(({result}) => {
                const data = result.data.replace(/<a href/g, '<a target="_blank" href');
                this.data.set('content', data);
            });
    }
    onClose() {
        this.data.set('open', false);
    }
}
