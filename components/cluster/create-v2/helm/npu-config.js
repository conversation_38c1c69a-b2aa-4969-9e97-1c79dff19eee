import {Component} from 'san';
import {Form, Link, Radio, Alert} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {html} from '@baiducloud/runtime';
import {NpuShareType, NpuVirtualCardNum} from '../../../../utils/enums';
import jsyaml from 'js-yaml';

const template = /* san */ html`
<template>
    <s-form
        s-ref="form"
        data="{=formData=}"
        class="npu-config"
    >
        <s-alert skin="warning" show-icon="{{false}}">
            温馨提示：共享方案一旦确认后不支持修改，请注意。
        </s-alert>
        <s-form-item prop="npuShare" label="NPU共享方案：" class="npu-share-form-item">
            <s-radio-group class="npu-share-radio-group" value="{=formData.npuShare.value=}">
                <s-radio
                    s-for="item in formData.npuShare.datasource"
                    value="{{item.value}}"
                    disabled="{{isUpgrading || (isConfiging && clusterUuid)}}"
                >
                    <span>{{item.text}}</span>
                    <s-tip
                        s-if="{{item.tip}}"
                        placement="left"
                        class="npu-share-tip"
                        content="{{item.tip}}"
                    />
                </s-radio>
            </s-radio-group>
            <div><s-link skin="info">{{getNpuShareDesc}}</s-link></div>
        </s-form-item>
        <s-form-item s-if="formData.npuShare.value" prop="NumVfs" label="虚拟卡数：">
            <s-radio-group
                class="npu-share-radio-group"
                datasource="{{formData.NumVfs.datasource}}"
                value="{=formData.NumVfs.value=}"
            />
        </s-form-item>
    </s-form>
</template>
`;
export default class NpuConfig extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-tip': Tip,
        's-link': Link,
        's-alert': Alert,
    };
    static computed = {
        getNpuShareDesc() {
            // 获取 NPU 共享类型的描述
            const npuShare = this.data.get('formData.npuShare');
            const temp = npuShare.datasource.find(v => v.value === npuShare.value);
            return temp?.desc;
        },
    };
    initData() {
        return {
            formData: {
                npuShare: {
                    datasource: NpuShareType.toArray(),
                    value: NpuShareType.getValueFromAlias('Soft'),
                },
                NumVfs: {
                    datasource: NpuVirtualCardNum.toArray(),
                    value: NpuVirtualCardNum.getValueFromAlias('ThreeZhang'),
                },
            },
        };
    }

    validateForm() {
        const form = this.ref('form');

        return form.validateFields();
    }

    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);

        if (data) {
            this.data.set('formData.npuShare.value', data.XPUUseSriov);
            if (data.XPUSriovNumVfs) {
                this.data.set('formData.NumVfs.value', data.XPUSriovNumVfs);
            }
        }
    }

    getFormData() {
        const {npuShare, NumVfs} = this.data.get('formData');
        const data = {
            XPUUseSriov: npuShare.value,
        };
        if (data.XPUUseSriov) {
            data.XPUSriovNumVfs = NumVfs.value;
        }
        return jsyaml.safeDump(data);
    }
}
