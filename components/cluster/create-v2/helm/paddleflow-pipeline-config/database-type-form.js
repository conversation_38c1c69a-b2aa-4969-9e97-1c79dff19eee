import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Input, Radio} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';

import {DatabaseType} from '../../../../../utils/enums';

// 验证
const rules = {
    databaseType: [{required: true, message: '请选择'}],
    rdsAddress: [{required: true, message: '请输入'}],
    databaseName: [{required: true, message: '请输入'}],
    username: [{required: true, message: '请输入'}],
    password: [{required: true, message: '请输入'}],
};

const template = /* san */ html`
    <template>
        <s-form
            s-ref="database-type-form"
            data="{=formData=}"
            rules="{{rules}}"
            class="database-type-form paddleflow-pipeline-form-common-style"
            label-align="right"
            isRequired
        >
            <s-form-item prop="databaseType" label="数据库类型：" class="form-item-radio">
                <s-radio-group
                    class="item-radio-group"
                    value="{=formData.databaseType=}"
                    datasource="{{formData.databaseTypeDatasource}}"
                    disabled="{{disabledForm}}"
                />
                <s-tip class="database-type-hit" content="{{databaseTypeComputed}}" />
            </s-form-item>
            <template s-if="formData.databaseType === rdsValue">
                <s-form-item label="RDS地址：" prop="rdsAddress">
                    <s-input
                        placeholder="请输入"
                        value="{=formData.rdsAddress=}"
                        disabled="{{disabledForm}}"
                    />
                </s-form-item>
                <s-form-item label="数据库名称：" prop="databaseName">
                    <s-input
                        placeholder="请输入"
                        value="{=formData.databaseName=}"
                        disabled="{{disabledForm}}"
                    />
                </s-form-item>
                <s-form-item label="用户名：" prop="username">
                    <s-input
                        placeholder="请输入"
                        value="{=formData.username=}"
                        disabled="{{disabledForm}}"
                    />
                </s-form-item>
                <s-form-item label="密码：" prop="password">
                    <s-input
                        type="password"
                        placeholder="请输入"
                        autocomplete="new-password"
                        value="{=formData.password=}"
                        disabled="{{disabledForm}}"
                    />
                </s-form-item>
            </template>
        </s-form>
    </template>
`;
export default class DatabaseTypeForm extends Component {
    static template = template;
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-tip': Tip,
        's-input': Input,
    };
    static computed = {
        // 计算 数据库类型后的提示文案
        databaseTypeComputed() {
            const {databaseType} = this.data.get('formData');
            const rdsValue = this.data.get('rdsValue');
            const mysqlValue = this.data.get('mysqlValue');
            if (databaseType === rdsValue) {
                return '若您在生产环境使用，推荐选择百度云RDS实例，更稳定可靠，简单易用；选择集群内置MySQL可能因误删MySQL等操作存在数据丢失风险';
            } else if (databaseType === mysqlValue) {
                return '选择集群内置MySQL不需输入任何信息，后端默认给用户创建';
            }
            return '';
        },
    };

    initData() {
        const databaseType = DatabaseType.toArray();
        // 表单数据
        const formData = {
            // 数据库类型
            databaseTypeDatasource: databaseType,
            databaseType: databaseType[0].value,
            // rds 地址
            rdsAddress: '',
            // 数据库名称
            databaseName: '',
            // 用户名
            username: '',
            // 密码
            password: '',
        };

        return {
            rdsValue: DatabaseType.getValueFromAlias('Rds'),
            mysqlValue: DatabaseType.getValueFromAlias('Mysql'),
            rules: rules,
            formData: {...formData},
            initFormData: {...formData},
        };
    }

    inited() {
        this.watch('open', open => {
            this.nextTick(() => {
                // disabledForm 表示的就是 升级 or 配置
                const disabledForm = this.data.get('disabledForm');
                // 每次打开弹窗，并且是创建的时候 初始化表单数据
                if (open && !disabledForm) {
                    this.resetFormData();
                }
            });
        });
    }

    resetFormData() {
        this.ref('database-type-form')?.resetFields();
        this.data.set('formData', this.data.get('initFormData'));
    }

    // 安装组件前，获取表单的数据
    async getFormData() {
        const formRef = this.ref('database-type-form');
        const formData = this.data.get('formData');
        // 验证
        const validateRes = await formRef.validateFields();
        // 验证通过 返回表单的值
        if (!validateRes) {
            return formData;
        }
    }

    // 修改组件配置，设置表单的值
    setFormData(data) {
        const {
            PF_SELFED_DB_ENABLED, // 是否是 内置的mysql
            PF_DB_DATABASE, // 数据库名
            PF_DB_HOST, // rds地址
            PF_DB_PASSWORD, // 密码
            PF_DB_USER, // 用户名
        } = data;

        const rdsValue = this.data.get('rdsValue');
        const mysqlValue = this.data.get('mysqlValue');

        // 给 formData 赋值
        if (!PF_SELFED_DB_ENABLED) {
            this.data.merge('formData', {
                databaseType: rdsValue,
                rdsAddress: PF_DB_HOST,
                databaseName: PF_DB_DATABASE,
                username: PF_DB_USER,
                password: PF_DB_PASSWORD,
            });
        } else {
            this.data.merge('formData', {
                databaseType: mysqlValue,
                rdsAddress: '',
                databaseName: '',
                username: '',
                password: '',
            });
        }
    }
}
