import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import _ from 'lodash';
import DatabaseTypeForm from './database-type-form';
import PaddleflowServiceConfigForm from './paddleflow-service-config-form';
import {DatabaseType, ServiceType} from '../../../../../utils/enums';
import './index.less';
import {NETWORK_CONF} from '../../../../../pages/cluster/detail/flow/ingress/util';
import {editPaddleflowYamlValue} from '../../../../../utils/util';

const {asComponent, invokeBceSanUI} = decorators;

const template = /* san */ html`
    <template>
        <div class="paddleflow-pipeline-config">
            <database-type-form
                s-ref="database-type-form"
                disabledForm="{{disabledForm}}"
                open="{{open}}"
            />

            <paddleflow-service-config-form
                s-ref="paddleflow-service-config-form"
                clusterUuid="{{clusterUuid}}"
                clusterDetail="{{clusterDetail}}"
                open="{{open}}"
                disabledForm="{{disabledForm}}"
            />
        </div>
    </template>
`;

@asComponent('@paddleflow-pipeline-config')
@invokeBceSanUI
export default class PaddleflowPipelineConfig extends Component {
    static template = template;
    static components = {
        'database-type-form': DatabaseTypeForm,
        'paddleflow-service-config-form': PaddleflowServiceConfigForm,
    };
    initData() {
        return {
            valuesYaml: '', // 最终组件要 安装的yaml
        };
    }

    static computed = {
        // 升级和配置的情况下 禁用表单
        disabledForm() {
            const isConfiging = this.data.get('isConfiging');
            const isUpgrading = this.data.get('isUpgrading');
            return isConfiging || isUpgrading;
        },
    };

    attached() {
        this.watch('open', open => {
            open && this.requestValuesYaml();
        });
    }

    // 获取 values.yaml 模板
    async requestValuesYaml() {
        const item = this.data.get('item');
        try {
            // 要等到 item.version 有值的时候，再去请求，否则请求报错
            const res = await this.$http.getPublicChartsValues({
                chartname: item.name,
                version: item.version || item.latestVersion || '',
            });
            this.data.set('valuesYaml', res?.result?.data || '');
        } catch (err) {
            this.data.set('valuesYaml', '');
        }
    }

    // 获取 database类型 and service类型的数据
    async getFormData() {
        const databaseTypeForm = this.ref('database-type-form');
        const paddleflowServiceConfigForm = this.ref('paddleflow-service-config-form');

        try {
            // 获取 两个表单的值
            const res = await Promise.all([
                databaseTypeForm.getFormData(),
                paddleflowServiceConfigForm.getFormData(),
            ]);
            const obj = {};
            const {databaseType, rdsAddress, databaseName, username, password} = res[0] || {};
            const {serviceType, loadBalancerPortMap, nodePortMap, clusterIpMap, networkTypeForm} =
                res[1] || {};
            // 1. 处理数据库类型 数据
            if (databaseType === DatabaseType.getValueFromAlias('Rds')) {
                obj.PF_SELFED_DB_ENABLED = false; // 是否使用PF内置mysql，true代表使用PF内置mysql
                obj.PF_DB_DATABASE = databaseName; // PF数据库名
                obj.PF_DB_HOST = rdsAddress; // 数据库host
                obj.PF_DB_PASSWORD = password; // 数据库密码
                obj.PF_DB_USER = username; // 用户名
            } else {
                obj.PF_SELFED_DB_ENABLED = true; // 是否使用PF内置mysql，true代表使用PF内置mysql
            }

            // 2. 处理 Service 数据
            obj.PF_SVC_TYPE = serviceType;
            if (serviceType === ServiceType.getValueFromAlias('LoadBalancer')) {
                obj.PF_SERVER_PORT = loadBalancerPortMap[0]
                    ? Number(loadBalancerPortMap[0].servicePort)
                    : 8999; // PF服务内部端口号
                obj.CCE_LB_ID = networkTypeForm.blbId; // blbId
                obj.CCE_LB_SBN_ID = networkTypeForm.subnet; // subnet
                // 如果是公网 再传递 eip
                if (networkTypeForm.networkType === NETWORK_CONF.getValueFromAlias('external')) {
                    obj.PF_LB_IP = networkTypeForm.eip; // CCE侧的EIP,外网模式
                    obj.CCE_LB_INTERNAL = false; // 公网为false
                } else {
                    obj.PF_LB_IP = ''; // CCE侧的EIP,外网模式
                    obj.CCE_LB_INTERNAL = true; // 内网为true
                }
            } else if (serviceType === ServiceType.getValueFromAlias('NodePort')) {
                obj.PF_SERVER_PORT = nodePortMap[0] ? Number(nodePortMap[0].servicePort) : 8999; // PF服务内部端口号
                obj.PF_SERVER_NODE_PORT = nodePortMap[0] ? Number(nodePortMap[0].nodePort) : 30999; // PF服务NodePort对外端口号
            } else if (serviceType === ServiceType.getValueFromAlias('ClusterIp')) {
                obj.PF_SERVER_PORT = clusterIpMap[0] ? Number(clusterIpMap[0].servicePort) : 8999; // PF服务内部端口号
            }
            let valuesYaml = this.data.get('valuesYaml');
            return editPaddleflowYamlValue(valuesYaml, obj); // 给 yaml的value 加上前缀
        } catch (err) {
            return false;
        }
    }

    // 配置 数据
    setFormData(data) {
        const config = _.get(data, 'global', undefined) || {};
        this.ref('database-type-form').setFormData(config);
        this.ref('paddleflow-service-config-form').setFormData(config);
    }
}
