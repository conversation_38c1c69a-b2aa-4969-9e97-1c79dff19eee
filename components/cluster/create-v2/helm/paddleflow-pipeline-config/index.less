.paddleflow-pipeline-config {
    // 弹窗抽屉 表单公用的样式
    .paddleflow-pipeline-form-common-style {
        .s-form-item .s-form-item-label {
            text-align: right;
            width: 95px;
        }
    
        .item-radio-group {
            display: inline-block;
    
            .s-radio-group {
                margin-right: 0;
            }
    
            .s-radio-group .s-radio .s-radio-text {
                vertical-align: 1px;
            }
        }
    
        .form-item-radio {
            display: flex;
            align-items: center;
        }
    }

    // 数据库类型
    .database-type-form {
        .database-type-hit {
            display: inline-block;
            vertical-align: middle;
        }
    }

    // service 类型
    .paddleflow-service-config-form {
        .form-item-radio .s-form-item-control {
            display: flex;
            align-items: center;

            .service-type-hit {
                margin-left: 15px;
                margin-top: 4px;
            }
        }
        .prot-map-table-item {
            .s-row-flex {
                display: flex;

                .s-form-item-control-wrapper {
                    width: 80%;
                }
            }
        }

        .s-table {
            position: relative;

            .s-form-item-error {
                position: absolute;
            }

            .s-form-item {
                margin-bottom: 0;
            }

            .s-table-cell {
                .s-table-cell-text {
                    padding: 25px 0 25px 12px;
                }

                &:first-child .s-table-cell-text {
                    padding-left: 20px;
                }
            }
        }
    }
}
