import {Component} from 'san';
import {decorators} from '@baiducloud/runtime';
import {Form, Input, Radio, Table} from '@baidu/sui'
import {Tip} from '@baidu/sui-biz';
import {ServiceType} from '../../../../../utils/enums';
import {NETWORK_CONF} from '../../../../../pages/cluster/detail/flow/ingress/util';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const rules = {
    loadBalancerPortMap: [],
    nodePortMap: [],
    clusterIpMap: [],
};

const template = /* san */`
<template class="paddleflow-pipeline-form-common-style paddleflow-service-config-form">
    <s-form
        s-ref="paddleflow-service-config-form"
        label-align="right"
        data="{=formData=}"
        rules="{{rules}}"
        errors="{=formErrors=}"
        disabled="{{disabledForm}}"
    >
        <!-- service 类型 -->
        <s-form-item name="serviceType" label="Service类型：" class="form-item-radio">
            <s-radio-group
                class="item-radio-group"
                radioType="button"
                datasource="{{formData.serviceTypeDatasource}}"
                value="{=formData.serviceType=}"
                disabled="{{disabledForm}}"
            />
            <s-tip class="service-type-hit" content="若需要公网访问，推荐选择LoadBalancer或NodePort类型" />
        </s-form-item>

        <!-- loadBalancer -->
        <template s-if="formData.serviceType === loadBalancer && clusterUuid">
            <s-form-item name="networkTypeForm.networkType" label="访问类型：">
                <s-radio-group
                    class="item-radio-group"
                    radioType="button"
                    datasource="{{formData.networkTypeList}}"
                    value="{=formData.networkTypeForm.networkType=}"
                    disabled="{{disabledForm}}"
                />
                <ui-form>
                    <ingress-advanced-configure
                        s-ref="advanced-configure"
                        formData="{=formData.networkTypeForm=}"
                        clusterDetail="{=clusterDetail=}"
                        on-request-status="onAdvancedRequestEnd"
                        disabledForm="{{disabledForm}}"
                        componentType="cce-paddleflow"
                    />
                </ui-form>
            </s-form-item>
            <s-form-item class="prot-map-table-item" label="端口映射：">
                <s-table columns="{{schemaComputed}}" datasource="{{formData.loadBalancerPortMap}}">
                    <s-form-item prop="loadBalancerPortMap[{{rowIndex}}].name" slot="c-name">
                        <span>{{formData.loadBalancerPortMap[rowIndex].name}}</span>
                    </s-form-item>
                    <s-form-item prop="loadBalancerPortMap[{{rowIndex}}].servicePort" slot="c-servicePort">
                        <s-input
                            width="100%"
                            value="{=formData.loadBalancerPortMap[rowIndex].servicePort=}"
                            on-input="onTableControlChange($event, 'loadBalancerPortMap', 'servicePort', rowIndex)"
                            disabled="{{disabledForm}}"
                        />
                    </s-form-item>
                    <s-form-item prop="loadBalancerPortMap[{{rowIndex}}].containerPort" slot="c-containerPort">
                        <span>{{formData.loadBalancerPortMap[rowIndex].containerPort}}</span>
                    </s-form-item>
                    <s-form-item prop="loadBalancerPortMap[{{rowIndex}}].protocol" slot="c-protocol">
                        <span>{{formData.loadBalancerPortMap[rowIndex].protocol}}</span>
                    </s-form-item>
                </s-table>
            </s-form-item>
        </template>

        <!-- nodePort -->
        <template s-if="formData.serviceType === nodePort">
            <s-form-item class="prot-map-table-item" label="端口映射：">
                <s-table columns="{{schemaComputed}}" datasource="{{formData.nodePortMap}}">
                    <s-form-item prop="nodePortMap[{{rowIndex}}].name" slot="c-name">
                        <span>{{formData.nodePortMap[rowIndex].name}}</span>
                    </s-form-item>
                    <s-form-item prop="nodePortMap[{{rowIndex}}].servicePort" slot="c-servicePort">
                        <s-input
                            width="100%"
                            value="{=formData.nodePortMap[rowIndex].servicePort=}"
                            on-input="onTableControlChange($event, 'nodePortMap', 'servicePort', rowIndex)"
                            disabled="{{disabledForm}}"
                        />
                    </s-form-item>
                    <s-form-item prop="nodePortMap[{{rowIndex}}].containerPort" slot="c-containerPort">
                        <span>{{formData.nodePortMap[rowIndex].containerPort}}</span>
                    </s-form-item>
                    <s-form-item prop="nodePortMap[{{rowIndex}}].nodePort" slot="c-nodePort">
                        <s-input
                            width="100%"
                            value="{=formData.nodePortMap[rowIndex].nodePort=}"
                            on-input="onTableControlChange($event, 'nodePortMap', 'nodePort', rowIndex)"
                            disabled="{{disabledForm}}"
                        />
                    </s-form-item>
                    <s-form-item prop="nodePortMap[{{rowIndex}}].protocol" slot="c-protocol">
                        <span>{{formData.nodePortMap[rowIndex].protocol}}</span>
                    </s-form-item>
                </s-table>
            </s-form-item>
        </template>

        <!-- clusterIp -->
        <template s-if="formData.serviceType === clusterIp">
            <s-form-item class="prot-map-table-item" label="端口映射：">
                <s-table columns="{{schemaComputed}}" datasource="{{formData.clusterIpMap}}">
                    <s-form-item prop="clusterIpMap[{{rowIndex}}].name" slot="c-name">
                        <span>{{formData.clusterIpMap[rowIndex].name}}</span>
                    </s-form-item>
                    <s-form-item prop="clusterIpMap[{{rowIndex}}].servicePort" slot="c-servicePort">
                        <s-input
                            width="100%"
                            value="{=formData.clusterIpMap[rowIndex].servicePort=}"
                            on-input="onTableControlChange($event, 'clusterIpMap', 'servicePort', rowIndex)"
                            disabled="{{disabledForm}}"
                        />
                    </s-form-item>
                    <s-form-item prop="clusterIpMap[{{rowIndex}}].containerPort" slot="c-containerPort">
                        <span>{{formData.clusterIpMap[rowIndex].containerPort}}</span>
                    </s-form-item>
                    <s-form-item prop="clusterIpMap[{{rowIndex}}].protocol" slot="c-protocol">
                        <span>{{formData.clusterIpMap[rowIndex].protocol}}</span>
                    </s-form-item>
                </s-table>
            </s-form-item>
        </template>
    </s-form>
</template>
`;

@asComponent('@paddleflow-service-config-form')
@invokeComp('@ingress-advanced-configure')
@invokeBceSanUI
export default class PaddleflowServiceConfigForm extends Component {
    static template = template;
    static components = {
        's-tip': Tip,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-input': Input,
        's-table': Table,
    };
    initData() {
        const serviceType = ServiceType.toArray();
        const networkConfig = NETWORK_CONF.toArray();
        const formData = {
            // service 类型
            serviceTypeDatasource: serviceType,
            serviceType: serviceType[0].value,
            // 访问类型
            networkTypeList: networkConfig,
            networkTypeForm: {
                networkType: networkConfig[0].value,
                blbId: '',
                eip: '',
                subnet: '',
                clusterUuid: '',
            },
            // loadBalancerPort 端口映射
            loadBalancerPortMap: [
                {
                    name: 'listen',
                    servicePort: '',
                    containerPort: '8999',
                    protocol: 'TCP',
                },
            ],
            // nodePort 端口映射
            nodePortMap: [
                {
                    name: 'listen',
                    servicePort: '',
                    containerPort: '8999',
                    nodePort: '',
                    protocol: 'TCP',
                },
            ],
            // clusterIp 端口映射
            clusterIpMap: [
                {
                    name: 'listen',
                    servicePort: '',
                    containerPort: '8999',
                    protocol: 'TCP',
                },
            ]
        };

        return {
            // loadBalancer 的单选值
            loadBalancer: ServiceType.getValueFromAlias('LoadBalancer'),
            // nodePort 的单选值
            nodePort: ServiceType.getValueFromAlias('NodePort'),
            // clusterIp 的单选值
            clusterIp: ServiceType.getValueFromAlias('ClusterIp'),

            rules,
            formErrors: null,
            formData: _.cloneDeep(formData),
            initFormData: _.cloneDeep(formData), // 初始化表单的时候使用
        };
    }
    inited() {
        this.data.set('formData.networkTypeForm.clusterUuid', this.data.get('clusterUuid'));
        this.data.set('initFormData.networkTypeForm.clusterUuid', this.data.get('clusterUuid'));


        this.getTableRules();
        // 监听 映射端口的 数据变化,生成对应的规则
        this.watch('formData.loadBalancerPortMap', v => {
            this.getTableRules();
        });
        this.watch('formData.nodePortMap', v => {
            this.getTableRules();
        });
        this.watch('formData.clusterIpMap', v => {
            this.getTableRules();
        });
    }

    attached() {
        this.watch('open', open => {
            this.nextTick(() => {
                // disabledForm 表示的就是 升级 or 配置
                const disabledForm = this.data.get('disabledForm');
                // 每次打开弹窗，并且是创建的时候 初始化表单数据
                if (open && !disabledForm) {
                    this.resetFormData();
                }
            });
        });
    }

    static computed = {
        // 计算表格的列 column
        schemaComputed() {
            const serviceType = this.data.get('formData.serviceType');
            const loadBalancer = this.data.get('loadBalancer');
            const nodePort = this.data.get('nodePort');
            const clusterIp = this.data.get('clusterIp');
            const schema = [
                {name: 'name', label: '端口名称', width: '25%'},
                {name: 'servicePort', label: '服务端口', width: '25%'},
                {name: 'containerPort', label: '容器端口', width: '25%'},
                {name: 'protocol', label: '协议', width: '25%'},
            ];
            if (serviceType === loadBalancer) {
                return schema;
            } else if (serviceType === nodePort) {
                return [
                    {name: 'name', label: '端口名称', width: '18%'},
                    {name: 'servicePort', label: '服务端口', width: '23%'},
                    {name: 'containerPort', label: '容器端口', width: '18%'},
                    {name: 'nodePort', label: '节点端口', width: '23%'},
                    {name: 'protocol', label: '协议', width: '18%'},
                ];
            } else if (serviceType === clusterIp) {
                return schema;
            }
            return [];
        }
    }

    resetFormData() {
        this.ref('paddleflow-service-config-form')?.resetFields();
        this.data.set('formData', this.data.get('initFormData'));
    }

    // 表格中的数据改变时，更新formData中的数据
    onTableControlChange(e, type, name, index) {
        this.data.set(`formData.${type}[${index}].${name}`, e.value);
    }

    // 获取 表格中的 表单控件的验证rules
    getTableRules() {
        // 拿到 loadBalancerPortMap、nodePortMap、clusterIpMap 这几个的数据结构
        // 循环数据结构，生成rules验证规则，方便在表格中使用
        const {loadBalancerPortMap, nodePortMap, clusterIpMap} = this.data.get('formData');
        // loadBalancerPortMap table的数据 rules
        if (loadBalancerPortMap.length) {
            rules.loadBalancerPortMap = [];
            loadBalancerPortMap.forEach((v, i) => {
                rules.loadBalancerPortMap.push({
                    servicePort: [
                        {required: true, message: '不能为空'},
                    ],
                });
            });
        }
        // nodePortMap table的数据 rules
        if (nodePortMap.length) {
            rules.nodePortMap = [];
            nodePortMap.forEach((v, i) => {
                rules.nodePortMap.push({
                    servicePort: [
                        {required: true, message: '不能为空'},
                    ],
                    nodePort: [
                        {required: true, message: '不能为空'},
                    ]
                });
            });
        }
        // clusterIp table的数据 rules
        if (clusterIpMap.length) {
            rules.clusterIpMap = [];
            clusterIpMap.forEach((v, i) => {
                rules.clusterIpMap.push({
                    servicePort: [
                        {required: true, message: '不能为空'},
                    ],
                });
            });
        }
        this.data.set('rules', {...rules});
    }

    // 创建组件前，获取表单的值
    async getFormData() {
        const formData = this.data.get('formData');
        const paddleflowServiceConfigFormRef = this.ref('paddleflow-service-config-form');

        const validateRes = await paddleflowServiceConfigFormRef.validateFields();
        if (!validateRes) {
            return formData;
        }
    }

    // 检测 blb、elp、subnet 下拉框的数据，是否请求完毕，并且告知祖先组件，有数据正在请求，提交按钮置灰
    onAdvancedRequestEnd({blb, eip, subnet}) {
        this.dispatch('advanced-request-loading', blb || eip || subnet);
    }

    // 修改组件配置，设置表单的值
    setFormData(data) {
        const {
            PF_SVC_TYPE, // service 类型
            CCE_LB_ID, // blb
            PF_LB_IP, // eip
            CCE_LB_SBN_ID, // subnet
            PF_SERVER_PORT, // 服务端口
            PF_SERVER_NODE_PORT, // 节点端口
            CCE_LB_INTERNAL, // 内网为 true 公网为 false
        } = data;

        // 给 formData 赋值
        const loadBalancer = this.data.get('loadBalancer');
        const nodePort = this.data.get('nodePort');
        const clusterIp = this.data.get('clusterIp');
        const networkTypeList = this.data.get('formData.networkTypeList');

        if (PF_SVC_TYPE === loadBalancer) {
            // service 类型
            this.data.set('formData.serviceType', loadBalancer);
            // 访问类型
            const networkType = CCE_LB_INTERNAL
                ? networkTypeList[1].value
                : networkTypeList[0].value;
            this.data.merge('formData.networkTypeForm', {networkType});
            // advanced-configure 组件赋值
            this.nextTick(() => {
                const advancedConfigure = this.ref('advanced-configure');
                advancedConfigure && advancedConfigure.setFormData({
                    blbId: CCE_LB_ID,
                    eip: PF_LB_IP === '0.0.0.0' ? '' : PF_LB_IP,
                    subnet: CCE_LB_SBN_ID,
                });
            });
            // 端口映射
            this.data.set('formData.loadBalancerPortMap[0].servicePort', PF_SERVER_PORT);
        }
        if (PF_SVC_TYPE === nodePort) {
            // service 类型
            this.data.set('formData.serviceType', nodePort);
            // 端口映射
            this.data.set('formData.nodePortMap[0].servicePort', PF_SERVER_PORT);
            this.data.set('formData.nodePortMap[0].nodePort', PF_SERVER_NODE_PORT);
        }
        if (PF_SVC_TYPE === clusterIp) {
            // service 类型
            this.data.set('formData.serviceType', clusterIp);
            // 端口映射
            this.data.set('formData.clusterIpMap[0].servicePort', PF_SERVER_PORT);
        }
    }
}