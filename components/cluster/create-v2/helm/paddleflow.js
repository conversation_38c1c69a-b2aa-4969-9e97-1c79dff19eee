import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Radio, Input, Select, Table, Notification, Loading} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {DatabaseType, ServiceType, NetworkType} from '../../../../utils/enums';
import jsyaml from 'js-yaml';

const template = html`
    <template>
        <s-loading loading="{{!loadSuccess}}">
            <s-form class="paddleflow">
                <s-form-item prop="databaseType" label="数据库类型：">
                    <s-radio-group
                        class="datatype-raido"
                        value="{=databaseType=}"
                        datasource="{{databaseTypes}}"
                        on-change="databaseTypeChange"
                    />
                    <s-tip
                        class="paddleflow-tip"
                        s-if="databaseTypeTip"
                        content="{{databaseTypeTip}}"
                    />
                </s-form-item>
                <template s-if="databaseType === 'rds'">
                    <s-form-item
                        label="RDS地址："
                        prop="rdsAddress"
                        class="required-item {{!rdsAddress ?'error':''}}"
                    >
                        <s-input value="{=rdsAddress=}" />
                    </s-form-item>
                    <s-form-item
                        label="数据库名称："
                        prop="databaseName"
                        class="required-item {{!databaseName ?'error':''}}"
                    >
                        <s-input value="{=databaseName=}" />
                    </s-form-item>
                    <s-form-item
                        label="用户名："
                        prop="username"
                        class="required-item {{!username ?'error':''}}"
                    >
                        <s-input value="{=username=}" />
                    </s-form-item>
                    <s-form-item
                        label="密码："
                        prop="password"
                        class="required-item {{!password ?'error':''}}"
                    >
                        <s-input type="password" value="{=password=}" autocomplete="new-password" />
                    </s-form-item>
                </template>
                <s-form-item label="Service类型：">
                    <s-radio-group
                        radioType="button"
                        datasource="{{serviceTypes}}"
                        value="{=serviceType=}"
                        on-change="serviceTypeChange"
                    />
                    <s-tip
                        class="service-tip"
                        content="若需要公网访问，推荐选择LoadBalancer或NodePort类型"
                    />
                </s-form-item>
                <s-form-item label="访问类型：" s-if="serviceType === 'LoadBalancer'">
                    <s-radio-group
                        radioType="button"
                        datasource="{{networkTypes}}"
                        value="{=networkType=}"
                        on-change="networkTypeChange"
                    />
                </s-form-item>
                <s-form-item label=" " s-if="serviceType === 'LoadBalancer'">
                    <p class="net-msg">
                        绑定已有 BLB 会导致该 BLB 规则被清空，请确认该 BLB 没有被使用
                    </p>
                    <div class="net-item">
                        <span class="net-item-label">指定BLB：</span>
                        <s-select
                            width="200"
                            value="{=blb=}"
                            datasource="{{blbListComputed}}"
                            on-change="blbChange"
                        >
                        </s-select>
                    </div>
                    <div class="net-item" s-if="networkType === 'external'">
                        <span class="net-item-label">指定EIP：</span>
                        <s-select
                            width="200"
                            s-if="!hasPublicIp.value"
                            value="{=eip=}"
                            datasource="{{eipList}}"
                        >
                        </s-select>
                        <s-select
                            width="200"
                            s-if="hasPublicIp.value"
                            value="{=eip=}"
                            datasource="{{hasPublicIp.list}}"
                        >
                        </s-select>
                        <span s-if="hasPublicIp.value" class="form-item-help">
                            该blb已绑定eip,不再提供eip绑定
                        </span>
                    </div>
                    <div class="net-item">
                        <span class="net-item-label">指定VPC子网：</span>
                        <s-select width="200" value="{=subnet=}" datasource="{{subnetList}}">
                        </s-select>
                    </div>
                </s-form-item>
                <s-form-item label="端口映射：">
                    <s-table columns="{{columns}}" datasource="{{[{}]}}">
                        <div slot="c-name">listen</div>
                        <div slot="c-servicePort">
                            <!--
                 <s-input
                        width="100"
                        value="{=servicePort=}"
                        on-input="portChange($event, 'servicePort')"
                    />
                -->
                            8999
                        </div>
                        <div slot="c-containerPort">8999</div>
                        <div slot="c-nodePort">
                            <s-input
                                width="100"
                                value="{=nodePort=}"
                                on-input="portChange($event, 'nodePort')"
                            />
                        </div>
                        <div slot="c-protocol">TCP</div>
                    </s-table>
                </s-form-item>
            </s-form>
        </s-loading>
    </template>
`;
export default class DeepLearning extends Component {
    static template = template;
    static components = {
        's-loading': Loading,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-tip': Tip,
        's-input': Input,
        's-select': Select,
        's-table': Table,
    };
    static computed = {
        databaseTypeTip() {
            const databaseType = this.data.get('databaseType');
            if (databaseType === DatabaseType.Rds) {
                return '若您在生产环境使用，推荐选择百度云RDS实例，更稳定可靠，简单易用；选择集群内置MySQL可能因误删MySQL等操作存在数据丢失风险';
            } else if (databaseType === DatabaseType.Mysql) {
                return '选择集群内置MySQL不需输入任何信息，后端默认给用户创建';
            }
            return '';
        },
        hasPublicIp() {
            const blb = this.data.get('blb');
            const blbList = this.data.get('blbList');
            const selected = blbList.find(e => e.value === blb);
            const data = {
                value: !!selected?.publicIp,
                list: [{text: selected?.publicIp, value: selected?.publicIp, disabled: true}],
            };
            return data;
        },
        blbListComputed() {
            const blbList = this.data.get('blbList');
            const networkType = this.data.get('networkType');
            if (networkType === 'internal') {
                return blbList.filter(e => !e.publicIp);
            } else {
                return blbList;
            }
        },
        columns() {
            const serviceType = this.data.get('serviceType');
            if (serviceType === 'NodePort') {
                return [
                    {name: 'name', label: '端口名称', width: '18%'},
                    {name: 'servicePort', label: '服务端口', width: '23%'},
                    {name: 'containerPort', label: '容器端口', width: '18%'},
                    {name: 'nodePort', label: '节点端口', width: '23%'},
                    {name: 'protocol', label: '协议', width: '18%'},
                ];
            } else {
                return [
                    {name: 'name', label: '端口名称', width: '25%'},
                    {name: 'servicePort', label: '服务端口', width: '25%'},
                    {name: 'containerPort', label: '容器端口', width: '25%'},
                    {name: 'protocol', label: '协议', width: '25%'},
                ];
            }
        },
    };

    initData() {
        return {
            databaseTypes: DatabaseType.toArray(),
            databaseType: DatabaseType.Rds,
            serviceTypes: ServiceType.toArray(),
            serviceType: ServiceType.LoadBalancer,
            networkTypes: NetworkType.toArray(),
            networkType: NetworkType.external,
            blbList: [{text: '新建BLB', value: ''}],
            blb: '',
            eipList: [{text: '新建EIP', value: ''}],
            eip: '',
            subnetList: [],
            subnet: '',
            servicePort: '',
            nodePort: '30999',
        };
    }

    async attached() {
        const clusterUuid = this.data.get('clusterUuid');
        if (clusterUuid) {
            const data = await this.$http.getClusterDetail(clusterUuid);
            this.data.set('vpcUuid', data?.vpcUuid);
            await Promise.all([this.getBlbList(), this.getEipList(), this.getSubnetList()]);
            this.data.set('loadSuccess', true);
        } else {
            this.data.set('loadSuccess', true);
        }
    }

    validateForm() {
        const {databaseType, serviceType, rdsAddress, databaseName, username, password, nodePort} =
            this.data.get();
        if (databaseType === 'rds') {
            if (!rdsAddress) {
                Notification.error('请输入RDS地址');
                return Promise.reject('rdsAddress');
            }
            if (!databaseName) {
                Notification.error('请输入数据库名称');
                return Promise.reject('databaseName');
            }
            if (!username) {
                Notification.error('请输入用户名');
                return Promise.reject('username');
            }
            if (!password) {
                Notification.error('请输入密码');
                return Promise.reject('password');
            }
        }
        if (serviceType === 'NodePort' && !nodePort) {
            Notification.error('请输入节点端口');
            return Promise.reject('nodePort');
        }
        return Promise.resolve();
    }
    async initFormData(comp) {
        await new Promise(resolve => {
            const timer = setInterval(() => {
                if (this.data.get('loadSuccess')) {
                    clearInterval(timer);
                    resolve();
                }
            }, 1000);
        });
        const data = jsyaml.load(comp?.instance?.params);
        if (data) {
            const {
                PF_SELFED_DB_ENABLED,
                PF_DB_HOST,
                PF_DB_DATABASE,
                PF_DB_PASSWORD,
                PF_DB_USER,
                PF_SVC_TYPE,
                CCE_LB_INTERNAL,
                CCE_LB_ID,
                PF_LB_IP,
                CCE_LB_SBN_ID,
                PF_SERVER_NODE_PORT,
            } = data.global || {};

            this.data.set('databaseType', PF_SELFED_DB_ENABLED ? 'mysql' : 'rds');
            this.data.set('rdsAddress', PF_DB_HOST);
            this.data.set('password', PF_DB_PASSWORD);
            this.data.set('username', PF_DB_USER);
            this.data.set('databaseName', PF_DB_DATABASE);
            this.data.set('serviceType', PF_SVC_TYPE);
            this.data.set('networkType', !CCE_LB_INTERNAL ? 'external' : 'internal');
            this.data.set('blb', CCE_LB_ID);
            this.data.set('eip', PF_LB_IP);
            this.data.set('subnet', CCE_LB_SBN_ID);
            this.data.set('nodePort', PF_SERVER_NODE_PORT);
        }
    }

    getFormData() {
        let {
            databaseType,
            databaseName,
            rdsAddress,
            password,
            username,
            serviceType,
            networkType,
            subnet,
            eip,
            blb,
            nodePort,
        } = this.data.get();

        if (serviceType !== 'LoadBalancer') {
            eip = '0.0.0.0';
        }

        const template = `global:\n  PF_SELFED_DB_ENABLED: &selfd_mysql ${
            databaseType !== 'rds'
        }\n  PF_DB_DATABASE: &pf_db_database ${databaseName}\n  PF_DB_HOST: &pf_db_host ${rdsAddress}\n  PF_DB_PASSWORD: &mysqlpwd ${password}\n  PF_DB_USER: &pf_db_user ${username}\n  PF_DB_PORT: &pf_db_port 3306\n  PF_SVC_TYPE: &pf_svc_type ${serviceType}\n  PF_SERVER_NODE_PORT: &pf_server_node_port ${nodePort}\n  PF_LB_IP: &eip "${eip}"\n  CCE_LB_ID: "${blb}"\n  CCE_LB_SBN_ID: "${subnet}"\n  CCE_LB_INTERNAL: ${
            networkType === 'internal'
        }\n  CCE_LB_POD_DERICT: false\n  IMAGE_REPOSITORY: registry.baidubce.com/cce-plugin-dev/paddleflow\n\npaddleflow-server:\n  out_depend_msg:\n    PF_DB_DATABASE: *pf_db_database\n    PF_DB_HOST: *pf_db_host\n    PF_DB_PASSWORD: *mysqlpwd\n    PF_DB_PORT: *pf_db_port\n    PF_DB_USER: *pf_db_user\n  paddleflow_server:\n    service:\n      extra_usr_define_services:\n        paddleflow-server:\n          ports:\n            port-0:\n              nodePort: *pf_server_node_port\n          loadBalancerIP: *eip\n          type: *pf_svc_type\n\n\npaddleflow-db-init:\n  out_depend_msg:\n    PF_DB_DATABASE: *pf_db_database\n    PF_DB_HOST: *pf_db_host\n    PF_DB_PASSWORD: *mysqlpwd\n    PF_DB_PORT: *pf_db_port\n    PF_DB_USER: *pf_db_user\n\n\nmysql-replication:\n  enabled: *selfd_mysql\n  fullnameOverride: *pf_db_host\n  nameOverride: *pf_db_host\n  auth:\n    rootPassword: *mysqlpwd\n    database: *pf_db_database\n    username: *pf_db_user\n    password: *mysqlpwd\n  primary:\n    service:\n      port: *pf_db_port\n\n`;

        return template;
    }

    async getBlbList() {
        const data = await this.$http.appblbInstanceList({
            pageNo: 1,
            pageSize: 1000,
        });
        const list = data?.page?.result || [];
        if (list) {
            const blbList = list.filter(e => e.vpcId === this.data.get('vpcUuid'));
            this.data.set('blbList', [
                {text: '新建BLB', value: ''},
                ...blbList.map(e => ({text: e.name, value: e.shortId, publicIp: e.publicIp})),
            ]);
        }
    }
    async getEipList() {
        const data = await this.$http.eipInstanceList({
            pageNo: 1,
            pageSize: 1000,
        });
        if (data?.page?.result) {
            this.data.set('eipList', [
                {text: '新建EIP', value: ''},
                ...data?.page?.result?.map(e => ({text: e.name, value: e.eip})),
            ]);
        }
    }
    async getSubnetList() {
        const data = await this.$http.getSubnetList({
            subnetTypes: [1],
            vpcId: this.data.get('vpcUuid'),
        });
        if (data?.result) {
            const subnetList = data.result.map(e => ({
                text: `${e.az}(${e.cidr})`,
                value: e.shortId,
            }));
            this.data.set('subnetList', subnetList);
            if (subnetList.length) {
                this.data.set('subnet', subnetList[0].value);
            }
        }
    }
    blbChange() {
        this.nextTick(() => {
            const hasPublicIp = this.data.get('hasPublicIp');
            if (hasPublicIp.value) {
                this.data.set('eip', hasPublicIp?.list?.[0]?.value);
            } else {
                this.data.set('eip', '');
            }
        });
    }
    networkTypeChange() {
        this.data.set('blb', '');
        this.data.set('eip', '');
    }
    serviceTypeChange() {
        this.data.set('blb', '');
        this.data.set('eip', '');
    }
    portChange(e, type) {
        this.data.set(type, e.value);
    }
    databaseTypeChange(e) {
        if (e.value === 'mysql') {
            this.data.set('rdsAddress', 'mysql-standalone');
            this.data.set('databaseName', 'paddleflow');
            this.data.set('username', 'root');
            this.data.set('password', 'Paddle@2022');
        } else {
            this.data.set('rdsAddress', '');
            this.data.set('databaseName', '');
            this.data.set('username', '');
            this.data.set('password', '');
        }
    }
}
