import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {Form, Link, Select, Tooltip, Alert, Button, Input, InputNumber} from '@baidu/sui';
import {OutlinedRefresh, OutlinedQuestion, OutlinedPlus} from '@baidu/sui-icon';
import {STS_ROLE_PFS_PARAM} from '@utils/constants';
import {Tip, AppLegend} from '@baidu/sui-biz';
import {connect, store} from 'san-store';
import jsyaml from 'js-yaml';
import {compareVersion} from '@utils/util';
const $flag = ServiceFactory.resolve('$flag');

import StoreMap from '../../../../pages/cluster/create-v2/store/map';

const template = /* san */ html`<template>
    <s-alert skin="warning" s-if="installDrawerEdit" class="helm-stroage-alert">
        温馨提示：请确认下方组件参数配置，这些参数配置将会生成新的组件运行模板（YAML），系统会新建实例替换原有实例，同时您通过其他途径（如kubectl）修改的参数配置将会被原先的系统默认值覆盖，需要更新后重新设置。请确保集群中有充足的资源以及满足调度策略的节点，否则组件实例无法调度，导致组件无法正常运行。
    </s-alert>
    <s-alert
        skin="warning"
        showIcon="{{false}}"
        s-else-if="{{comp && isUpgrade}}"
        class="helm-stroage-alert"
    >
        <div slot="description">
            <s-alert skin="warning" showIcon="{{false}}">
                <div slot="description">
                    <div>
                        1、组件版本将由 {{comp.instance.installedVersion}} 升级至
                        {{comp.instance.upgradeInfo.nextVersion}}。
                    </div>
                    <div>
                        2、建议您升级前通过版本迭代记录查看变更内容和影响，具体信息请见
                        <a href="{{comp.doc}}" target="_blank">组件介绍</a>。
                    </div>
                    <div>
                        3、请确认下方组件参数配置，这些参数配置将会生成新的组件运行模板（YAML），同时您通过其他途径（如kubectl）修改的参数配置将会被原先的系统默认值覆盖，需要升级后重新设置。
                    </div>
                </div>
            </s-alert>
        </div>
    </s-alert>
    <s-alert s-else skin="info" class="helm-stroage-alert">
        请确保集群中有充⾜的资源以及满⾜调度策略的节点，否则组件实例⽆法调度，导致组件⽆法正常运⾏。
    </s-alert>
    <s-form
        class="helm-stroage quota-scheduler-container"
        s-ref="form"
        data="{=formData=}"
        rules="{{rules}}"
        label-align="left"
    >
        <s-app-legend label="参数配置" noHighlight="true">
            <s-form-item prop="mountTargetId" label="关联挂载服务：">
                <p
                    s-if="{{isUpgrade || installDrawerEdit}}"
                    class="{{!defaultMountTargetInfo ? 'default-target-delete' : defaultMountTargetInfo.disabled ? 'default-target-disabled' : 'default-target-info'}}"
                >
                    {{defaultMountTargetInfo && defaultMountTargetInfo.label ?
                    defaultMountTargetInfo.label : defaultMountTargetId}}
                </p>
                <s-select
                    width="360"
                    height="70"
                    loading="{{loading}}"
                    placeholder="请选择挂载服务"
                    value="{=formData.mountTargetId=}"
                    s-else
                >
                    <s-option
                        s-for="item in instanceList"
                        label="{{item.label}}"
                        value="{{item.value}}"
                        disabled="{{item.disabled}}"
                    >
                        <s-tooltip s-if="{{item.disabled}}" placement="right">
                            <!--bca-disable-next-line-->
                            <div slot="content">{{item.disabledTip | raw}}</div>
                            <span>{{item.label}}</span></s-tooltip
                        >
                        <span s-else>{{item.label}}</span>
                    </s-option>
                </s-select>
                <s-button
                    s-if="{{!isUpgrade && !installDrawerEdit}}"
                    class="s-icon-button ml-10"
                    on-click="getPfsList"
                    ><s-icon-refresh class="button-icon" is-button="{{false}}"
                /></s-button>
                <template s-if="{{(isUpgrade || installDrawerEdit) && !loading}}">
                    <p s-if="{{!defaultMountTargetInfo}}" class="error-msg">
                        挂载服务（ID：{{defaultMountTargetId}}）不存在，请在<a
                            skin="primary"
                            href="https://console.bce.baidu.com/pfs/#/pfs/mount"
                            target="_blank"
                            >PFS 控制台</a
                        >确认后卸载组件重新安装。
                    </p>
                    <p s-if="{{defaultMountTargetInfo.disabled}}" class="error-msg">
                        <!--bca-disable-next-line-->
                        <span>{{defaultMountTargetInfo.disabledDefaultTip | raw}}</span>
                        <s-button skin="stringfy" class="s-icon-button ml-10" on-click="getPfsList">
                            <s-icon-refresh class="button-icon" is-button="{{false}}" />刷新
                        </s-button>
                    </p>
                </template>
                <div class="error-msg mt4" s-if="!pfsStsRole">
                    检测到并行文件存储 PFS 服务未开通，请先<a
                        href="javascript:void(0)"
                        on-click="openPfsStsRole"
                        >开通 PFS 服务</a
                    >
                </div>
                <div class="help-tip-content" s-if="{{!isUpgrade && !installDrawerEdit}}">
                    仅支持关联与CCE集群同一VPC下的挂载服务。目前CCE集群只能关联一个挂载服务，仅能使用挂载服务绑定的PFS实例。<s-link
                        skin="primary"
                        href="https://console.bce.baidu.com/pfs/#/pfs/mount"
                        target="_blank"
                    >
                        <span>去创建挂载服务</span>
                    </s-link>
                </div>
                <div class="help-tip-content" s-if="{{!isUpgrade && !installDrawerEdit}}">
                    极速型L2并行文件存储（PFS）仅支持CentOS或者Ubuntu操作系统，请确认节点的操作系统类型，以确保组件正常安装。<s-link
                        skin="primary"
                        href="https://cloud.baidu.com/doc/PFS/s/olzz6s5bi"
                        target="_blank"
                    >
                        <span>详细说明</span>
                    </s-link>
                </div>
            </s-form-item>
        </s-app-legend>
        <s-app-legend
            label="调度策略"
            noHighlight="true"
            s-if="{{!installDrawerEdit || versionAvailable}}"
        >
            <s-form-item inline>
                <div slot="help">
                    <span>
                        节点选择器 nodeSelect 可将 Pod 调度到带有特定标签的节点上，且 nodeSelector
                        指定的键值对与节点的标签完全匹配时 Pod 才能被调度。可查看
                    </span>
                    <a
                        target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                        href="https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/assign-pods-nodes/"
                    >
                        节点选择器说明
                    </a>
                </div>
                <template slot="label">
                    {{'节点选择:'}}
                    <s-tip class trigger="click" class="inline-tip" skin="question" placement="top">
                        <s-question />
                        <!--bca-disable-next-line-->
                        <div slot="content" class="node-tip-content">{{nodeTip | raw}}</div>
                    </s-tip>
                </template>
                <div class="memory-container">
                    <div class="tag-config-item" s-for="item, index in formData.nodeSelector">
                        <s-input
                            width="220"
                            on-input="nodeInput($event, index, 'key')"
                            value="{=item.key=}"
                            placeholder="请输入标签键"
                        />
                        <s-input
                            width="220"
                            on-input="nodeInput($event, index, 'value')"
                            value="{=item.value=}"
                            placeholder="请输入标签值"
                        />
                        <a class="close" on-click="onRemoveNode(index)">删除</a>
                        <p s-if="{{nodeSelectorError[index]}}" class="error-tip">
                            {{nodeSelectorError[index]}}
                        </p>
                    </div>
                    <s-button
                        skin="stringfy"
                        disabled="{{nodeSelectorErrorShow}}"
                        on-click="onAddNode"
                        ><s-icon-plus class="button-plus-icon" />添加</s-button
                    >
                </div>
            </s-form-item>
            <s-form-item inline label="容忍设置:">
                <div slot="help">
                    <span>
                        节点污点和Pod容忍度共同作用。节点设置污点后，可能避免Pod调度到节点上，或者将Pod从节点驱逐。除非Pod容忍能够节点污点相匹配。可查看
                    </span>
                    <a
                        target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                        href="https://kubernetes.io/zh-cn/docs/concepts/scheduling-eviction/taint-and-toleration/"
                    >
                        污点和容忍说明
                    </a>
                    <s-alert skin="info" s-if="componentDefault">
                        组件默认添加两条容忍策略，用于在节点异常时等待60秒再驱逐组件Deployment实例，提高组件可用性，不建议修改。
                    </s-alert>
                </div>
                <template slot="label">
                    {{'容忍设置:'}}
                    <s-tip class trigger="click" class="inline-tip" skin="question" placement="top">
                        <s-question />
                        <!--bca-disable-next-line-->
                        <div slot="content" class="node-tip-content">{{configTip | raw}}</div>
                    </s-tip>
                </template>
                <div class="memory-container">
                    <div class="tag-config-item" s-for="item, index in formData.tolerations">
                        <s-input
                            width="100"
                            on-input="tolerationInput($event, index, 'key')"
                            value="{=item.key=}"
                            placeholder="请输入键"
                        />
                        <s-select
                            width="90"
                            value="{=item.operator=}"
                            on-change="operatorSelectChange($event, index)"
                            datasource="{{operatorList}}"
                        >
                        </s-select>
                        <s-input
                            on-input="tolerationInput($event, index, 'value')"
                            disabled="{{item.operator === 'Exists'}}"
                            width="100"
                            value="{=item.value=}"
                            placeholder="请输入值"
                        />
                        <s-form-item class="tag-config-item-select" label="Effect">
                            <s-select
                                value="{=item.effect=}"
                                on-change="effectSelectChange($event, index)"
                                width="150"
                                datasource="{{effectList}}"
                            >
                            </s-select>
                        </s-form-item>
                        <s-form-item
                            s-if="item.effect === 'NoExecute'"
                            class="tag-config-item-select"
                            label="容忍时间"
                        >
                            <s-input-number
                                min="{{0}}"
                                value="{=item.tolerationSeconds=}"
                                width="10"
                            />
                            <span>秒</span>
                        </s-form-item>
                        <a class="close" on-click="onRemoveToleration(index)">删除</a>
                        <p s-if="{{tolerationsError[index]}}" class="error-tip">
                            {{tolerationsError[index]}}
                        </p>
                    </div>
                    <s-button
                        skin="stringfy"
                        disabled="{{tolerationsErrorShow}}"
                        on-click="onAddToleration"
                        ><s-icon-plus class="button-plus-icon" />添加容忍</s-button
                    >
                </div>
            </s-form-item>
        </s-app-legend>
    </s-form>
</template>`;

class PfsStroage extends Component {
    static template = template;
    static components = {
        's-app-legend': AppLegend,
        's-tip': Tip,
        's-question': OutlinedQuestion,
        's-icon-plus': OutlinedPlus,
        's-select': Select,
        's-input': Input,
        's-input-number': InputNumber,
        's-option': Select.Option,
        's-form': Form,
        's-form-item': Form.Item,
        's-link': Link,
        's-tooltip': Tooltip,
        's-alert': Alert,
        's-button': Button,
        's-icon-refresh': OutlinedRefresh,
    };

    static computed = {
        nodeSelectorErrorShow() {
            let nodeSelectorError = this.data.get('nodeSelectorError');
            return nodeSelectorError.some(item => item !== '');
        },
        tolerationsErrorShow() {
            let tolerationsError = this.data.get('tolerationsError');
            return tolerationsError.some(item => item !== '');
        },
        isErrorShow() {
            let tolerationsErrorShow = this.data.get('tolerationsErrorShow');
            let nodeSelectorErrorShow = this.data.get('nodeSelectorErrorShow');
            let memoryResourceError = this.data.get('memoryResourceError');
            let cpuResourceError = this.data.get('cpuResourceError');
            return (
                tolerationsErrorShow ||
                nodeSelectorErrorShow ||
                memoryResourceError ||
                cpuResourceError
            );
        },
        componentDefault() {
            const tolerations = this.data.get('formData.tolerations');
            const defualtFir = tolerations.find(
                item =>
                    item.key === 'node.kubernetes.io/not-ready' &&
                    item.operator === 'Exists' &&
                    item.effect === 'NoExecute' &&
                    item.tolerationSeconds === 60,
            );
            const defaultSec = tolerations.find(
                item =>
                    item.key === 'node.kubernetes.io/unreachable' &&
                    item.operator === 'Exists' &&
                    item.effect === 'NoExecute' &&
                    item.tolerationSeconds === 60,
            );
            return defualtFir && defaultSec;
        },
    };
    initData() {
        return {
            $flag,
            formData: {
                mountTargetId: '',
                nodeSelector: [],
                tolerations: [
                    {
                        key: 'node.kubernetes.io/not-ready',
                        operator: 'Exists',
                        effect: 'NoExecute',
                        tolerationSeconds: 60,
                    },
                    {
                        key: 'node.kubernetes.io/unreachable',
                        operator: 'Exists',
                        effect: 'NoExecute',
                        tolerationSeconds: 60,
                    },
                ],
            },
            rules: {
                mountTargetId: [{required: true, message: '请选择关联实例'}],
            },
            instanceList: [],
            comp: null,
            loading: true,
            pfsStsRole: true,
            defaultMountTargetInfo: {}, // 默认值的源信息
            nodeTip:
                '<p><strong>键：</strong><span>由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符</span></p><p><strong>值：</strong><span>可以包含字母数字字符、连字符（-）、下划线（_）或点（.），并且必须以字母数字字符开头和结束；最多为63个字符<span></p>',
            configTip:
                '<p><strong>键：</strong><span>由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符</span></p><p><strong>值：</strong><span>不能为空，输入限制为 只能是由字母数字字符、-、_或.组成的字符串，并且必须以字母数字字符开头和结束；最多为63个字符<span></p>',
            operatorList: [
                {value: 'Equal', label: 'Equal'},
                {value: 'Exists', label: 'Exists'},
            ],
            effectList: [
                {value: '', label: '全部匹配'},
                {value: 'NoSchedule', label: 'NoSchedule'},
                {value: 'PreferNoSchedule', label: 'PreferNoSchedule'},
                {value: 'NoExecute', label: 'NoExecute'},
            ],
            nodeSelectorError: [],
            tolerationsError: [],
        };
    }

    inited() {
        this.checkPfsIam();
    }

    checkPfsIam() {
        this.$http
            .checkStsRole({
                ...STS_ROLE_PFS_PARAM[window.$context.isOnline() ? 'online' : 'offline'],
                accountId: window.$context.getUserId(),
            })
            .then(res => {
                const data = res.result;
                if (data && data.id) {
                    this.data.set('pfsStsRole', true);
                    this.getPfsList();
                } else {
                    this.data.set('pfsStsRole', false);
                }
            })
            .catch(() => this.data.set('pfsStsRole', false));
    }

    openPfsStsRole() {
        return this.$http
            .openIamService(
                {
                    ...STS_ROLE_PFS_PARAM[window.$context.isOnline() ? 'online' : 'offline'],
                    accountId: window.$context.getUserId(),
                },
                {region: 'bj'},
            )
            .then(res => {
                this.data.set('pfsStsRole', true);
                this.getPfsList();
            });
    }

    async getPfsList() {
        try {
            this.data.set('loading', true);
            this.data.set('formData.mountTargetId', '');
            const res = await this.$http.getPfsMountTargetList({
                pageNo: 1,
                pageSize: 1000,
                manner: 'page',
            });
            let instanceList = res?.page?.result?.map(d => {
                const bindInsatanceList =
                    d.mountTargetBindList?.filter(item => item.state === 1) || [];
                const disabled =
                    d.state !== 2 ||
                    !d.primaryInstanceId ||
                    d.mountTargetBindList?.length !== bindInsatanceList.length;
                const disabledTip =
                    d.state !== 2
                        ? '挂载服务状态不正常'
                        : !d.primaryInstanceId
                        ? '未绑定PFS L2 实例或未设置主PFS，前往<a skin="primary" href="https://console.bce.baidu.com/pfs/#/pfs/mount" target="_blank">PFS挂载服务</a>操作'
                        : '绑定的PFS实例状态不正常';

                const disabledDefaultTip =
                    d.state !== 2
                        ? `挂载服务（ID：${d.mountTargetId}）状态不正常，请在 <a skin="primary" href="https://console.bce.baidu.com/pfs/#/pfs/mount" target="_blank">PFS 控制台 </a>及时处理。`
                        : !d.primaryInstanceId
                        ? `挂载服务（ID：${d.mountTargetId}）未绑定PFS L2实例或未设置主PFS，请在 <a skin="primary" href="https://console.bce.baidu.com/pfs/#/pfs/mount" target="_blank">PFS 控制台 </a>及时处理。`
                        : `挂载服务绑定的PFS实例状态不正常，请在 <a skin="primary" href="https://console.bce.baidu.com/pfs/#/pfs/mount" target="_blank">PFS 控制台 </a>及时处理。`;
                return {
                    label: `${d.name || '-'}/${d.mountTargetId}`,
                    value: d.mountTargetId,
                    primaryInstanceId: d.primaryInstanceId,
                    disabled,
                    disabledDefaultTip,
                    disabledTip: disabled ? disabledTip : '',
                    vpcId: d.vpcId,
                };
            });
            const detailVpcId = store.getState('detailVpcId');
            if (detailVpcId) {
                instanceList = instanceList.filter(d => d.vpcId === detailVpcId);
            }
            const defaultMountTargetId = this.data.get('defaultMountTargetId');
            const selectItem = instanceList.find(d => d.value === defaultMountTargetId);
            if (selectItem) {
                this.data.set('defaultMountTargetInfo', selectItem);
                this.data.set('formData.mountTargetId', selectItem.value);
            } else {
                this.data.set('defaultMountTargetInfo', null);
            }
            this.data.set('instanceList', instanceList);
            this.data.set('loading', false);
        } catch (e) {
            console.error(e);
            this.data.set('loading', false);
        }
    }

    operatorSelectChange({value}, index) {
        if (value === 'Exists') {
            this.data.set(`formData.tolerations[${index}].value`, '');
        }
    }
    effectSelectChange({value}, index) {
        if (value === 'NoExecute') {
            this.data.set(`formData.tolerations[${index}].tolerationSeconds`, 1);
        }
    }
    onAddNode() {
        // 如果存在非法标签，则不可再添加新标签
        if (this.checkAllNode()) {
            this.data.push('formData.nodeSelector', {});
        }
    }
    onAddToleration() {
        // 如果存在非法标签，则不可再添加新标签
        if (this.checkAllToleration()) {
            this.data.push('formData.tolerations', {
                operator: 'Exists',
                effect: '',
                tolerationSeconds: 1,
            });
        }
    }
    nodeInput({value}, index, key) {
        this.data.set(`formData.nodeSelector[${index}]['${key}']`, value);
        this.nextTick(() => {
            this.checkAllNode();
        });
    }
    checkAllNode() {
        const errors = [];
        /* eslint-disable max-len */
        const nodeKeyRegex =
            /^(?=.{1,253}(?:\/|$))(?:(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)*[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\/)?[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        const valueRegex = /^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;

        let nodeSelectorList = this.data.get('formData.nodeSelector') || [];
        nodeSelectorList.forEach(item => {
            if (!item.key) {
                errors.push('键不能为空');
            } else if (!nodeKeyRegex.test(item.key)) {
                errors.push(
                    '由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符',
                );
            } else if (item.value && !valueRegex.test(item.value)) {
                errors.push(
                    '仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符',
                );
            } else {
                errors.push('');
            }
        });
        this.data.set('nodeSelectorError', errors);
        return !errors.some(item => item !== '');
    }
    tolerationInput({value}, index, key) {
        this.data.set(`formData.tolerations[${index}]['${key}']`, value);
        this.nextTick(() => {
            this.checkAllToleration();
        });
    }
    checkAllToleration() {
        const errors = [];
        let tolerationList = this.data.get('formData.tolerations') || [];
        /* eslint-disable max-len */
        const tolerationKeyRegex =
            /^(?=.{1,253}(?:\/|$))(?:(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)*[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\/)?[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        const valueRegex = /^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        tolerationList.forEach(item => {
            if (item.operator !== 'Exists' && !item.key) {
                errors.push('键不能为空');
            } else if (item.key && !tolerationKeyRegex.test(item.key)) {
                errors.push(
                    '由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符',
                );
            } else if (item.value && !valueRegex.test(item.value)) {
                errors.push(
                    '仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符',
                );
            } else {
                errors.push('');
            }
        });
        this.data.set('tolerationsError', errors);
        return !errors.some(item => item !== '');
    }
    onRemoveNode(index) {
        this.data.removeAt('formData.nodeSelector', index);
        this.checkAllNode();
    }
    onRemoveToleration(index) {
        this.data.removeAt('formData.tolerations', index);
        this.checkAllToleration();
    }

    initFormData(comp) {
        this.data.set('comp', comp);
        const data = jsyaml.load(comp?.instance?.params);
        if (data?.storage?.mountTargetId || data?.Storage?.MountTargetId) {
            this.data.set(
                'formData.mountTargetId',
                data?.storage?.mountTargetId || data?.Storage?.MountTargetId,
            );
            this.data.set(
                'defaultMountTargetId',
                data?.storage?.mountTargetId || data?.Storage?.MountTargetId,
            );
        }
        if (data?.NodeSelector) {
            const nodeSelector = Object.keys(data.NodeSelector).map(key => {
                return {key, value: data.NodeSelector[key]};
            });
            this.data.set('formData.nodeSelector', nodeSelector);
        }
        data?.Tolerations && this.data.set('formData.tolerations', data.Tolerations);
        // 编辑时大于等于1.0.9版本才支持权限策略;一共有：安装、编辑、更新；只有编辑需要兼容老的版本
        if (compareVersion(comp?.instance?.installedVersion, '1.0.8')) {
            this.data.set('versionAvailable', true);
        } else {
            this.data.set('versionAvailable', false);
        }
    }
    getFormData() {
        const {formData, instanceList} = this.data.get();
        const {mountTargetId, nodeSelector, tolerations} = formData;
        const instance = instanceList.find(d => d.value === mountTargetId);
        const ValidData = {};
        nodeSelector.forEach(item => {
            item.key && (ValidData[item.key] = item.value || '');
        });
        tolerations.forEach(item => {
            if (item.effect !== 'NoExecute') {
                delete item.tolerationSeconds;
            }
        });
        return jsyaml.safeDump({
            storage: {
                pfsId: instance.primaryInstanceId,
                mountTargetId: mountTargetId,
            },
            nodeSelector: ValidData,
            tolerations,
        });
    }
    validateForm() {
        const {installDrawerEdit, versionAvailable, defaultMountTargetInfo, isUpgrade} =
            this.data.get('');
        // 编辑的时候校验fps信息
        if (installDrawerEdit || isUpgrade) {
            if (!defaultMountTargetInfo || defaultMountTargetInfo.disabled) {
                return Promise.reject('PFS信息异常');
            }
        }
        if (installDrawerEdit && !versionAvailable) {
            return this.ref('form')?.validateFields();
        }
        if (
            this.checkAllNode() &&
            this.checkAllToleration() &&
            this.ref('form')?.validateFields()
        ) {
            return Promise.resolve();
        } else {
            return Promise.reject();
        }
    }
}

@decorators.asComponent('@pfs-stroage')
export default class PfsStroageStore extends connect.san(StoreMap)(PfsStroage) {}
