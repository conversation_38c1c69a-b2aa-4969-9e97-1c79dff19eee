import {Component} from 'san';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {Form, Input, InputNumber, Select, Alert, Button} from '@baidu/sui';
import {Tip, AppLegend} from '@baidu/sui-biz';
import {OutlinedQuestion, OutlinedPlus, Cos} from '@baidu/sui-icon';
import jsyaml from 'js-yaml';
const $flag = ServiceFactory.resolve('$flag');

/* eslint-disable max-len */
const template = /* san */ html`
    <template class="quota-scheduler-container">
        <s-alert skin="warning" s-if="installDrawerEdit">
            温馨提示：请确认下方组件参数配置，这些参数配置将会生成新的组件运行模板（YAML），系统会新建实例替换原有实例，同时您通过其他途径（如kubectl）修改的参数配置将会被原先的系统默认值覆盖，需要更新后重新设置。请确保集群中有充足的资源以及满足调度策略的节点，否则组件实例无法调度，导致组件无法正常运行。
        </s-alert>
        <s-alert s-else skin="info">
            请确保集群中有充⾜的资源以及满⾜调度策略的节点，否则组件实例⽆法调度，导致组件⽆法正常运⾏。
        </s-alert>
        <s-form data="{=formData=}">
            <s-app-legend label="规格配置" noHighlight="true">
                <s-form-item inline label="{{titleName}}:">
                    <div class="memory-container">
                        <s-form-item inline label="CPU:">
                            <s-input
                                addonBefore="request"
                                width="140"
                                on-change="checkResourceData"
                                value="{=formData.resource.requestsCPU=}"
                                placeholder="请输入request"
                            />
                            <s-input
                                addonBefore="limit"
                                on-change="checkResourceData"
                                value="{=formData.resource.limitCPU=}"
                                width="140"
                                placeholder="请输入limit"
                            />
                        </s-form-item>
                        <p s-if="cpuResourceError" class="error-tip">{{cpuResourceError}}</p>
                        <s-form-item inline label="内存:">
                            <s-input
                                addonBefore="request"
                                width="140"
                                on-change="checkResourceData"
                                placeholder="请输入request"
                                value="{=formData.resource.requestsMemory=}"
                            />
                            <s-input
                                addonBefore="limit"
                                value="{=formData.resource.limitMemory=}"
                                on-change="checkResourceData"
                                width="140"
                                placeholder="请输入limit"
                            />
                        </s-form-item>
                        <p s-if="memoryResourceError" class="error-tip">{{memoryResourceError}}</p>
                    </div>
                </s-form-item>
            </s-app-legend>
            <s-app-legend label="调度策略" noHighlight="true">
                <s-form-item inline>
                    <div slot="help">
                        <span>
                            节点选择器 nodeSelect 可将 Pod 调度到带有特定标签的节点上，且
                            nodeSelector 指定的键值对与节点的标签完全匹配时 Pod 才能被调度。可查看
                        </span>
                        <a
                            target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                            href="https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/assign-pods-nodes/"
                        >
                            节点选择器说明
                        </a>
                    </div>
                    <template slot="label">
                        {{'节点选择:'}}
                        <s-tip
                            class
                            trigger="click"
                            class="inline-tip"
                            skin="question"
                            placement="top"
                        >
                            <s-question />
                            <!--bca-disable-next-line-->
                            <div slot="content" class="node-tip-content">{{nodeTip | raw}}</div>
                        </s-tip>
                    </template>
                    <div class="memory-container">
                        <div class="tag-config-item" s-for="item, index in formData.nodeSelector">
                            <s-input
                                width="220"
                                on-input="nodeInput($event, index, 'key')"
                                value="{=item.key=}"
                                placeholder="请输入标签键"
                            />
                            <s-input
                                width="220"
                                on-input="nodeInput($event, index, 'value')"
                                value="{=item.value=}"
                                placeholder="请输入标签值"
                            />
                            <a class="close" on-click="onRemoveNode(index)">删除</a>
                            <p s-if="{{nodeSelectorError[index]}}" class="error-tip">
                                {{nodeSelectorError[index]}}
                            </p>
                        </div>
                        <s-button
                            skin="stringfy"
                            disabled="{{nodeSelectorErrorShow}}"
                            on-click="onAddNode"
                            ><s-icon-plus class="button-plus-icon" />添加</s-button
                        >
                    </div>
                </s-form-item>
                <s-form-item inline label="容忍设置:">
                    <div slot="help">
                        <span>
                            节点污点和Pod容忍度共同作用。节点设置污点后，可能避免Pod调度到节点上，或者将Pod从节点驱逐。除非Pod容忍能够节点污点相匹配。可查看
                        </span>
                        <a
                            target="{{$flag.CceSupportXS ? '_self' : '_blank'}}"
                            href="https://kubernetes.io/zh-cn/docs/concepts/scheduling-eviction/taint-and-toleration/"
                        >
                            污点和容忍说明
                        </a>
                    </div>
                    <template slot="label">
                        {{'容忍设置:'}}
                        <s-tip
                            class
                            trigger="click"
                            class="inline-tip"
                            skin="question"
                            placement="top"
                        >
                            <s-question />
                            <!--bca-disable-next-line-->
                            <div slot="content" class="node-tip-content">{{configTip | raw}}</div>
                        </s-tip>
                    </template>
                    <div class="memory-container">
                        <div class="tag-config-item" s-for="item, index in formData.tolerations">
                            <s-input
                                width="100"
                                on-input="tolerationInput($event, index, 'key')"
                                value="{=item.key=}"
                                placeholder="请输入键"
                            />
                            <s-select
                                width="90"
                                value="{=item.operator=}"
                                on-change="operatorSelectChange($event, index)"
                                datasource="{{operatorList}}"
                            >
                            </s-select>
                            <s-input
                                on-input="tolerationInput($event, index, 'value')"
                                disabled="{{item.operator === 'Exists'}}"
                                width="100"
                                value="{=item.value=}"
                                placeholder="请输入值"
                            />
                            <s-form-item class="tag-config-item-select" label="Effect">
                                <s-select
                                    value="{=item.effect=}"
                                    on-change="effectSelectChange($event, index)"
                                    width="160"
                                    datasource="{{effectList}}"
                                >
                                </s-select>
                            </s-form-item>
                            <s-form-item
                                s-if="item.effect === 'NoExecute'"
                                class="tag-config-item-select"
                                label="容忍时间"
                            >
                                <s-input-number
                                    min="{{0}}"
                                    value="{=item.tolerationSeconds=}"
                                    width="10"
                                />
                                <span>秒</span>
                            </s-form-item>
                            <a class="close" on-click="onRemoveToleration(index)">删除</a>
                            <p s-if="{{tolerationsError[index]}}" class="error-tip">
                                {{tolerationsError[index]}}
                            </p>
                        </div>
                        <s-button
                            skin="stringfy"
                            disabled="{{tolerationsErrorShow}}"
                            on-click="onAddToleration"
                            ><s-icon-plus class="button-plus-icon" />添加容忍</s-button
                        >
                    </div>
                </s-form-item>
            </s-app-legend>
        </s-form>
    </template>
`;
export default class GpuConfig extends Component {
    static template = template;
    static components = {
        's-app-legend': AppLegend,
        's-form': Form,
        's-button': Button,
        's-form-item': Form.Item,
        's-input': Input,
        's-tip': Tip,
        's-question': OutlinedQuestion,
        's-select': Select,
        's-alert': Alert,
        's-icon-plus': OutlinedPlus,
        's-input-number': InputNumber,
    };
    static computed = {
        nodeSelectorErrorShow() {
            let nodeSelectorError = this.data.get('nodeSelectorError');
            return nodeSelectorError.some(item => item !== '');
        },
        tolerationsErrorShow() {
            let tolerationsError = this.data.get('tolerationsError');
            return tolerationsError.some(item => item !== '');
        },
        isErrorShow() {
            let tolerationsErrorShow = this.data.get('tolerationsErrorShow');
            let nodeSelectorErrorShow = this.data.get('nodeSelectorErrorShow');
            let memoryResourceError = this.data.get('memoryResourceError');
            let cpuResourceError = this.data.get('cpuResourceError');
            return (
                tolerationsErrorShow ||
                nodeSelectorErrorShow ||
                memoryResourceError ||
                cpuResourceError
            );
        },
    };
    initData() {
        return {
            $flag,
            titleName: '',
            installDrawerEdit: false,
            formData: {},
            nodeTip:
                '<p><strong>键：</strong><span>由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符</span></p><p><strong>值：</strong><span>可以包含字母数字字符、连字符（-）、下划线（_）或点（.），并且必须以字母数字字符开头和结束；最多为63个字符<span></p>',
            configTip:
                '<p><strong>键：</strong><span>由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符</span></p><p><strong>值：</strong><span>不能为空，输入限制为 只能是由字母数字字符、-、_或.组成的字符串，并且必须以字母数字字符开头和结束；最多为63个字符<span></p>',
            operatorList: [
                {value: 'Equal', label: 'Equal'},
                {value: 'Exists', label: 'Exists'},
            ],
            effectList: [
                {value: '', label: '全部匹配'},
                {value: 'NoSchedule', label: 'NoSchedule'},
                {value: 'PreferNoSchedule', label: 'PreferNoSchedule'},
                {value: 'NoExecute', label: 'NoExecute'},
            ],
            nodeSelectorError: [],
            tolerationsError: [],
            memoryResourceError: '',
            cpuResourceError: '',
        };
    }

    inited() {
        if (!this.data.get('installDrawerEdit')) {
            this.data.set('formData', this.getDefaultFormData());
        }
        this.watch('isErrorShow', value => {
            this.fire('confirmDisabled', !!value);
        });
    }

    validateForm() {
        if (this.checkAllNode() && this.checkAllToleration() && this.checkResourceData()) {
            return Promise.resolve();
        } else {
            return Promise.reject();
        }
    }

    getDefaultFormData() {
        if (this.data.get('titleName') === 'CoreDNS') {
            return {
                resource: {
                    limitCPU: '2000m',
                    limitMemory: '2000Mi',
                    requestsCPU: '200m',
                    requestsMemory: '400Mi',
                },
                nodeSelector: [],
                tolerations: [],
            };
        }
        return {
            resource: {
                limitCPU: '500m',
                limitMemory: '500Mi',
                requestsCPU: '300m',
                requestsMemory: '300Mi',
            },
            nodeSelector: [],
            tolerations: [],
        };
    }

    initFormData(comp) {
        // 升级时初始化参数
        const data = jsyaml.load(comp?.instance?.params);
        const formData = {...data};
        const defaultFormData = this.getDefaultFormData();
        if (data.nodeSelector) {
            formData.nodeSelector = Object.keys(data.nodeSelector).map(key => {
                return {key, value: data.nodeSelector[key]};
            });
        }
        this.data.set('formData', {...defaultFormData, ...formData});
    }

    getFormData() {
        const {nodeSelector, resource, tolerations} = this.data.get('formData');
        const formData = this.data.get('formData');
        const ValidData = {};
        nodeSelector.forEach(item => {
            item.key && (ValidData[item.key] = item.value || '');
        });
        tolerations.forEach(item => {
            if (item.effect !== 'NoExecute') {
                delete item.tolerationSeconds;
            }
        });
        return jsyaml.safeDump({
            ...formData,
            nodeSelector: ValidData,
            resource,
            tolerations,
        });
    }
    operatorSelectChange({value}, index) {
        if (value === 'Exists') {
            this.data.set(`formData.tolerations[${index}].value`, '');
        }
    }
    effectSelectChange({value}, index) {
        if (value === 'NoExecute') {
            this.data.set(`formData.tolerations[${index}].tolerationSeconds`, 1);
        }
    }
    onAddNode() {
        // 如果存在非法标签，则不可再添加新标签
        if (this.checkAllNode()) {
            this.data.push('formData.nodeSelector', {});
        }
    }
    onAddToleration() {
        // 如果存在非法标签，则不可再添加新标签
        if (this.checkAllToleration()) {
            this.data.push('formData.tolerations', {
                operator: 'Exists',
                effect: '',
                tolerationSeconds: 1,
            });
        }
    }
    checkResourceData() {
        const getCpuValue = value => {
            let number = parseFloat(value);
            if (String(value).includes('m')) {
                return number;
            } else {
                return number * 1000;
            }
        };
        const getMemoryValue = value => {
            const units = {
                Ei: Math.pow(2, 60),
                Pi: Math.pow(2, 50),
                Ti: Math.pow(2, 40),
                Gi: Math.pow(2, 30),
                Mi: Math.pow(2, 20),
                Ki: Math.pow(2, 10),
                E: Math.pow(10, 18),
                P: Math.pow(10, 15),
                T: Math.pow(10, 12),
                G: Math.pow(10, 9),
                M: Math.pow(10, 6),
                k: Math.pow(10, 3),
            };
            const parten =
                /^(?:([1-9]\d*(?:\.\d*)?|0|0\.\d*))((?:Ei|Pi|Ti|Gi|Mi|Ki|E|P|T|G|M|k)?)$/;
            const match = String(value).match(parten);

            const number = parseFloat(value);
            const unit = match[2];
            if (unit) {
                return number * units[unit];
            } else {
                return number;
            }
        };
        const resource = this.data.get('formData.resource');
        let memoryResourceError = '';
        let cpuResourceError = '';
        const cpuRegex =
            /^(?:[1-9]\d*(?:\.\d*)?|0|0\.[1-9]\d*|[1-9]\d*\.?\d*m|0\.0[1-9]\d*?|0\.00[1-9]\d*?)$/;
        const memoryRegex = /^(?:[1-9]\d*(?:\.\d*)?|0|0\.\d*)(?:Ei|Pi|Ti|Gi|Mi|Ki|E|P|T|G|M|k)?$/;
        if (resource.limitCPU && resource.requestsCPU) {
            const limitCPU = resource.limitCPU;
            const requestsCPU = resource.requestsCPU;
            if (!cpuRegex.test(limitCPU) || !cpuRegex.test(requestsCPU)) {
                cpuResourceError += '请输入合法的 CPU 大小，例如 500m、1.5 等';
            } else if (getCpuValue(limitCPU) < getCpuValue(requestsCPU)) {
                cpuResourceError += 'limit应该大于等于request';
            }
        } else {
            cpuResourceError += '请输入合法的 CPU 大小，例如 500m、1.5 等';
        }
        if (resource.limitMemory && resource.requestsMemory) {
            const limitMemory = resource.limitMemory;
            const requestsMemory = resource.requestsMemory;
            if (!memoryRegex.test(limitMemory) || !memoryRegex.test(requestsMemory)) {
                memoryResourceError += '请输入合法的内存大小，例如 500、 500Mi、1Gi 等';
            } else if (getMemoryValue(limitMemory) < getMemoryValue(requestsMemory)) {
                memoryResourceError += 'limit应该大于等于request';
            }
        } else {
            memoryResourceError += '请输入合法的内存大小，例如 500、 500Mi、1Gi 等';
        }
        this.data.set('cpuResourceError', cpuResourceError);
        this.data.set('memoryResourceError', memoryResourceError);
        return !cpuResourceError && !memoryResourceError;
    }
    nodeInput({value}, index, key) {
        this.data.set(`formData.nodeSelector[${index}]['${key}']`, value);
        this.nextTick(() => {
            this.checkAllNode();
        });
    }
    checkAllNode() {
        const errors = [];
        /* eslint-disable max-len */
        const nodeKeyRegex =
            /^(?=.{1,253}(?:\/|$))(?:(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)*[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\/)?[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        const valueRegex = /^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;

        let nodeSelectorList = this.data.get('formData.nodeSelector') || [];
        nodeSelectorList.forEach(item => {
            if (!item.key) {
                errors.push('键不能为空');
            } else if (!nodeKeyRegex.test(item.key)) {
                errors.push(
                    '由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符',
                );
            } else if (item.value && !valueRegex.test(item.value)) {
                errors.push(
                    '仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符',
                );
            } else {
                errors.push('');
            }
        });
        this.data.set('nodeSelectorError', errors);
        return !errors.some(item => item !== '');
    }
    tolerationInput({value}, index, key) {
        this.data.set(`formData.tolerations[${index}]['${key}']`, value);
        this.nextTick(() => {
            this.checkAllToleration();
        });
    }
    checkAllToleration() {
        const errors = [];
        let tolerationList = this.data.get('formData.tolerations') || [];
        /* eslint-disable max-len */
        const tolerationKeyRegex =
            /^(?=.{1,253}(?:\/|$))(?:(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)*[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\/)?[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        const valueRegex = /^[a-zA-Z0-9]([a-zA-Z0-9._-]{0,61}[a-zA-Z0-9])?$/;
        tolerationList.forEach(item => {
            if (item.operator !== 'Exists' && !item.key) {
                errors.push('键不能为空');
            } else if (item.key && !tolerationKeyRegex.test(item.key)) {
                errors.push(
                    '由前缀和名称组成，用斜杠（/）分隔。名称不能为空，仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符；前缀可选，必须是DNS子域，仅支持小写字母、数字、连字符（-）和点（.），必须以字母或数字开头和结尾，最多253个字符',
                );
            } else if (item.value && !valueRegex.test(item.value)) {
                errors.push(
                    '仅支持字母、数字、连字符（-）、下划线（_）和点（.），必须以字母或数字开头和结尾，最多63个字符',
                );
            } else {
                errors.push('');
            }
        });
        this.data.set('tolerationsError', errors);
        return !errors.some(item => item !== '');
    }
    onRemoveNode(index) {
        this.data.removeAt('formData.nodeSelector', index);
        this.checkAllNode();
    }
    onRemoveToleration(index) {
        this.data.removeAt('formData.tolerations', index);
        this.checkAllToleration();
    }
}
