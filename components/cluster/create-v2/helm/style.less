.cce-cluster-create-helm {
    .bui-legend {
        .highlight {
            display: none;
        }
    }

    .s-alert-skin-warning {
        .s-alert-icon {
            align-self: flex-start;
            position: relative;
            top: 2px;
        }
    }

    .helm-card-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding-right: 16px;
    }

    .s-tabs-horizontal.s-tabs-top {
        .s-tabnav {
            margin-bottom: 0;
        }
    }

    .s-card-invalid {
        border-color: #f33e3e;
    }

    .s-card {
        margin-top: 16px;
        width: 50%;
        &:nth-child(even) {
            transform: translateX(16px);
        }

        .s-card-header {
            background: #f5f5f5;
            padding: 10px 20px;
        }

        .s-card-title {
            font-size: 14px;
            color: #000;
        }

        .s-checkbox {
            .s-checkbox-input {
                vertical-align: middle;
            }

            .s-radio-text {
                vertical-align: middle;
                margin-left: 3px;
            }
        }

        .cce-cluster-helm-ingress-tip {
            position: relative;
            top: 4px;
        }
    }

    .helm-desc {
        .helm-desc-bottom {
            margin-top: 10px;
            height: 18px;
        }

        .helm-desc-item {
            width: 100%;
            display: inline-block;
            zoom: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .helm-operations {
            float: right;

            .s-button-skin-stringfy {
                padding: 0;
                margin: 0 0 0 10px;
            }

            .s-loading {
                position: relative;
                top: 3px;
            }
        }

        .helm-desc-tip {
            color: #f38900;
            margin: 10px 0;
        }
    }

    .helm-selected-list {
        color: #666;
        padding-left: 20px;
        padding-top: 10px;
    }

    .installed-tag {
        float: right;
        background: #2cb663;
        line-height: 15px;
        border-radius: 15px;
        padding: 0 6px;
        font-size: 12px;
        color: #fff;
    }

    .status.rolling {
        float: right;

        &:before {
            color: #f33e3e;
        }
    }

    .install-fail {
        float: right;
        color: #f33e3e;
        font-size: 12px;
    }
}

.detail-container {
    .cce-cluster-create-helm {
        .s-alert-skin-warning {
            height: auto;
            margin: 0 0 16px;
        }
    }
}

.cce-cluster-helm-release-confirm {
    width: 400px;

    .helm-release-info {
        margin-top: 10px;
    }

    .s-radio-text {
        vertical-align: 2px;
    }
}

.cce-cluster-ai-helm-introduce-drawer {
    .markdown-body {
        font-size: 12px;
    }
}

.cce-cluster-helm-ingress-config {
    .s-alert-content {
        display: flex;

        label {
            width: 125px;
        }
    }

    .s-row {
        margin-top: 20px;

        .s-form-item-label {
            width: 137px;
        }

        .s-form-item-help {
            padding: 10px 0 0 0;
        }

        .s-trigger-container {
            vertical-align: middle;
        }
    }

    .ingress-radio-group {
        display: inline-block;
        zoom: 1;
        vertical-align: middle;
    }

    .bui-form {
        .bui-form-item-inline {
            .bui-form-item-label {
                width: 127px !important;
                font-size: 12px;
            }
        }

        .bui-form-item {
            .bui-form-item-help {
                padding: 10px 0 0 0;
                color: #666;
            }

            .required-label {
                &:before {
                    display: none;
                }
            }
        }
    }

    .resources-separator {
        margin: 0 10px;
    }

    .resources-suffix {
        margin-left: 10px;
    }

    .resources-item {
        margin-top: 10px;
    }

    .tolerations-config {
        .cce-add-tag {
            margin-top: 20px;
        }
    }

    .cce-add-tag {
        font-size: 0;
    }

    .eip-config-panel {
        .bui-form-item-name {
            display: none;
        }

        .color-gray-light {
            color: #666;
        }

        .sub-item,
        .multi-line-item {
            margin-left: 120px;
        }

        .bui-form-item {
            font-size: 0;

            .bui-form-item-label {
                font-size: 12px;
                margin-right: -12px;
            }
        }
    }

    .container-quota {
        .s-form-item {
            display: inline-block;
            zoom: 1;
            vertical-align: middle;
            margin: 0;
        }

        .s-row {
            margin: 0;

            .s-form-item-label {
                width: 50px;
            }
        }
    }
}

.cce-cluster-helm-upgrade-icon {
    position: relative;
    top: 30px;
}

.cce-helm-upgrade-confirm-wrap {
    height: 90px;
}

.cce-helm-upgrade-tip {
    position: absolute;
    width: 490px;
    top: 60px;
    left: 30px;
}

.cce-helm-upgrade-confirm-tip {
    position: relative;
    top: 65px;
}

.cce-cluster-helm-hybrid-config {
    .cce-helm-hybrid-form {
        padding: 20px;
        width: 460px;

        .s-form-item-label {
            width: 140px;
            text-align: left;
        }
    }

    .cce-helm-hybrid-suffix {
        margin: 0 5px;
    }

    .s-input {
        background: #fff;
    }

    .s-trigger-container {
        vertical-align: middle;
    }
}

.gpu-config {
    .gpu-share-tip .s-tip {
        vertical-align: sub !important;
        position: relative;
        top: 4px;
    }
}

.err-autofix {
    display: block;
    max-height: calc(100vh - 140px);
    overflow: auto;
    .s-row-flex {
        flex-wrap: nowrap;
    }
    .s-form-item-label-left {
        width: 100px;
        flex-shrink: 0;
    }
    ul {
        list-style: none;
        li {
            padding: 10px;
            background-color: #f5f5f5;
            margin-top: 4px;
            .ml63 {
                margin-left: 63px;
            }
            & > .item {
                margin-bottom: 14px;
                &:last-child {
                    margin-bottom: 0px;
                }
            }
            .action .s-checkbox .s-radio-text {
                font-size: 12px;
                margin-right: 10px;
            }
            .del {
                float: right;
            }
        }
    }
    .s-button {
        padding: 0;
        &.add {
            float: left;
            margin-top: 10px;
            margin-right: 22px;
        }
    }
    .s-select,
    .s-input {
        background: white;
    }
    .repeat-msg,
    .not-steps-msg {
        float: left;
        margin-top: 10px;
        color: #f33e3e;
    }
    .tactics {
        width: 420px;
        .tactics-item {
            display: flex;
            flex-wrap: wrap;
            flex-grow: 0;
            flex-shrink: 0;
            .s-input {
                height: 30px;
            }
            .help {
                flex-basis: 420px;
                padding-left: 116px;
                margin-bottom: 20px;
            }
            &:last-child .help {
                margin-bottom: 0;
            }
            .label {
                flex-basis: 115px;
            }
            span {
                line-height: 34px;
            }
            .unit {
                text-align: center;
                flex-basis: 25px;
            }
        }
    }
    .aksk {
        position: relative;
        left: 100px;
        top: -10px;
    }
}
.quota-scheduler-container {
    .s-form-item-control-wrapper {
        flex-grow: 1;
    }
    .s-form-item-label > label {
        padding-left: 0;
    }
    .s-form-item-help {
        max-width: 740px;
    }
    .button-plus-icon {
        position: relative;
        top: -1px;
        font-size: 0;
    }
    .tag-config-item {
        margin-bottom: 12px;
        .tag-config-item-select {
            display: inline-block;
            margin: 0 !important;
        }
    }
    .s-legend {
        label {
            margin-bottom: 24px;
        }
    }
    .inline-tip {
        top: 3px;
        position: relative;
        .s-tip-question {
            justify-content: center;
        }
    }
    .memory-container {
        background: #f7f7f9;
        padding: 12px 24px;
        border-radius: 6px;
        .s-input {
            margin-right: 6px;
        }
        .s-input-area {
            background: #fff;
        }
        .s-form-item {
            margin: 4px 0;
            .s-input-addon-before {
                width: 60px;
            }
        }
        .s-button-skin-stringfy {
            padding-left: 0;
        }
        .error-tip {
            max-width: 680px;
            padding: 4px 0;
            color: #eb5252;
        }
        .s-form-item-label {
            label {
                padding-left: 0 !important;
            }
            width: 55px;
            text-align: left;
        }
        .s-inputnumber {
            min-width: 60px;
            .s-inputnumber-input {
                padding: 0 6px;
                width: calc(~'100% - 36px');
            }
        }
    }
}
.credential-image {
    margin-top: 10px;
    .memory-containe-alert {
        width: 470px;
    }
    .error-tip {
        margin-left: 80px;
        max-width: 470px;
        top: -16px;
        position: relative;
        color: #eb5252;
    }
    .memory-container {
        background: #f7f7f9;
        padding: 12px 24px;
        border-radius: 6px;
        width: 470px;
        .inline-tip {
            top: 3px;
            position: relative;
            .s-tip-question {
                justify-content: center;
            }
        }
        .s-radio {
            margin-right: 10px;
        }
        .s-form-item-label {
            width: 110px;
            text-align: left;
        }
        .s-form-item-help {
            max-width: 310px;
        }
        .custom-credentials {
            display: flex;
            justify-content: space-between;
            padding-left: 110px;
        }
    }
}
.node-tip-content {
    width: 260px;
    p {
        display: flex;
        margin-bottom: 4px;
    }
    span {
        display: block;
        color: #84868c;
    }
}
.help-tip-content {
    color: #84868c;
    max-width: 400px;
    margin-top: 4px;
}
