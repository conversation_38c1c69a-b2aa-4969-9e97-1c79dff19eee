/**
 * @file components/cluster/create-v2/helm/upgrade-confirm.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Dialog} from '@baidu/sui';
import {OutlinedExclamationCircle} from '@baidu/sui-icon';

const template = html`
    <template>
        <s-dialog
            s-ref="dialog"
            open="{=open=}"
            title="升级确认"
            width="550"
            class="s-dialog-instance">
            <div class="s-dialog-icon cce-cluster-helm-upgrade-icon">
                <s-outlined-exclamation-circle
                    class="s-dialog-icon-warning" />
            </div>
            <div class="s-dialog-text">
                <div class="cce-helm-upgrade-confirm-wrap">
                    <div class="tip-warning cce-helm-upgrade-tip">
                        温馨提示：升级可能会影响已有服务，您可以先在【组件介绍-版本记录】中查看变更影响后再确定是否继续升级组件。
                    </div>
                    <div class="cce-helm-upgrade-confirm-tip">
                        您确定升级{{item.title}}组件到最新版吗？
                    </div>
                </div>
            </div>
            <div slot="footer">
                <s-button
                    width="46"
                    on-click="onClose">取消</s-button>
                <s-button
                    width="46"
                    skin="primary"
                    on-click="onConfirm">确认</s-button>
            </div>
        </s-dialog>
    </template>
`;
export default class extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-dialog': Dialog,
        's-outlined-exclamation-circle': OutlinedExclamationCircle
    };
    initData() {
        return {
            open: true
        };
    }
    onClose() {
        this.data.set('open', false);
    }
    onConfirm() {
        this.fire('confirm');
    }
}
