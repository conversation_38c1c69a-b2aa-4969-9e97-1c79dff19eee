import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Checkbox, Radio, Switch, Alert} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import jsyaml from 'js-yaml';
import {GpuBinpack} from '../../../../utils/enums';
import {compareVersion} from '@utils/util';

const template = /* san */ html`
    <template class="volcano">
        <s-form>
            <s-alert skin="warning" showIcon="{{false}}">
                <div slot="description">温馨提示：1.7.12及以上版本默认关闭抢占功能</div>
            </s-alert>
            <s-form-item prop="binpack" label="资源调度：" help="{{Binpack | getBinpackTip}}">
                <s-radio-group
                    value="{=Binpack=}"
                    datasource="{{binpackTypes}}"
                    on-change="bipackChange"
                >
                </s-radio-group>
            </s-form-item>
            <s-form-item prop="overshell" label="任务抢占模式：">
                <div class="job-mod">
                    <div>
                        <span>队列内优先级抢占</span>
                        <s-switch checked="{=Preemptgang=}" />
                    </div>
                    <div class="s-form-item-help">
                        队列内优先级抢占指同一队列中，优先级高的任务可抢占优先级低任务的资源，保障高优先级任务的运行。
                    </div>
                    <div s-if="enableJobPreemptionStrategy && Preemptgang">
                        <div>自定义抢占逻辑：</div>
                        <s-radio-group value="{=strategy=}" datasource="{{strategyDatasource}}" />
                    </div>
                </div>
                <div class="job-mod">
                    <div>
                        <span>队列间超发抢占</span>
                        <s-switch checked="{=Reclaimgang=}" />
                    </div>
                    <div class="s-form-item-help">
                        队列间超发抢占是指A队列资源用满，B队列有空闲资源时，此时若A队列上提交了新任务，将调度到B队列上运行，当B队列上有新任务提交发现资源不足时，将Kill超发任务保障B队列任务运行。
                    </div>
                </div>
            </s-form-item>
        </s-form>
    </template>
`;
export default class Volcano extends Component {
    static template = template;
    static components = {
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-radio': Radio,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-tip': Tip,
        's-switch': Switch,
        's-alert': Alert,
    };

    initData() {
        return {
            binpackTypes: GpuBinpack.toArray(),
            Binpack: true,
            Preemptgang: false,
            Reclaimgang: false,
            inJobPreemptionWhiteList: false,
            strategy: true,
            strategyDatasource: [
                {
                    label: '高优先级任务可以抢占中、低优先级任务；中优先级任务可以抢占低优先级任务',
                    value: true,
                },
                {
                    label: '高优先级任务仅可以抢占低优先级任务；中优先级任务可以抢占低优先级任务',
                    value: false,
                },
            ],
            isEdit: false,
        };
    }

    static computed = {
        enableJobPreemptionStrategy() {
            const inJobPreemptionWhiteList = this.data.get('inJobPreemptionWhiteList');
            const isEdit = this.data.get('isEdit');
            const versionAvailable = this.data.get('versionAvailable');

            return inJobPreemptionWhiteList && (!isEdit || versionAvailable);
        },
    };

    static filters = {
        getBinpackTip(value) {
            return GpuBinpack.fromValue(value).tip;
        },
    };

    inited() {
        this.checkEnableJobPreemptionStrategy();
    }

    validateForm() {
        return true;
    }

    initFormData(comp) {
        const data = jsyaml.load(comp?.instance?.params);
        if (data) {
            this.data.set('Binpack', data.Binpack);
            this.data.set('Preemptgang', data.Preemptgang);
            this.data.set('Reclaimgang', data.Reclaimgang);
            this.data.set('strategy', data.PreempteePriorityThreshold !== -10);

            // 编辑时大于等于1.7.3版本才支持自定义抢占策略
            if (!compareVersion('1.7.3', comp.instance.installedVersion || '')) {
                this.data.set('versionAvailable', true);
            }
        }

        this.data.set('isEdit', true);
    }

    getFormData() {
        const {Binpack, Preemptgang, Reclaimgang, enableJobPreemptionStrategy, strategy} =
            this.data.get();
        const data = {
            Binpack,
            Preemptgang,
            Reclaimgang,
        };

        if (enableJobPreemptionStrategy && Preemptgang) {
            data.PreemptorPriorityThreshold = -10;
            data.PreempteePriorityThreshold = strategy ? 0 : -10;
        }

        return jsyaml.safeDump(data);
    }

    async checkEnableJobPreemptionStrategy() {
        // 白名单检查
        const res = await this.$http.checkWhiteList('EnableJobPreemptionStrategy');

        this.data.set('inJobPreemptionWhiteList', !!res?.isExist);
    }
}
