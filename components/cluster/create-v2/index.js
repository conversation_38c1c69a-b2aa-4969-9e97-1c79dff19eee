/**
 * @file components/cluster/create-v2/index.js
 * <AUTHOR>
 */

import './expand/index';
import './instance/transfer';
import './master/add-bbc';
import './master/add-custom';
import './master/add-dialog';
import './master/add-exist';
import './master/index';
import './master/node';
import './master/eip';
import './master/zone-subnets';
import './network/cidr';
import './network/cni';
import './network/index';
import './network/port-mode';
import './network/recommend';
import './network/vpc';
import './network/cluster-ip-cidr';
import './worker/index';
import './advanced';
import './info';
import './script';
import './subnet';
import './style.less';
import './security-group';
import './template/template';
import './template/style.less';
import './shopping-cart';
import './shopping-cart-bec';
import './cluster-check-valid';
import './cluster-check-valid.less';
import './helm/components';
import './helm/style.less';

import './edge-autonomy';
import './data-root';

import './confirm-seting';

import './tag-config';
