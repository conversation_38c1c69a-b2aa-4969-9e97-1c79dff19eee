/**
 * @file components/cluster/create-v2/info.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';
import StoreMap from '../../../pages/cluster/create-v2/store/map';
import {getVpcName, getSubnetName} from '../../../utils/helper';
import {ClusterType} from '../../../utils/enums';
import tips from '../../../utils/tips';
import Rule from '../../../utils/rule';
import {NodeName} from './enums';
import {checkCidrConflict, CloudClusterCIDR, BecVmCIDR} from '../../../utils/network';
import {compareVersion} from '@utils/util';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';
import SubnetTable from '@pages/cluster/create-v2/components/network/subnet-table';
import {Loading} from '@baidu/sui';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const Schema = AsyncValidator.default;

const rules = new Schema({
    clusterName: [
        {required: true, message: '请输入集群名称'},
        {
            pattern: Rule.NAME.pattern,
            message: '支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度1-65',
        },
    ],
    vpcId: [{required: true, message: '请选择VPC网络'}],
    blbSubnetId: {
        validator(rule, value, callback, source) {
            if (_.isUndefined(source.blbSubnetId)) {
                return callback();
            }

            if (!source.blbSubnetId) {
                return callback('请选择Master BLB 子网');
            }

            return callback();
        },
    },
    lbSubnetId: {
        validator(rule, value, callback, source) {
            if (_.isUndefined(source.lbSubnetId)) {
                return callback();
            }

            if (
                !_.isUndefined(source.enableServiceController) &&
                source.enableServiceController !== 'BLB'
            ) {
                return callback();
            }

            if (!source.lbSubnetId) {
                return callback('请选择LB Service 子网');
            }

            return callback();
        },
    },
    description: {
        validator(rule, value, callback, source) {
            const comment = value;
            if (_.isUndefined(comment)) {
                return callback();
            }

            if (comment.length < 0 || comment.length > 250) {
                return callback('备注信息长度不超过250字符');
            }

            return callback();
        },
    },
    apiServerCertSAN: {
        validator(rule, value, callback, source) {
            const arr = value ? value.split(',') : [];
            for (const item of arr) {
                if (!Rule.IPADDRESS.pattern.test(item) && !Rule.HOST.pattern.test(item)) {
                    return callback('请输入正确的IP或者域名');
                }
            }
            return callback();
        },
    },
});

const loadingDatasource = [{text: '加载中...', value: ''}];

const $flag = ServiceFactory.resolve('$flag');

const docker = {
    text: 'Docker 20.10.24',
    value: 'docker',
    version: '20.10.24',
};
const containerd_1_6_28 = {
    text: 'Containerd 1.6.28',
    value: 'containerd',
    version: '1.6.28',
};

/* eslint-disable */
const template = html`<template>
    <ui-biz-legend label="{{title}}">
        <ui-form s-ref="form" rules="{{rules}}" form-data="{=formData=}" errors="{{formErrors}}">
            <ui-form-item
                inline
                required
                label="集群名称："
                name="clusterName"
                help="支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度1-65；集群名称不支持修改，请谨慎输入"
            >
                <ui-text-box
                    placeholder="请输入集群名称"
                    width="240"
                    value="{=formData.clusterName=}"
                />
            </ui-form-item>

            <pay-config
                s-ref="payConfig"
                on-change="onPayConfigChange"
                s-if="!(isServerless || isCloudEdge)"
            />

            <ui-form-item inline label="Kubernetes版本：" name="k8sVersion">
                <div s-if="kubernetes.status === 'loading'">
                    <s-loading loading />
                </div>
                <div s-if="kubernetes.status === 'error'" class="cce-error">
                    数据异常，请刷新重试
                </div>
                <div s-if="kubernetes.status === 'done'">
                    <ui-select
                        s-if="kubernetes.list.length"
                        value="{=formData.k8sVersion=}"
                        datasource="{{kubernetes.list}}"
                        on-change="onVersionChange"
                        width="240"
                    />
                    <div s-else class="cluster-create-form-item-empty">暂无数据</div>
                </div>
            </ui-form-item>

            <ui-form-item
                inline
                label="IPv6双栈网络："
                name="IPversion"
                s-if="!isServerless && !isCloudEdge"
                help="IPv6双栈网络选择后暂不支持变更，请谨慎选择
                    <a href=${tips.doc.ipv6} target=_blank>CCE使用IPv4/IPv6双栈网络说明</a>"
            >
                <ui-tip
                    s-if="!enableIPv6"
                    layerStyle="{{layerStyle}}"
                    position="tc"
                    message="{{ipv6TipMessage}}"
                    in-component="{{true}}"
                    class="disabled-tip"
                >
                    <ui-switch checked="{=formData.IPversion=}" disabled="{{true}}" />
                </ui-tip>
                <ui-switch s-else checked="{=formData.IPversion=}" on-change="onIpVersionChange" />
                <ui-tip
                    layer-width="300"
                    message="开启IPv6双栈网络，将筛选出已配置IPv6的VPC网络和子网，并自动分配IPv6的容器网段和服务网段"
                />
            </ui-form-item>

            <ui-form-item
                inline
                label="容器运行时："
                s-if="!isServerless && !isCloudEdge"
                name="runtime"
            >
                <ui-radio-select
                    value="{=formData.runtimeType=}"
                    on-change="onChangeRuntime"
                    datasource="{{runtimes}}"
                >
                </ui-radio-select>
                <div class="bui-form-item-help">
                    <span s-if="!isARM && formData.runtimeType === 'docker' && runtimes.length > 1">
                        Docker 将在 Kubernetes 1.24版本废弃，推荐选择 Containerd。
                    </span>
                    <a href="${tips.doc.runtimeType}" target="_blank">
                        如何选择 Kubernetes 集群的容器运行时
                    </a>
                </div>
            </ui-form-item>

            <ui-form-item inline label="VPC 网络：" name="vpcId">
                <div s-if="vpc.status === 'loading'">
                    <s-loading loading />
                </div>
                <div s-if="vpc.status === 'error'" class="cce-error">数据异常，请刷新重试</div>
                <div s-if="vpc.status === 'done'">
                    <ui-select
                        s-if="vpc.list.length"
                        width="240"
                        value="{=formData.vpcId=}"
                        datasource="{{vpc.list}}"
                    />
                    <div s-else class="cluster-create-form-item-empty">暂无数据</div>
                    <div s-if="isDefaultVpc" class="cce-tip-grey">
                        您的默认 VPC 为0.0.0.0网段，与所有地址段冲突，烦请修改或者使用自定义
                        VPC，谢谢。
                    </div>
                    <div s-if="defaultPodConflict" class="cce-error">
                        边缘集群的 pod 网段为默认为10.0.0.0/8网段冲突，请选择其他VPC
                    </div>
                    <div s-if="defaultBecConflict" class="cce-error">
                        与虚机内部**********/16网段冲突，请选择其他VPC
                    </div>
                </div>
            </ui-form-item>

            <ui-form-item inline label="Master BLB子网：" s-if="!isServerless">
                <cce-cluster-create-subnet
                    name="blbSubnetId"
                    value="{=formData.blbSubnetId=}"
                    id="{{formData.blbSubnetId}}"
                    datasource="{{blb.list}}"
                    loading="{{blb}}"
                    selected-item="{{blbSubnetIdItem}}"
                    message="Kubernetes API Server将使用该子网创建负载均衡器，不能选择NAT子网，该子网配置仅对自定义Master生效"
                />
            </ui-form-item>

            <ui-form-item inline label="Pod子网：" s-if="isServerless" name="podSubnets" required>
                <subnet-table
                    s-ref="subnetTable"
                    vpcId="{{formData.vpcId}}"
                    selectedVpcItem="{{selectedVpcItem}}"
                    isServerless="{{isServerless}}"
                    IPversion="{{formData.IPversion}}"
                    on-selected-change="onPodSubnetsSelected"
                />
                <div class="bui-form-item-help">
                    <span
                        >可选择多个可用区子网，创建的Pod将分散在所选子网中，并占用子网IP地址。</span
                    >
                    <span
                        >请选择IP充足、无冲突的子网。若无合适子网，请<a
                            href="javascript:void(0)"
                            on-click="onCreateSubnet"
                            >创建子网</a
                        ></span
                    >
                </div>
            </ui-form-item>

            <ui-form-item
                inline
                required
                label="服务网络模式："
                name="enableServiceController"
                s-if="isServerless"
            >
                <ui-radio-select
                    datasource="{{controller.datasource}}"
                    value="{=formData.enableServiceController=}"
                    on-change="onServiceChange"
                />
                <div class="bui-form-item-help">
                    <span>选择不同的服务网络模式，会产生不同的费用，请根据实际需求进行选择</span>
                    <a
                        href="${tips.doc.serviceController}"
                        target="_blank"
                        class="enable-service-controller-link"
                        >服务网络模式说明</a
                    >
                </div>
            </ui-form-item>

            <ui-form-item
                inline
                label="LB Service子网："
                s-if="(!isServerless || (isServerless && formData.enableServiceController)) && !isCloudEdge"
            >
                <cce-cluster-create-subnet
                    name="lbSubnetId"
                    value="{=formData.lbSubnetId=}"
                    id="{{formData.lbSubnetId}}"
                    datasource="{{lb.list}}"
                    loading="{{lb}}"
                    selected-item="{{lbSubnetIdItem}}"
                    message="Service及Ingress将默认使用该子网创建负载均衡器，不能选择NAT子网"
                />
            </ui-form-item>

            <cce-cluster-create-cni
                s-if="isServerless"
                enableServiceController="{{formData.enableServiceController}}"
                s-ref="cni"
            />

            <ui-form-item
                inline
                label="API Server公网访问："
                name="exposedPublic"
                s-if="!$flag.CceClusterNoEip"
            >
                <ui-switch
                    checked="{=formData.exposedPublic=}"
                    on-change="onExposedPublicChange"
                    disabled="{{isCloudEdge}}"
                />
                <div class="eip-msg">
                    开启后将为集群API Server绑定公网IP，否则无法通过外网访问API Server。
                    <span class="eip-warning-tip">自定义Master集群创建后不支持修改</span>
                </div>
            </ui-form-item>

            <ui-form-item
                inline
                label="NodeName："
                name="enableHostname"
                s-if="!isServerless"
                help="{{nodeNameHelp}}"
            >
                <ui-radio-select datasource="{{nodeName}}" value="{=formData.enableHostname=}" />
            </ui-form-item>

            <advanced-config-container open="{=advancedOpen=}">
                <tag-config s-ref="tagConfig" />
                <ui-form-item inline label="集群删除保护" name="forbidDelete">
                    <label slot="label" title="集群删除保护">集群删除保护：</label>
                    <ui-switch
                        checked="{=formData.forbidDelete=}"
                        on-change="onForbidDeleteChange"
                    />
                    <ui-tip layer-width="300" message="防止通过控制台或API误删除集群" />
                </ui-form-item>
                <ui-form-item
                    inline
                    label="认证方式："
                    name="authenticateMode"
                    s-if="!isServerless && enableOIDC"
                >
                    <label slot="label" title="认证方式：">认证方式：</label>
                    <ui-check-box
                        title="开启OIDC认证"
                        checked="{=formData.authenticateModeValue=}"
                    />
                    <div class="cce-tip-grey">
                        <span>默认为常用的X509客户端证书认证，勾选后将支持OIDC认证</span
                        >&nbsp;&nbsp;
                        <a href="${tips.doc.oidc}" target="_blank">集群认证说明&gt;&gt;</a>
                    </div>
                </ui-form-item>

                <ui-form-item
                    inline
                    label="自定义证书SAN："
                    name="apiServerCertSAN"
                    s-if="!isServerless"
                >
                    <ui-text-box
                        placeholder="请输入IP或域名"
                        width="240"
                        value="{=formData.apiServerCertSAN=}"
                    />
                    <div class="cce-tip-grey">
                        <span>多个IP或域名请用英文逗号分隔，暂不支持更新</span>
                        <span class="cce-tip-warn"></span>
                    </div>
                </ui-form-item>

                <ui-form-item inline label="备注信息：" name="description">
                    <ui-text-box
                        multiline
                        placeholder="请输入备注信息"
                        width="240"
                        height="80"
                        value="{=formData.description=}"
                    />
                </ui-form-item>
            </advanced-config-container>
        </ui-form>
    </ui-biz-legend>
</template>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp('@cce-cluster-create-subnet', '@cce-cluster-create-cni', '@pay-config', '@tag-config')
class Info extends Component {
    static template = template;

    static components = {
        'advanced-config-container': AdvancedConfigContainer,
        'subnet-table': SubnetTable,
        's-loading': Loading,
    };

    static computed = {
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },
        isARM() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.ARM;
        },
        nodeNameHelp() {
            const enableHostname = this.data.get('formData.enableHostname');
            return enableHostname
                ? '集群将以实例的主机名称（hostname）作为节点名称（NodeName），须保证加入集群的实例的主机名称不重复。节点加入集群后，不支持变更NodeName'
                : '集群将以实例的内网IP作为节点名称（NodeName）。节点加入集群后，不支持变更NodeName';
        },

        // 小于 1.20.8
        lessThan120() {
            return compareVersion('1.20.8', this.data.get('formData.k8sVersion'));
        },
        // 小于 1.24.4
        lessThan124() {
            return compareVersion('1.24.4', this.data.get('formData.k8sVersion'));
        },

        // 小于 1.26.9
        lessThan126() {
            return compareVersion('1.26.9', this.data.get('formData.k8sVersion'));
        },

        runtimes() {
            const lessThan124 = this.data.get('lessThan124');
            const runtimes = $flag.CceCreateRuntimeContainerd
                ? lessThan124
                    ? [docker, containerd_1_6_28]
                    : [containerd_1_6_28]
                : [docker];

            return runtimes;
        },

        enableIPv6() {
            const lessThan120 = this.data.get('lessThan120');

            return !lessThan120;
        },
    };

    initData() {
        const runtime = $flag.CceCreateRuntimeContainerd ? containerd_1_6_28 : docker;

        return {
            title: '基础配置',
            formData: {
                runtimeType: runtime.value,
                runtimeVersion: runtime.version,
                k8sVersion: '',
                IPversion: false,
                exposedPublic: !$flag.CceClusterNoEip,
                enableHostname: false,
                forbidDelete: true,
            },
            formErrors: null,
            rules,
            kubernetes: {
                status: 'loading',
                list: [],
            },
            vpc: {
                status: 'loading',
                list: [],
            },
            blb: {
                status: 'loading',
                list: [],
            },
            lb: {
                status: 'loading',
                list: [],
            },
            advancedOpen: false,
            nodeName: NodeName.toArray(),
            zone: {
                list: loadingDatasource,
            },
            subnet: {
                list: loadingDatasource,
            },
            layerStyle: {
                width: '150px',
            },
            ipv6TipMessage: '当前Kubernetes版本不支持开启IPv6双栈',
            controller: {
                datasource: [
                    {
                        text: 'kube-proxy',
                        value: false,
                        tip: '在pod中注入kube-proxy，通过iptables模式转发流量到后端pod。用户需要为kube-proxy占用资源进行付费。',
                    },
                    {
                        text: 'BLB',
                        value: true,
                        tip: '通过负载均衡BLB把内网或公网的访问流量转发到后端pod。用户需要遵循BLB的计费规则和配额限制。',
                    },
                ],
            },
            defaultPodConflict: false,
            defaultBecConflict: false,
            $flag,
        };
    }

    inited() {
        this.loadKubernetes();
    }

    attached() {
        const {isServerless, isCloudEdge, isARM} = this.data.get();
        if (isServerless) {
            this.data.set('formData.enableServiceController', false);
            this.nextTick(() => store.dispatch('queryBlbPrice', 'custom'));
        }

        store.dispatch('setStore', '1', 2);

        if (isCloudEdge) {
            this.data.set('nodeName', NodeName.toArray('IPTABLES'));
            this.data.set('formData.enableHostname', true);
        }

        if (isServerless || isCloudEdge || isARM) {
            this.data.set('formData.runtimeType', docker.value);
            this.data.set('formData.runtimeVersion', docker.version);
        }

        this.loadVpcList();
        this.checkEnableOIDC();

        this.watch('formData.k8sVersion', value => {
            if (value) {
                const lessThan120 = this.data.get('lessThan120');

                if (lessThan120) {
                    this.data.set('formData.IPversion', false);
                    this.onIpVersionChange({value: false});
                }
            }
        });

        this.watch('formData.vpcId', value => {
            let selectedVpcItem = null;
            const ipv6 = this.data.get('formData.IPversion');
            if (value) {
                if (!isServerless) {
                    this.data.set('formData.blbSubnetId', '');
                }

                this.data.set('formData.lbSubnetId', '');
                this.data.set('lbSubnetIdItem', null);
                this.data.set('lb', {status: 'done', list: []});
                this.data.set('selectedItems', null);
                this.data.set('formErrors.podSubnets', null);

                this.loadSubnetList(value, ipv6);

                selectedVpcItem = _.find(this.data.get('vpc.list'), item => item.value === value);

                const isCloudEdge = this.data.get('isCloudEdge');
                if (isCloudEdge) {
                    const defaultBecConflict = checkCidrConflict(selectedVpcItem.cidr, BecVmCIDR);
                    const defaultPodConflict = checkCidrConflict(
                        selectedVpcItem.cidr,
                        CloudClusterCIDR,
                    );
                    this.data.set('defaultPodConflict', defaultPodConflict);
                    this.data.set('defaultBecConflict', defaultBecConflict);
                }
            }
            // 选择VPC后要清空master worker配置
            store.dispatch('masterNodeListSet', null);
            store.dispatch('workerNodeListSet', null);
            store.dispatch('groupNodeListSet', null);
            store.dispatch('changeSelectedVpcItem', selectedVpcItem);
            store.dispatch('changeSelectedVpcId', value);
        });

        const formData = this.data.get('formData');
        store.dispatch('changeIpVersion', formData.IPversion);
        store.dispatch('changeExposedPublic', formData.exposedPublic);
        store.dispatch('changeForbidDelete', formData.forbidDelete);
        store.dispatch('setRuntimeType', formData.runtimeType);
        store.dispatch('setRuntimeVersion', formData.runtimeVersion);
    }

    onPayConfigChange(data) {
        store.dispatch('setPayConfig', data);
        const masterType = this.data.get('masterType');
        if (masterType) {
            store.dispatch('queryBlbPrice', masterType);
            store.dispatch('queryBccPrice');
        }
    }

    checkEnableOIDC() {
        return this.$http
            .checkWhiteList('EnableOIDC')
            .then(response => this.data.set('enableOIDC', _.get(response, '.isExist', false)));
    }

    onChangeRuntime(e) {
        this.data.set('formData.runtimeVersion', e.version);
        store.dispatch('setRuntimeType', e.value);
        store.dispatch('setRuntimeVersion', e.version);
    }

    loadKubernetes() {
        const {isCloudEdge, isServerless, isARM} = this.data.get();
        // 增加 1.26 版本
        let versionList = ['1.30.1', '1.28.8', '1.26.9', '1.24.4'];
        let defaultVersion = '1.28.8';

        if (isARM) {
            versionList = ['1.20.8'];
            defaultVersion = versionList[0];
        } else if (isServerless) {
            versionList = ['1.16.8'];
            defaultVersion = versionList[0];
        } else if (isCloudEdge) {
            versionList = ['1.18.9'];
            defaultVersion = versionList[0];
        }

        this.data.set('formData.k8sVersion', defaultVersion);
        this.data.set('kubernetes', {
            status: 'done',
            list: versionList.map(v => ({text: v, value: v})),
        });

        store.dispatch('changeK8sVersion', defaultVersion);
    }

    loadVpcList(ipv6) {
        this.data.set('vpc', {status: 'loading'});

        return this.$http
            .getVpcList({})
            .then(data => {
                let result = [];
                let exist = false;
                _.each(data.result || [], item => {
                    if (!ipv6 || (ipv6 && item.ipv6Cidr)) {
                        let text =
                            getVpcName(item.name) +
                            (item.cidr ? '（' + item.cidr + '）' : '') +
                            (ipv6 && item.ipv6Cidr ? '（' + item.ipv6Cidr + '）' : '');
                        result.push({
                            value: item.shortId,
                            text: text,
                            title: text,
                            defaultVpc: item.defaultVpc,
                            cidr: item.cidr,
                            shortId: item.shortId,
                            ipv6Cidr: item.ipv6Cidr,
                            auxiliaryCidr: item.auxiliaryCidr,
                            vpcId: item.vpcId,
                        });
                        if (this.data.get('formData.vpcId') === item.shortId) {
                            exist = true;
                        }
                    }
                });

                store.dispatch('setVpcList', result);

                this.data.set('vpc', {status: 'done', list: result});

                if (result.length > 0) {
                    if (!exist) {
                        this.data.set('formData.vpcId', result[0].value);
                    }
                    this.data.set(
                        'isDefaultVpc',
                        result[0].defaultVpc && result[0].cidr.split('/')[0] === '0.0.0.0',
                    );
                } else {
                    this.data.set('formData.vpcId', '');
                    this.data.set('isDefaultVpc', false);

                    this.data.set('blb', {status: 'done', list: []});
                    this.data.set('lb', {status: 'done', list: []});
                }

                return result;
            })
            .catch(error => this.data.set('vpc', {status: 'error'}));
    }

    loadSubnetList(vpcId, ipv6) {
        if (this.data.get('isServerless')) {
            return;
        }
        vpcId = vpcId || this.data.get('formData.vpcId');

        this.data.set('blb', {status: 'loading'});
        this.data.set('lb', {status: 'loading'});

        const subnetTypes = [1];
        const payload = {vpcId, subnetTypes};

        return this.$http
            .getSubnetList(payload)
            .then(data => {
                let result = [];
                let existBlb = false;
                let existLb = false;
                _.each(data.result || [], item => {
                    if (!ipv6 || (ipv6 && item.ipv6Cidr)) {
                        let text =
                            getSubnetName(item.name) +
                            (item.cidr ? '（' + item.cidr + '）' : '') +
                            (ipv6 && item.ipv6Cidr ? '（' + item.ipv6Cidr + '）' : '');
                        const data = {
                            value: item.shortId,
                            text: text,
                            title: text,
                            cidr: item.cidr,
                            subnetType: item.subnetType,
                            ipv6Cidr: item.ipv6Cidr,
                        };
                        result.push(data);
                        if (this.data.get('formData.blbSubnetId') === item.shortId) {
                            existBlb = true;
                            this.data.set('blbSubnetIdItem', data);
                        }
                        if (this.data.get('formData.lbSubnetId') === item.shortId) {
                            existLb = true;
                            this.data.set('lbSubnetIdItem', data);
                        }
                    }
                });

                if (result.length > 0) {
                    if (!existBlb) {
                        this.data.set('formData.blbSubnetId', result[0].value);
                        this.data.set('blbSubnetIdItem', result[0]);
                    }
                    if (!existLb) {
                        this.data.set('formData.lbSubnetId', result[0].value);
                        this.data.set('lbSubnetIdItem', result[0]);
                    }
                } else {
                    this.data.set('formData.blbSubnetId', '');
                    this.data.set('blbSubnetIdItem', null);

                    this.data.set('formData.lbSubnetId', '');
                    this.data.set('lbSubnetIdItem', null);
                }

                store.dispatch('setSubNetList', result);
                this.data.set('blb', {status: 'done', list: result});
                this.data.set('lb', {status: 'done', list: result});

                return result;
            })
            .catch(data => {
                this.data.set('blb', {status: 'error'});
                this.data.set('lb', {status: 'error'});
            });
    }

    onVersionChange(e) {
        const k8sVersion = e.value;

        this.data.set('formData.k8sVersion', k8sVersion);

        if ($flag.CceCreateRuntimeContainerd) {
            const containerd = containerd_1_6_28;
            this.data.set('formData.runtimeType', containerd.value);
            this.onChangeRuntime(containerd);
        }

        store.dispatch('changeK8sVersion', k8sVersion);
    }

    onIpVersionChange(e) {
        store.dispatch('changeIpVersion', e.value);
        this.loadVpcList(e.value).then(() => this.loadSubnetList(null, e.value));
    }

    onExposedPublicChange(e) {
        store.dispatch('changeExposedPublic', e.value);
        this.nextTick(() => {
            store.dispatch('queryBccPrice');
            store.dispatch(
                'queryBlbPrice',
                this.data.get('isServerless') ? 'custom' : this.data.get('masterType'),
            );
        });
    }

    onForbidDeleteChange(e) {
        store.dispatch('changeForbidDelete', e.value);
    }

    validateForm() {
        const defaultPodConflict = this.data.get('defaultPodConflict');
        const defaultBecConflict = this.data.get('defaultBecConflict');
        if (defaultPodConflict || defaultBecConflict) {
            return Promise.reject();
        }

        const form = this.ref('form');
        const cni = this.ref('cni');

        let tasks = [form.validateForm()];
        if (cni) {
            tasks.push(cni.validateForm());
        }

        return Promise.all(tasks).then(() => {
            this.data.set('formErrors.podSubnets', null);
            const subnetTable = this.ref('subnetTable');

            if (subnetTable) {
                const subnets = subnetTable.getFormData();
                if (subnets.vkSubnets.length === 0) {
                    this.data.set('formErrors.podSubnets', '至少选择一个Pod子网');
                    return Promise.reject();
                }

                return Promise.resolve();
            }
        });
    }

    getFormData() {
        const formData = this.data.get('formData');

        formData.authenticateMode = formData.authenticateModeValue ? 'oidc' : 'x509';

        const cni = this.ref('cni');
        const subnetTable = this.ref('subnetTable');
        const payConfig = this.ref('payConfig');
        const tagConfig = this.ref('tagConfig');
        const {payType, duration, autoRepay, repayCycle, repayCycleType} =
            payConfig?.getFormData() || {};
        const chargingType = payType === 'prepay' ? 'Prepaid' : 'Postpaid';

        let ResourceChargingOption = {
            chargingType,
        };

        if (payType === 'prepay') {
            ResourceChargingOption = {
                purchaseTime: duration,
                purchaseTimeUnit: 'month',
                autoRenew: autoRepay,
                ...ResourceChargingOption,
            };
            if (autoRepay) {
                ResourceChargingOption.autoRenewTime = repayCycle;
                ResourceChargingOption.autoRenewTimeUnit = repayCycleType || 'month';
            }
        }

        return _.extend(
            {},
            formData,
            cni ? cni.getFormData() : {},
            subnetTable ? subnetTable.getFormData() : {},
            {ResourceChargingOption},
            tagConfig ? {tags: tagConfig.getFormData()} : {},
        );
    }

    onCreateSubnet() {
        const selectedVpcItem = this.data.get('selectedVpcItem');
        if (!selectedVpcItem) {
            return;
        }

        const subnetTable = this.ref('subnetTable');
        if (!subnetTable) {
            return;
        }

        subnetTable.onCreateSubnet();
    }

    onPodSubnetsSelected(selectedItems) {
        if (this.data.get('selectedItems')) {
            if (selectedItems.length === 0) {
                this.data.set('formErrors.podSubnets', '至少选择一个Pod子网');
            } else {
                this.data.set('formErrors.podSubnets', null);
            }
        } else {
            this.data.set('selectedItems', true);
        }
        let subnets = [];
        let existLb = false;
        _.each(selectedItems, item => {
            if (_.indexOf([1], item.subnetType) > -1) {
                let text = getSubnetName(item.name) + (item.cidr ? '（' + item.cidr + '）' : '');
                const data = {
                    value: item.shortId,
                    text: text,
                    title: text,
                    cidr: item.cidr,
                    subnetType: item.subnetType,
                    ipv6Cidr: item.ipv6Cidr,
                };
                subnets.push(data);
                if (this.data.get('formData.lbSubnetId') === data.shortId) {
                    existLb = true;
                    this.data.set('lbSubnetIdItem', data);
                }
            }
        });
        if (!existLb && subnets.length > 0) {
            this.data.set('formData.lbSubnetId', subnets[0].value);
            this.data.set('lbSubnetIdItem', subnets[0]);
        } else {
            this.data.set('formData.lbSubnetId', '');
            this.data.set('lbSubnetIdItem', null);
        }
        this.data.set('lb', {status: 'done', list: subnets});
    }

    onServiceChange({value}) {
        this.fire('service-change', value);
    }
}

@asComponent('@cce-cluster-create-info')
export default class InfoStore extends connect.san(StoreMap)(Info) {}
