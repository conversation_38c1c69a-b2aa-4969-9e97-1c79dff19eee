/**
 * @file components/cluster/create-v2/instance-bec/transfer.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html} from '@baiducloud/runtime';
import {
    Table,
    TextBox,
    SearchBox,
    Select,
    bizXPager,
    Loading,
    StopScroll,
    Button,
} from '@baiducloud/bce-ui/san';
import {connect} from 'san-store';
import PasswordEditor from '../instance/password-editor';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
// import {getMultiPublicIpText} from '../../../../utils/bec-flavor';
import {BecExistNodeType, BmType} from '../enums';
import CommonTransfer from '../instance/transfer';
import {PopConfirm} from '@baidu/sui-biz';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<template>
    <div class="cluster-create-node-transfer">
        <div class="all-instance-list">
            <div class="operation-wrap">
                <ui-search-box
                    value="{=searchbox.keyword=}"
                    keyword-type="{=searchbox.keywordType=}"
                    datasource="{{keywordTypes}}"
                    on-search="onSearch"
                    width="200"
                />
                <input type="text" style="display: none" />
            </div>
            <ui-table
                s-ref="existTable"
                select="multi"
                schema="{{table.schema}}"
                cell-builder="{{table.cellRender}}"
                datasource="{{table.datasource}}"
                selected-index="{=table.selectedIndex=}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-selected-change="onTableSelected($event)"
                on-row-enter="onTableRowEnter"
                on-row-leave="onTableRowLeave"
                on-filter="onFilter"
            >
                <div slot="loading">
                    <ui-loading size="middle" />
                </div>
                <div class="bui-table-error" slot="error">
                    啊呀，出错了？<a href="javascript:void(0)" on-click="getBccNodeList"
                        >重新加载</a
                    >
                </div>
            </ui-table>

            <ui-pager
                s-if="pager.count > 0"
                pager="{=pager=}"
                with-pager-size="{{withPagerSize}}"
                on-pager-size-change="onPagerSizeChange($event)"
                on-pager-change="onPagerChange($event)"
            />
        </div>
        <ui-stop-scroll class="selected-instance-list">
            <div class="operation-wrap">
                已选择
                <span class="count">{{selected.datasource.length}}</span>
                个资源
                <ui-button skin="stringfy" on-click="onClearAll" class="clear-all">清空</ui-button>
            </div>
            <div class="scale-info" s-if="scaleLimitMessage">{{scaleLimitMessage}}</div>
            <ui-table
                s-ref="selectedTable"
                schema="{{selected.schema}}"
                datasource="{{selected.datasource}}"
                on-command="onSelectedTableCommand"
            >
                <div slot="c-vmId">{{row.vmId || row.bmId || '-'}}</div>
                <div slot="c-operation">
                    <a href="javascript:void(0)" data-command="DELETE">删除</a>
                    &nbsp;&nbsp;
                    <password-editor
                        s-if="!isRebuild && isNewBec"
                        value="{{row.password}}"
                        getPopupContainer="{{getPopupContainer}}"
                        on-confirm="onPasswordConfirm(rowIndex, $event)"
                    >
                        <a href="javascript:void(0)">
                            {{row.password ? '重新输入密码' : '输入密码'}}
                        </a>
                    </password-editor>
                </div>
            </ui-table>
        </ui-stop-scroll>
    </div>
</template> `;

@invokeBceSanUI
class Transfer extends CommonTransfer {
    static template = template;

    static components = {
        'ui-table': Table,
        'ui-text-box': TextBox,
        'ui-select': Select,
        'ui-pager': bizXPager,
        'ui-loading': Loading,
        'ui-stop-scroll': StopScroll,
        'ui-button': Button,
        'ui-search-box': SearchBox,
        's-popconfirm': PopConfirm,
        'password-editor': PasswordEditor,
    };

    static computed = {
        scaleLimitMessage() {
            const masterFlavor = this.data.get('masterFlavor');
            const existedNodeNum = this.data.get('existedNodeNum');
            if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                const num = masterFlavorNum - existedNodeNum;
                return `当前集群规模节点数量上限为${masterFlavorNum}，已有${existedNodeNum}个节点，当前最多可添加${
                    num < 0 ? 0 : num
                }个节点`;
            }
        },
        keywordTypes() {
            const instanceNodeType = this.data.get('instanceNodeType');
            if (instanceNodeType === BecExistNodeType.BEC_EBC) {
                return [
                    {
                        text: '实例名称',
                        value: 'name',
                    },
                    {
                        text: '实例ID',
                        value: 'bmId',
                    },
                ];
            }
            return [
                {
                    text: '实例名称',
                    value: 'instanceName',
                },
                {
                    text: '实例ID',
                    value: 'instanceId',
                },
            ];
        },
    };

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: 'instanceName',
            },
            table: {
                schema: [
                    {
                        name: 'vmId',
                        label: '实例ID',
                    },
                    {
                        name: 'vmName',
                        label: '实例名称',
                    },
                    {
                        name: 'configuration',
                        label: '实例配置',
                    },
                    {
                        name: 'region',
                        label: '所在区域',
                        // filter: {
                        //     options: [
                        //         {
                        //             text: '所有',
                        //             value: '',
                        //         },
                        //         ...AllRegionName.toArray(),
                        //     ],
                        //     value: '',
                        // },
                    },
                    // {
                    //     name: 'serviceId',
                    //     label: '实例组',
                    // },
                    // {
                    //     name: 'bandwidth',
                    //     label: '公网IP/带宽',
                    // },
                    {
                        name: 'internalIp',
                        label: '内网IP',
                    },
                ],
                selectedIndex: [],
                datasource: [],
                cellRender(item, key) {
                    switch (key) {
                        case 'vmId':
                            return `${item.vmId || item.bmId || '-'}`;
                        case 'vmName':
                            return `${item.vmName || item.name || '-'}`;
                        case 'configuration':
                            return `${item.cpu}核/${item.mem || item.memory || '-'}GB`;
                        // return getResourceConfig(
                        //     item,
                        //     'cpu',
                        //     'mem',
                        //     'rootDiskSize',
                        //     'dataStorage',
                        //     'gpu',
                        // );
                        case 'region':
                            if (!item) {
                                return;
                            }
                            const {regionName, cityName, spName} = item;
                            return `${regionName}-${cityName}-${spName}`;
                        // const provider = [
                        //     {
                        //         city: item.city,
                        //         region: item.region,
                        //         replicas: 1,
                        //         serviceProvider: item.serviceProvider,
                        //     },
                        // ];
                        // return item && getRegionInfo(provider);
                        // case 'bandwidth':
                        //     return getMultiPublicIpText(item, 'bandwidth');
                        case 'internalIp':
                            if (!item) {
                                return '-';
                            }
                            if (item.internalIp) {
                                return item.internalIp;
                            }
                            if (item.bmType === BmType.BBC1) {
                                return item.internalIp || '-';
                            }
                            const iplist = item.iplist || [];
                            const intranetip = iplist.filter(
                                item => item.isp === 'intranet' || item.isp === 'intra',
                            );
                            return intranetip.length > 0 ? intranetip[0].ip : '-';
                        default:
                            break;
                    }

                    return _.escape(item[key]);
                },
            },
            selected: {
                schema: [
                    {
                        name: 'vmId',
                        label: '实例ID',
                    },
                    {
                        name: 'operation',
                        label: '操作',
                    },
                ],
                datasource: [],
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100},
                ],
            },
            withPagerSize: true,
            getPopupContainer: () => {
                return this.ref('selectedTable').el;
            },
        };
    }

    attached() {
        const {instanceList, clusterHA, nodeType} = this.data.get();
        this.getBccNodeList();

        if (!instanceList || (instanceList && instanceList.length === 0)) {
            this.dispatch('instanceList_no_selected', {
                disable: true,
                message: clusterHA
                    ? `请选择${clusterHA}个${nodeType}节点`
                    : `请选择${nodeType}节点`,
            });
        }

        this.watch('selected.datasource', data => {
            const existedNodeNum = this.data.get('existedNodeNum');
            const masterFlavor = this.data.get('masterFlavor');
            if ((existedNodeNum || existedNodeNum === 0) && masterFlavor) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                if (data?.length > masterFlavorNum - existedNodeNum) {
                    this.dispatch('form_validate', {
                        disable: true,
                        message: '参数校验失败',
                    });
                } else {
                    this.dispatch('form_validate', {
                        disable: false,
                    });
                }
            } else {
                this.dispatch('form_validate', {
                    disable: false,
                });
            }
        });

        this.watch('instanceNodeType', () => {
            this.data.set('selected.datasource', []);
            this.getBccNodeList();
        });

        this.watch('keywordTypes', value => {
            const {keywordType} = this.data.get('searchbox');
            if (value && value.length && !value.find(item => item.value === keywordType)) {
                const detaulItem = value[0];
                this.data.set('searchbox.keywordType', detaulItem?.value);
                this.data.set('searchbox.placeholder', `请输入${detaulItem?.text || '-'}进行搜索`);
            } else {
                this.data.set('searchbox.keywordType', '');
                this.data.set('searchbox.placeholder', '');
            }
            this.data.set('searchbox.keyword', '');
        });
    }

    getBccNodeList = _.debounce((payload = {pageNo: 1}) => {
        const vpcId = this.data.get('vpcId');
        const isNewBec = this.data.get('isNewBec');
        this.data.set('table.selectedIndex', []);
        this.data.set('table.loading', true);
        this.dispatch('instanceList_loading', {
            disable: true,
            message: '数据加载中',
        });
        const {searchbox, instanceNodeType} = this.data.get();
        const isBecBm = instanceNodeType === BecExistNodeType.BEC_EBC;
        const pageSize = this.data.get('pager.size');
        const {keywordType, keyword} = searchbox;
        const options = _.extend(
            {
                keywordType,
                keyword,
                orderBy: keywordType,
                order: 'asc',
                pageSize,
            },
            payload,
        );
        if (isNewBec) {
            options.vpcId = vpcId;
        }

        return this.$http[isBecBm ? 'getBecBmNodeList' : 'getBecInstanceList'](options)
            .then(({result}) => {
                if (!result) {
                    this.data.set('table.datasource', []);
                    return;
                }

                let data = _.get(result, '.result') || [];

                const selectedIds = _.pluck(
                    this.data.get('selected.datasource'),
                    isBecBm ? 'bmId' : 'vmId',
                );
                // Worker之前选中的节点
                const workers = this.data.get('workerSelectedInstanceIds') || [];
                _.each(data, item => {
                    item.tipWidth = 100;
                    if (!item.bandwidth && !isNewBec) {
                        item.xui__disabled = true; // eslint-disable-line
                        item.tip = '该实例未绑定公网IP';
                    }
                    if (item.cceCluster) {
                        item.xui__disabled = true; // eslint-disable-line
                        item.tip = '该实例已加入集群';
                    }

                    if (
                        _.find(_.difference(workers, selectedIds), isBecBm ? item.bmId : item.vmId)
                    ) {
                        item.xui__disabled = true; // eslint-disable-line
                        item.tip = '该实例已添加到Worker配置';
                    }

                    if (
                        isNewBec &&
                        !item.xui__disabled &&
                        ((isBecBm && !['ubutnu20.04', 'ubuntu22.04'].includes(item.imageOs)) ||
                            (!isBecBm &&
                                !['BaiduLinux', 'Ubuntu'].includes(item?.osImage?.osName)) ||
                            (item?.osImage?.osName === 'BaiduLinux' &&
                                item?.osImage?.osVersion !== '3.0') ||
                            (item?.osImage?.osName === 'Ubuntu' &&
                                !['20.04', '22.04'].includes(item?.osImage?.osVersion)))
                    ) {
                        item.xui__disabled = true;
                        item.tip = '服务器操作系统类型不支持';
                    }
                });

                this.data.set('table.datasource', data);

                this.data.set('pager.count', _.get(result, '.totalCount', 0));
                this.data.set('pager.page', _.get(result, '.pageNo', 1));

                // 编辑状态 已经选中的节点
                const list = this.data.get('selected.datasource');
                if (list && list.length > 0) {
                    /* eslint-disable */
                    let selectedIndex = [];
                    _.each(list, item => {
                        const findIndex = _.findIndex(data, d =>
                            isBecBm ? d.bmId === item.bmId : d.vmId === item.vmId,
                        );
                        if (findIndex > -1) {
                            selectedIndex.push(findIndex + '');
                        }
                    });
                    /* eslint-enable */
                    this.data.set('table.selectedIndex', selectedIndex);
                }
            })
            .catch(e => {
                this.data.set('table.datasource', []);
                this.data.set('table.error', true);
            })
            .finally(() => {
                this.data.set('table.loading', false);
                this.dispatch('instanceList_loading', {disable: false});
            });
    }, 300);

    onFilter(e) {
        this.getBccNodeList({pageNo: 1, region: e.region});
    }

    onPasswordConfirm(rowIndex, value) {
        this.data.set(`selected.datasource[${rowIndex}].password`, value);

        const allFill = this.data.get('selected.datasource').every(item => !!item.password);
        this.fire('password-change', allFill);
    }

    getFormData() {
        const {instanceNodeType} = this.data.get();
        const isBecBm = instanceNodeType === BecExistNodeType.BEC_EBC;
        const workers = this.data.get('workerSelectedInstanceIds') || [];
        return {
            instanceList: _.filter(
                this.data.get('selected.datasource'),
                d => !_.find(workers, v => (isBecBm ? v === d.bmId : v === d.vmId)),
            ),
        };
    }
}

@asComponent('@cce-cluster-create-transfer-bec')
export default class TransferStore extends connect.san(StoreMap)(Transfer) {}
