/**
 * 密码输入框
 * @file password-editor.js
 */
import {defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Input, Popover} from '@baidu/sui';

export default defineComponent({
    template: html`
        <div class="password-editor">
            <s-popover
                visible="{=visible=}"
                trigger="click"
                getPopupContainer="{{getPopupContainer}}"
                placement="bottomRight"
                class="password-editor-popup"
            >
                <slot></slot>
                <template slot="content">
                    <s-input
                        type="password"
                        value="{=value=}"
                        showPasswordIcon
                        autocomplete="new-password"
                    ></s-input>
                    <div class="password-editor-buttons">
                        <s-button skin="primary" on-click="onConfirm">确认</s-button>
                        <s-button on-click="onCancel">取消</s-button>
                    </div>
                </template>
            </s-popover>
        </div>
    `,
    components: {
        's-popover': Popover,
        's-input': Input,
        's-button': <PERSON><PERSON>,
    },

    initData() {
        return {
            visible: false,
            value: '',
        };
    },

    onConfirm() {
        const value = this.data.get('value');

        this.fire('confirm', value);
        this.data.set('visible', false);
    },

    onCancel() {
        this.data.set('visible', false);
    },
});
