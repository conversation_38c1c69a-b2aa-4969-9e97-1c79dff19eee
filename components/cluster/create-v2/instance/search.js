/**
 * SUI搜索框
 *
 * @file components/cluster/create-v2/instance/search.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Input, Tag, Button} from '@baidu/sui';

const {asComponent} = decorators;

const template = html`<div class="cce-cluster-transfer-search">
    <label>实例内网IP：</label>
    <s-input
        value="{=keyword=}"
        placeholder="{{placeholder}}"
        on-blur="onKeywordAdd"
        on-enter="onKeywordAdd"
        width="400"
    >
        <div slot="prefix" class="form-item-tag-wrapper">
            <s-tag
                class="form-item-tag"
                s-for="item, index in keywords"
                closable
                on-click="onKeywordDelete(index)"
                >{{item}}</s-tag
            >
        </div>
    </s-input>
    <div class="search-button">
        <s-button skin="primary" on-click="onSearch($event)">搜索</s-button>
        <s-button on-click="onClear($event)">清空</s-button>
    </div>
</div>`;

@asComponent('@cce-search-box')
export default class SearchBox extends Component {
    static template = template;

    static components = {
        's-input': Input,
        's-tag': Tag,
        's-button': Button,
    };

    initData() {
        return {
            keywords: [],
            placeholder: '请输入实例内网IP进行查询；多个IP请用回车、空格、换行分隔',
        };
    }

    onKeywordAdd(e) {
        if (e.value) {
            const valueByComma = e.value.split(',');
            const valueBySpace = _.filter(e.value.split(' '), item => item);

            if (valueByComma.length > 1) {
                valueByComma.map(item => this.data.push('keywords', item));
            } else if (valueBySpace.length > 1) {
                valueBySpace.map(item => this.data.push('keywords', item));
            } else {
                this.data.push('keywords', e.value);
            }
            this.data.set('keyword', '');
        }
    }

    onKeywordDelete(index) {
        this.data.removeAt('keywords', index);
    }

    onSearch(e) {
        this.fire('search', {value: this.data.get('keywords')});
    }

    onClear() {
        this.data.set('keywords', []);
        this.data.set('keyword', '');

        this.fire('search', {value: this.data.get('keywords')});
    }
}
