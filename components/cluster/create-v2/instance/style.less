.cluster-create-node-transfer {
    display: flex;
    justify-content: space-between;
    width: 1120px;

    .scale-info {
        margin-top: -15px;
        color: #84868c;
    }

    .all-instance-list {
        width: 700px;

        .bui-biz-pager {
            margin-top: 10px;
        }

        .warning-tip {
            fill: #f38900;
            margin-left: 4px;
        }
    }

    .selected-instance-list {
        margin-left: 20px;
        width: 400px;
    }

    .selected-instance-list-wrap {
        max-height: 400px;
        overflow: hidden auto;
    }

    .operation-wrap {
        margin-bottom: 20px;
        min-height: 30px;
        line-height: 30px;

        .count {
            color: #f33e3e;
        }

        .clear-all.bui-button {
            padding: 0;
            display: inline;
            line-height: unset;
            float: right;
        }
    }

    .network-advanced-toggle {
        float: right;
        margin-right: 20px;
        position: relative;

        label,
        .bui-icon {
            cursor: pointer;
        }

        .bui-icon {
            margin-left: 5px;
        }
    }

    .network-advanced-toggle.network-advanced-toggle-close {
        position: relative;
        top: 0;
        right: 20px;
    }

    .bui-table {
        max-height: 650px;
        min-height: 200px;
        overflow-y: scroll;

        .bui-table-cell:first-child {
            .bui-table-cell-text {
                padding-left: 10px;

                .bui-tip-template {
                    display: block;
                }

                .bui-table-multi-select {
                    margin-left: 5px;
                    vertical-align: middle;

                    &:disabled {
                        cursor: not-allowed;
                    }
                }
            }
        }

        .bui-table-hcell:first-child {
            .bui-table-hcell-text {
                padding-left: 15px;
            }
        }
    }

    .instance-operations {
        display: flex;
        align-items: center;

        .password-editor {
            margin-left: 4px;
            position: relative;
        }
    }

    .password-editor-popup {
        .password-editor-buttons {
            margin-top: 12px;
            height: 35px;
            .s-button {
                float: right;
                margin-left: 10px;
            }
        }
    }
}

.cce-cluster-transfer-search {
    margin-top: 10px;

    .s-input.s-input-prefix-container {
        display: inline-block;
        zoom: 1;
        width: 480px;
        vertical-align: top;
    }

    .s-tag {
        margin: 0 5px;
    }

    .search-button,
    .form-item-tag-wrapper {
        display: inline-block;
        zoom: 1;
    }
}
