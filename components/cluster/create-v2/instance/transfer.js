/**
 * @file components/cluster/create-v2/instance/transfer.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {
    Table,
    TextBox,
    SearchBox,
    Select,
    bizXPager,
    Loading,
    StopScroll,
    Tip,
    Button,
    nextZindex,
    Icon,
} from '@baiducloud/bce-ui/san';
import {Input, Tooltip} from '@baidu/sui';
import {OutlinedExclamationCircle} from '@baidu/sui-icon';
import {PopConfirm} from '@baidu/sui-biz';
import {connect} from 'san-store';

import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {StorageType, DiskType} from '@baidu/bce-bcc-sdk-enum';
import {CODE as Code} from '../../../../utils/constants';
import Advanced from '../advanced';
import {NodeType, ExistNodeType, ExistHPASNodeType} from '../enums';
import Search from './search';
import PasswordEditor from './password-editor';

const {asComponent, invokeBceSanUI} = decorators;

const BCC_SELECTED_SCHEMA = [
    {
        name: 'instanceId',
        label: '实例ID',
    },
    // {
    //     name: 'cds',
    //     label: '数据磁盘',
    //     width: 150,
    // },
    {
        name: 'operation',
        label: '操作',
        width: 200,
    },
];

const BBC_SELECTED__SCHEMA = [
    {
        name: 'instanceId',
        label: '实例ID',
    },
    {
        name: 'operation',
        label: '操作',
    },
];
const template = html`<template>
    <div class="cluster-create-node-transfer">
        <div class="all-instance-list">
            <div class="operation-wrap">
                <ui-search-box
                    s-if="!advanced"
                    value="{=searchbox.keyword=}"
                    keyword-type="{=searchbox.keywordType=}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    on-search="onSearch"
                    width="200"
                    autocomplete="new-password"
                />
                <cce-advanced open="{=advanced=}" s-if="instanceNodeType !== 'HPAS'">
                    <div slot="icon">
                        <label>批量搜索</label>
                        <ui-icon name="downarrow" />
                    </div>
                    <cce-search s-if="advanced" on-search="onBatchSearch" />
                </cce-advanced>
                <input type="text" style="display: none" />
            </div>
            <ui-table
                s-ref="existTable"
                select="multi"
                schema="{{table.schema}}"
                cell-builder="{{table.cellRender}}"
                datasource="{{table.datasource}}"
                selected-index="{=table.selectedIndex=}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-selected-change="onTableSelected($event)"
                on-row-enter="onTableRowEnter"
                on-row-leave="onTableRowLeave"
            >
                <div slot="loading">
                    <ui-loading size="middle" />
                </div>
                <div slot="c-instanceId">
                    {{row.instanceId}}
                    <s-tooltip
                        s-if="isEni && isEbc && row.eniQuota === 0"
                        trigger="hover"
                        placement="top"
                    >
                        <div slot="content">
                            该实例不支持绑定弹性网卡，VPC-ENI模式下容器IP地址为分配的主网卡辅助IP
                        </div>
                        <s-outlined-exclamation-circle class="warning-tip" />
                    </s-tooltip>
                </div>
                <div class="bui-table-error" slot="error">
                    啊呀，出错了？
                    <a href="javascript:void(0)" on-click="getBccNodeList"> 重新加载 </a>
                </div>
            </ui-table>

            <ui-pager
                s-if="pager.count > 0"
                pager="{=pager=}"
                with-pager-size="{{withPagerSize}}"
                on-pager-size-change="onPagerSizeChange($event)"
                on-pager-change="onPagerChange($event)"
            />
        </div>
        <div class="selected-instance-list">
            <ui-stop-scroll class="selected-instance-list-wrap">
                <div class="operation-wrap">
                    已选择
                    <span class="count">{{selected.datasource.length}}</span>
                    个资源
                    <ui-button skin="stringfy" on-click="onClearAll" class="clear-all">
                        清空
                    </ui-button>
                </div>
                <div class="scale-info" s-if="scaleLimitMessage">{{scaleLimitMessage}}</div>
                <ui-table
                    s-ref="selectedTable"
                    schema="{{selected.schema}}"
                    cell-builder="{{selected.cellRender}}"
                    datasource="{{selected.datasource}}"
                    on-command="onSelectedTableCommand"
                >
                    <div slot="h-cds">
                        数据磁盘
                        <ui-tip
                            message="数据盘用于部署kubernetes组件并存放日志，建议您选择磁盘空间充足的CDS云磁盘，若不选择将默认使用系统盘。"
                            layer-width="200"
                        >
                        </ui-tip>
                    </div>
                    <div slot="c-instanceId">{{row.instanceId}}</div>
                    <!--
                    <div slot="c-cds">
                        <ui-loading s-if="row.loading" size="small" />
                        <ui-select
                            s-else
                            datasource="{{row.cdsList}}"
                            value="{=row.volumeId=}"
                            on-change="onCdsChange($event, rowIndex)"
                        />
                    </div>
                    -->
                    <div slot="c-operation" class="instance-operations">
                        <a href="javascript:void(0)" data-command="DELETE">删除</a>
                        <password-editor
                            s-if="!isRebuild"
                            value="{{row.password}}"
                            getPopupContainer="{{getPopupContainer}}"
                            on-confirm="onPasswordConfirm(rowIndex, $event)"
                        >
                            <a href="javascript:void(0)">
                                {{row.password ? '重新输入密码' : '输入密码'}}
                            </a>
                        </password-editor>
                    </div>
                </ui-table>
            </ui-stop-scroll>
        </div>
    </div>
</template> `;

@invokeBceSanUI
class Transfer extends Component {
    static template = template;

    static components = {
        'ui-table': Table,
        'ui-text-box': TextBox,
        'ui-select': Select,
        'ui-pager': bizXPager,
        'ui-loading': Loading,
        'ui-stop-scroll': StopScroll,
        'ui-tip': Tip,
        'ui-button': Button,
        'ui-search-box': SearchBox,
        'ui-icon': Icon,
        'cce-advanced': Advanced,
        'cce-search': Search,
        's-input': Input,
        's-popconfirm': PopConfirm,
        'password-editor': PasswordEditor,
        's-tooltip': Tooltip,
        's-outlined-exclamation-circle': OutlinedExclamationCircle,
    };

    static computed = {
        scaleLimitMessage() {
            const masterFlavor = this.data.get('masterFlavor');
            const existedNodeNum = this.data.get('existedNodeNum');
            if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                const num = masterFlavorNum - existedNodeNum;
                return `当前集群规模节点数量上限为${masterFlavorNum}，已有${existedNodeNum}个节点，当前最多可添加${
                    num < 0 ? 0 : num
                }个节点`;
            }
        },
    };

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: 'name',
                keywordTypes: [
                    {
                        text: '实例名称',
                        value: 'name',
                    },
                    {
                        text: '实例ID',
                        value: 'instanceId',
                    },
                    {
                        text: '内网IP',
                        value: 'internalIp',
                    },
                ],
            },
            table: {
                schema: [
                    {
                        name: 'instanceId',
                        label: '实例ID',
                        width: 110,
                    },
                    {
                        name: 'name',
                        label: '实例名称',
                        width: 150,
                    },
                    {
                        name: 'configuration',
                        label: '实例配置',
                        width: 180,
                    },
                    {
                        name: 'logicalZone',
                        label: '可用区',
                        width: 80,
                    },
                    {
                        name: 'internalIp',
                        label: '内网IP',
                        width: 120,
                    },
                ],
                selectedIndex: [],
                datasource: [],
                cellRender: (item, key) => {
                    const instanceNodeType = this.data.get('instanceNodeType');
                    switch (key) {
                        case 'logicalZone':
                            return window.$zone.getLabel(item.logicalZone);
                        case 'configuration':
                            if (instanceNodeType === ExistHPASNodeType.HPAS) {
                                return `${item.appType || '-'}/${item.appPerformanceLevel || '-'}`;
                            }
                            return `${item.cpu}核/${item.memory}GB/${item.sysDisk}GB`;
                        case 'internalIp':
                            if (instanceNodeType === ExistHPASNodeType.HPAS) {
                                return item.internalIp || '-';
                            }
                            return `${item.internalIp}${item.ipv6 ? `<br>${item.ipv6}` : ''}`;
                        default:
                            break;
                    }

                    return _.escape(item[key]) || '-';
                },
            },
            selected: {
                schema: BCC_SELECTED_SCHEMA,
                datasource: [],
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
                datasource: [
                    {text: '10', value: 10},
                    {text: '20', value: 20},
                    {text: '50', value: 50},
                    {text: '100', value: 100},
                ],
            },
            withPagerSize: true,
            advanced: false,
            getPopupContainer: () => {
                return this.ref('selectedTable').el;
            },
            specAvailableStatusMap: null,
        };
    }

    inited() {
        const {instanceList, instanceNodeType, isEni} = this.data.get();
        if (instanceList && instanceList.length > 0) {
            this.data.set('selected.datasource', _.cloneDeep(instanceList));
        }
        if (instanceNodeType === NodeType.BBC) {
            this.data.set('selected.schema', BBC_SELECTED__SCHEMA);
        }
    }

    async attached() {
        await Promise.all([this.getSpecAvailableStatus()]);
        const nodeTypeChangeHandler = () => {
            const type = this.data.get('instanceNodeType');
            let schema = BCC_SELECTED_SCHEMA;
            if (type === NodeType.BBC) {
                schema = BBC_SELECTED__SCHEMA;
            }
            this.data.set('selected.schema', schema);

            this.data.set('selected.datasource', []);
            if (type) {
                this.getBccNodeList();
            }
        };
        nodeTypeChangeHandler();
        this.watch('instanceNodeType', () => {
            nodeTypeChangeHandler();
        });

        this.watch('advanced', value => {
            if (!value) {
                this.data.set('filters', []);
            } else {
                this.data.set('searchbox.keywordType', 'name');
                this.data.set('searchbox.keyword', '');
            }
        });

        this.watch('selected.datasource', data => {
            const existedNodeNum = this.data.get('existedNodeNum');
            const masterFlavor = this.data.get('masterFlavor');
            this.fire('change', data);
            if ((existedNodeNum || existedNodeNum === 0) && masterFlavor) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                if (data?.length > masterFlavorNum - existedNodeNum) {
                    this.dispatch('form_validate', {
                        disable: true,
                        message: '参数校验失败,所选节点数超出集群规模',
                    });
                } else {
                    this.dispatch('form_validate', {
                        disable: false,
                    });
                }
            } else {
                this.dispatch('form_validate', {
                    disable: false,
                });
            }
        });
    }

    async getSpecAvailableStatus() {
        try {
            const data = await this.$http.getSpecAvailableStatus();
            if (data?.machineSpecList) {
                const specAvailableStatusMap = {};
                data?.machineSpecList?.forEach(item => {
                    specAvailableStatusMap[item.machineSpec] = item.status;
                });
                this.data.set('specAvailableStatusMap', specAvailableStatusMap);
            }
        } catch (error) {
            this.data.set('specAvailableStatusMap', null);
        }
    }

    onKeywordTypeChange(e) {
        const keywordTypes = this.data.get('searchbox.keywordTypes');
        const selectedItem = _.find(keywordTypes, item => item.value === e.value);
        this.data.set('searchbox.placeholder', `请输入${selectedItem.text}进行搜索`);
    }

    getBccNodeList = _.debounce((payload = {pageNo: 1}) => {
        this.data.set('table.selectedIndex', []);
        const {instanceNodeType} = this.data.get();
        // const isBbc = instanceNodeType === NodeType.BBC;

        this.data.set('table.loading', true);
        this.data.set('table.error', false);
        this.dispatch('instance_list_loading', {
            disable: true,
            message: '数据加载中',
        });
        const {searchbox} = this.data.get();
        const pageSize = this.data.get('pager.size');
        const {keywordType, keyword} = searchbox;
        const selectedVpcItem = this.data.get('selectedVpcItem');
        const options = _.extend(
            {
                keywordType,
                keyword,
                orderBy: keywordType,
                order: 'asc',
                pageSize,
                vpcId: selectedVpcItem ? selectedVpcItem.value : '',
                machineType: instanceNodeType?.toLowerCase(),
            },
            payload,
        );
        const {isEni, eniVPCSubnetIDs} = this.data.get();
        // 创建 vpc-cni网络模式 增加eniSubnetIDs参数
        if (isEni && eniVPCSubnetIDs) {
            options.eniSubnetIDs = [];
            _.map(eniVPCSubnetIDs, s => _.each(s, d => options.eniSubnetIDs.push(d)));
        }
        if (instanceNodeType === ExistHPASNodeType.HPAS) {
            return this.getHpasNodeList(options);
        }

        return this.$http
            .getBccNodeListV2(options)
            .then(({result}) => {
                const currentInstanceNodeType = this.data.get('instanceNodeType');
                if (currentInstanceNodeType !== instanceNodeType) {
                    return;
                }
                this.fire('showEbcTip');
                if (!result) {
                    this.data.set('table.datasource', []);
                    return;
                }

                const flavors = this.data.get('flavors');

                let bccList = _.get(result, '.bccList') || [];

                const IPversion = this.data.get('IPversion');
                const masters = this.data.get('masterSelectedInstanceIds') || [];
                const workers = this.data.get('workerSelectedInstanceIds') || [];
                const selectedIds = _.pluck(this.data.get('selected.datasource'), 'instanceId');
                const {isEbc, isEni, specAvailableStatusMap} = this.data.get();
                let data = [];

                _.each(bccList, item => {
                    let d = item.server || {};
                    d.cardCount = item.cardCount;
                    d.spec = item.spec;
                    d.xui__disabled = !item.available; // eslint-disable-line
                    d.tipWidth = 100;
                    d.eniQuota = item.eniQuota || 0;

                    if (item.available) {
                        // 上一步Master配置已经选择
                        // 编辑状态 去掉当前选择的节点
                        /* eslint-disable */
                        _.each(_.difference(masters, selectedIds), id => {
                            if (id === d.instanceId) {
                                d.xui__disabled = true;
                                d.tip = '该实例已添加到Master配置';
                            }
                        });
                        /* eslint-enable */

                        // Worker之前选中的节点
                        // 编辑状态 去掉当前选择的节点
                        /* eslint-disable */
                        _.each(_.difference(workers, selectedIds), id => {
                            if (id === d.instanceId) {
                                d.xui__disabled = true;
                                d.tip = '该实例已添加到Worker配置';
                            }
                        });
                        /* eslint-enable */
                    } else if (item.info && Code[item.info]) {
                        d.tip = Code[item.info];
                    }

                    // SAME_WITH_ENI_SUBNET 目前放开子网限制，后端去除后，可以删除这个判断
                    if (d.xui__disabled && item.info === 'SAME_WITH_ENI_SUBNET') {
                        d.xui__disabled = false;
                    }

                    if (d.bbcFlavorId) {
                        let bbcFlovor = null;
                        /* eslint-disable */
                        _.each(flavors, (v, k) => {
                            const flavor = _.find(v, value => value.id === d.bbcFlavorId);
                            if (flavor) {
                                bbcFlovor = flavor;
                            }
                        });
                        /* eslint-enable */
                        if (bbcFlovor) {
                            d.cpu = bbcFlovor.cpu;
                            d.memory = bbcFlovor.memory;
                        }
                    }

                    if (!this.data.get('enableBidding') && d.createdFrom === 'bidding') {
                        d.xui__disabled = true;
                        d.tip = '不支持抢占实例';
                    }

                    if (
                        specAvailableStatusMap &&
                        [ExistNodeType.BCC, ExistNodeType.EBC].includes(currentInstanceNodeType) &&
                        !d.xui__disabled
                    ) {
                        const targetStatus = specAvailableStatusMap[d.spec];
                        if (targetStatus === 'Adapted') {
                            // 通过
                        } else if (targetStatus === 'NotSupported') {
                            // 不支持
                            d['xui__disabled'] = true;
                            d.tip = '该实例规格CCE不支持，请选择其他实例规格';
                        } else if (targetStatus === 'Unadapted' || !targetStatus) {
                            // 未适配
                            d['xui__disabled'] = true;
                            d.tip = `该实例规格CCE暂未适配，若有需要请<a href="${
                                window.$context.getDomains().ticket
                            }/#/ticket/create~productId=110" target="_blank">提工单</a>联系`;
                        }
                    }

                    if (isEni) {
                        if (
                            currentInstanceNodeType === ExistNodeType.BCC &&
                            d.eniQuota === 0 &&
                            !d.xui__disabled
                        ) {
                            d.xui__disabled = true;
                            d.tip = '该实例不支持绑定弹性网卡，请选择其他实例';
                        }
                    }

                    if (!IPversion || (IPversion && d.ipv6)) {
                        data.push(d);
                    }
                });

                this.data.set('table.datasource', data);

                this.data.set('pager.count', _.get(result, '.totalCount', 0));
                this.data.set('pager.page', _.get(result, '.pageNo', 1));

                // 编辑状态 已经选中的节点
                const list = this.data.get('selected.datasource');
                if (list && list.length > 0) {
                    /* eslint-disable */
                    let selectedIndex = [];
                    _.each(list, item => {
                        const findIndex = _.findIndex(data, d => d.instanceId === item.instanceId);
                        if (findIndex > -1) {
                            selectedIndex.push(findIndex + '');
                        }
                    });
                    this.data.set('table.selectedIndex', selectedIndex);

                    /* eslint-enable */
                }
            })
            .catch(e => {
                const currentInstanceNodeType = this.data.get('instanceNodeType');
                if (currentInstanceNodeType !== instanceNodeType) {
                    return;
                }
                this.data.set('table.datasource', []);
                this.data.set('table.error', true);
            })
            .finally(() => {
                this.data.set('table.loading', false);
                this.dispatch('instance_list_loading', {disable: false});
            });
    }, 300);

    async getHpasNodeList(options = {}) {
        const {instanceNodeType, selectedVpcItem} = this.data.get();
        try {
            const params = {
                vpcId: selectedVpcItem ? selectedVpcItem.value : '',
                pageNo: options.pageNo,
                pageSize: options.pageSize,
                keywordType: options.keywordType,
                keyword: options.keyword,
            };
            const data = await this.$http.getHpasNodeList(params);
            const currentInstanceNodeType = this.data.get('instanceNodeType');
            if (currentInstanceNodeType !== instanceNodeType) {
                return;
            }
            const {isEni, eniVPCSubnetIDs} = this.data.get();
            const list = (data?.hpasPage?.HpasList || []).map(e => {
                let disabled = !e.available;
                let disabledTip = !e.available ? '该实例已在集群中或不在当前集群私有网络下' : '';
                if (!disabled && isEni && eniVPCSubnetIDs && Object.keys(eniVPCSubnetIDs).length) {
                    disabled = !eniVPCSubnetIDs[e.zoneName];
                    disabledTip = !eniVPCSubnetIDs[e.zoneName]
                        ? '该实例无可用容器子网，请添加与实例同可用区的容器子网'
                        : '';
                }
                return {
                    instanceId: e.hpasId,
                    logicalZone: e.zoneName,
                    xui__disabled: disabled,
                    tip: disabledTip,
                    ...e,
                };
            });
            this.data.set('table.datasource', list);
            this.data.set('pager.count', data?.hpasPage?.totalCount || 0);
            this.data.set('pager.page', data?.hpasPage?.pageNo || 1);

            // 编辑状态 已经选中的节点
            const datasource = this.data.get('selected.datasource');
            if (datasource?.length > 0) {
                let selectedIndex = [];
                _.each(datasource, item => {
                    const findIndex = _.findIndex(list, d => d.instanceId === item.instanceId);
                    if (findIndex > -1) {
                        selectedIndex.push(findIndex + '');
                    }
                });
                this.data.set('table.selectedIndex', selectedIndex);
            }
        } catch (error) {
            const currentInstanceNodeType = this.data.get('instanceNodeType');
            if (currentInstanceNodeType !== instanceNodeType) {
                return;
            }
            this.data.set('table.datasource', []);
            this.data.set('table.error', true);
        } finally {
            this.data.set('table.loading', false);
            this.dispatch('instance_list_loading', {disable: false});
        }
    }

    onClearAll(e) {
        this.data.set('selected.datasource', []);
        this.data.set('table.selectedIndex', []);
    }

    onSearch() {
        this.getBccNodeList();
    }

    onBatchSearch(e) {
        let filters = [];

        if (e.value && e.value.length > 0) {
            filters = [
                {
                    keywordType: 'internalIp',
                    keyword: e.value.join(','),
                },
            ];
        }

        this.data.set('filters', filters);
        this.getBccNodeList({filters, pageNo: 1});
    }

    onPagerSizeChange({value}) {
        this.data.set('pager.size', value);
        const payload = {filters: this.data.get('filters'), pageNo: 1};
        this.getBccNodeList(payload);
    }

    onPagerChange({pageNo}) {
        const payload = {filters: this.data.get('filters'), pageNo};
        this.getBccNodeList(payload);
    }

    getCdsByInstanceName(name, id) {
        return this.$http
            .bccCdsDiskList({
                keyword: name,
                keywordType: 'instanceName',
                filter: [{keyword: name, keywordType: 'instanceName'}],
                instanceId: id,
            })
            .then(({page}) =>
                _.map(
                    _.filter(page.result || [], ({type}) => type !== 'system'),
                    disk => _.pick(disk, ['storageType', 'diskSize', 'volumeId', 'type']),
                ),
            )
            .then(res => {
                const diskType = [{text: '暂不挂载', value: ''}];
                _.each(res, ({storageType, diskSize, volumeId, type}) => {
                    const alias = StorageType.getAliasFromValue(storageType);
                    const volumeType = DiskType.getValueFromAlias(alias);
                    // 过滤掉cce不支持的cds
                    if (volumeType) {
                        diskType.push({
                            text: `${DiskType.getTextFromAlias(alias)} ${diskSize}GB`,
                            value: volumeId,
                            size: diskSize,
                            storageType,
                            volumeId,
                            type,
                        });
                    }
                });
                return diskType;
            })
            .catch(e => {
                const diskType = [{text: '暂不挂载', value: ''}];
                return diskType;
            });
    }

    getNeedSelectedMasterCount() {
        const clusterHA = this.data.get('clusterHA') || 0;
        // 自定义节点中的master节点
        const selectedCustomMaster = this.data.get('masterCustomInstances');
        let count = 0;
        _.each(selectedCustomMaster, v => {
            if (v && v.value) {
                count += v.value.purchaseNum;
            }
        });
        // 已选择的已有节点
        const masterSelectedInstanceIds = this.data.get('masterSelectedInstanceIds') || [];
        const selectedIds = _.pluck(this.data.get('selected.datasource'), 'instanceId');
        const totalSelected = count + _.difference(masterSelectedInstanceIds, selectedIds).length;
        return clusterHA - totalSelected;
    }

    onTableSelected(e) {
        // 之前选中的
        const selectedIndex = this.data.get('table.selectedIndex');
        // 增加的
        const addItemsIndex = _.difference(e.selectedIndex, selectedIndex);
        // 删除的
        const deleteItemsIndex = _.difference(selectedIndex, e.selectedIndex);
        const datasource = this.data.get('table.datasource');
        _.each(deleteItemsIndex, index => {
            const item = datasource[+index];
            const findIndex = _.findIndex(
                this.data.get('selected.datasource'),
                data => data.id === item.id,
            );
            this.data.removeAt('selected.datasource', findIndex);
        });
        _.each(addItemsIndex, index => {
            const item = datasource[+index];
            this.data.push('selected.datasource', item);
            // const i = this.data.get('selected.datasource').length - 1;
            // this.data.set(`selected.datasource[${i}].loading`, true);
            // this.getCdsByInstanceName(item.name, item.id)
            //     .then(data => {
            //         // 还没请求完就可能被取消选中了
            //         const item = this.data.get(`selected.datasource[${i}]`);
            //         if (item) {
            //             this.data.set(`selected.datasource[${i}].cdsList`, data);
            //         }
            //     })
            //     .finally(() => {
            //         // 还没请求完就可能被取消选中了
            //         const item = this.data.get(`selected.datasource[${i}]`);
            //         if (item) {
            //             this.data.set(`selected.datasource[${i}].loading`, false);
            //         }
            //     });
        });
        const clusterHA = this.data.get('clusterHA') || 0;
        const nodeType = this.data.get('nodeType');
        const count = this.data.get('selected.datasource').length;
        const needSelected = this.getNeedSelectedMasterCount();
        const isMaster = nodeType === 'Master';
        if (
            (isMaster &&
                // Master的个数还要减去已选择的自定义节点
                clusterHA &&
                count > needSelected) ||
            count === 0
        ) {
            this.dispatch('instance_list_no_selected', {
                disable: true,
                message: isMaster
                    ? `请选择${needSelected}个${nodeType}节点`
                    : `请选择${nodeType}节点`,
            });
        } else {
            this.dispatch('instance_list_no_selected', {disable: false, message: ''});
        }
    }

    onCdsChange(e, rowIndex) {
        this.data.set(`selected.datasource[${rowIndex}].volumeId`, e.value || '');
    }

    onSelectedTableCommand(e) {
        const rowIndex = e.rowIndex - 1;
        const item = this.data.get(`selected.datasource[${rowIndex}]`);
        switch (e.type) {
            case 'DELETE': {
                const findIndex = _.findIndex(this.data.get('table.datasource'), data => {
                    if (data.vmId) {
                        return data.vmId === item.vmId;
                    }
                    return data.id === item.id;
                });
                this.data.remove('table.selectedIndex', findIndex + '');
                this.data.removeAt('selected.datasource', rowIndex);
                break;
            }
            default:
                break;
        }
    }

    onPasswordConfirm(rowIndex, value) {
        this.data.set(`selected.datasource[${rowIndex}].password`, value);

        const allFill = this.data.get('selected.datasource').every(item => !!item.password);
        this.fire('password-change', allFill);
    }

    getFormData() {
        const selectedDatasource = this.data.get('selected.datasource');
        const uniqueDatasource = selectedDatasource.reduce((acc, current) => {
            const x = acc.find(item => item.instanceId === current.instanceId);
            if (!x) {
                acc.push(current);
            }
            return acc;
        }, []);
        return {
            instanceList: uniqueDatasource,
        };
    }

    validateForm() {
        const selected = this.data.get('selected.datasource');
        if (selected.length > 0) {
            return Promise.resolve();
        }

        return Promise.reject();
    }

    // 查找每一行tr下的tip
    // 根据是否有showLayer hideLayer判断
    // 可能会不太准备
    findTip(children) {
        let tip = null;
        _.each(children, component => {
            if (component.showLayer && component.hideLayer) {
                tip = component;
            } else if (!tip && component.children) {
                tip = this.findTip(component.children);
            }
        });
        return tip;
    }

    findTbody(children) {
        let tbody = null;
        _.each(children, component => {
            if (component.tagName && component.tagName === 'tbody') {
                tbody = component;
            } else if (!tbody && component.children) {
                tbody = this.findTbody(component.children);
            }
        });
        return tbody;
    }

    findTr(children) {
        let trs = [];
        _.each(children, component => {
            if (component.tagName && component.tagName === 'tr') {
                trs.push(component);
            } else if (component.children) {
                trs = [...trs, ...this.findTr(component.children)];
            }
        });
        return trs;
    }

    findCurrentRowTip(rowIndex) {
        let tip = null;

        const table = this.ref('existTable');
        const tbody = this.findTbody(table.children);
        const trs = this.findTr(tbody.children);

        if (trs[rowIndex]) {
            tip = this.findTip(trs[rowIndex].children);
        }

        return tip;
    }

    onTableRowEnter(e) {
        const tip = this.findCurrentRowTip(e.rowIndex);
        tip && tip.showLayer();
    }

    onTableRowLeave(e) {
        const tip = this.findCurrentRowTip(e.rowIndex);
        tip && tip.hideLayer();
    }
}

@asComponent('@cce-cluster-create-transfer')
export default class TransferStore extends connect.san(StoreMap)(Transfer) {}
