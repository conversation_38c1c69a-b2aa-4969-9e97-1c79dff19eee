/**
 * @file components/cluster/create-v2/master/add-bcc.js
 * <AUTHOR>
 */
/* global $context */
import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, HttpClient} from '@baiducloud/runtime';
import {
    notification,
    Loading,
    RadioSelect,
    Form,
    FormItem,
    Select,
    NumberTextline,
    Table,
    Dragger,
    Tip,
    Suggestion,
    Button,
} from '@baiducloud/bce-ui/san';
import {Tip as BceTip} from '@baidu/sui-biz';
import {Slider, Tooltip} from '@baidu/sui';
import {AdvancedConfigPanel} from '@baidu/bce-bcc-sdk-san';
import {SubnetType, Arch} from '@baidu/bce-bcc-sdk-enum';
import {SubnetCreateDialog} from '@baidu/bce-vpc-sdk-san';
import {VpcSDK} from '@baidu/bce-vpc-sdk';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';
import {PayType, ClusterType, ProductType, PartitionType} from '../../../../utils/enums';
import {BbcFlavorType, BbcImageType, NetworkMode} from '../enums';
import {getSdk} from '../../../../utils/sdk';
import {getSubnetName, gibToGb, gbToGib} from '../../../../utils/helper';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import tips from '../../../../utils/tips';
import deploysetConfigPanel from './bbc-partial/deployset-config-panel';
import './add-bbc.less';
import LoginConfig from '../../../common/login-config';
import ProductTypeSelector from '@components/product-type';
import PayConfig from '../../../../pages/cluster/create-v2/components/pay-config';
import {ImageTypeMap} from '../../../../utils/cluster-helper';

function isSmartCard(flavor) {
    // 华东P客户 下面这俩套餐需要支持可购买安全组  这里临时开放一下限制
    // BBC-G4-PDD01 BBC-G4-PDD02
    const isClientPFlavor =
        flavor && (flavor.id === 'BBC-G4-PDD01' || flavor.id === 'BBC-G4-PDD02');
    return flavor ? flavor.id.charAt(flavor.id.length - 1) === 'S' || isClientPFlavor : false;
}

const Schema = AsyncValidator.default;

const getRules = self => {
    return new Schema({
        logicalZone: [{required: true, message: '请选择可用区'}],
        subnetId: [{required: true, message: '请选择子网'}],
    });
};

const {asComponent, invokeBceSanUI} = decorators;

const ImageType = BbcImageType;

const silent = {'x-silent': true}; // 无loading、无弹窗报错
const silence = {'X-silence': true}; // 无loading、有弹窗报错

const template = html`<div class="cce-cluster-master-add-bbc">
    <ui-form s-ref="form" form-data="{=formData=}" form-errors="{{formErrors}}" rules="{{rules}}">
        <!-- 付费地域 -->
        <ui-form-item
            inline
            label-width="{{100}}"
            label="付费方式："
            name="productType"
            s-if="showPayType"
            style="display: {{!hideCommonItem ? 'block' : 'none'}}"
        >
            <label slot="label" title="付费方式：">付费方式：</label>
            <product-type-selector
                s-if="$flag.CceClusterCreateBidding"
                value="{=formData.productType=}"
                on-change="onProductTypeChange"
                layout="prepay,postpay"
            ></product-type-selector>
        </ui-form-item>
        <pay-config
            s-ref="payConfig"
            initPayType="prepay"
            s-if="showPayType && formData.productType === 'prepay'"
            style="display: {{!hideCommonItem ? 'block' : 'none'}}"
            on-change="onPayConfigChange"
        />

        <ui-form-item inline label-width="{{100}}" label="可用区：" name="logicalZone">
            <ui-loading size="small" s-if="zone.loading" />
            <ui-radio-select
                s-else
                datasource="{{datasource.logicalZone}}"
                value="{=formData.logicalZone=}"
                on-change="onLogicalZoneChange"
            />
            <ui-tip
                s-if="{{datasource.logicalZone.length}}"
                class="com-warning-tip"
                skin="warning"
                message="{{tips.instance.zoneTip}}"
                layer-width="{{200}}"
                track-id="ti_bbc_instance_create_zone_tip"
                track-name="可用区提示"
            />
        </ui-form-item>

        <ui-form-item inline label-width="{{100}}" label="子网设备类型：">
            <ui-radio-select
                datasource="{{datasource.subnetTypes}}"
                value="{=subnetType=}"
                on-change="onSubnetTypeChange"
            />
        </ui-form-item>
        <ui-form-item inline label-width="{{100}}" label="子网：" name="subnetId">
            <ui-loading size="small" s-if="subnet.loading" />
            <ui-select
                s-else
                width="199"
                datasource="{{subnet.list}}"
                value="{=formData.subnetId=}"
                on-change="onSubnetChange"
            />
            <div s-if="subnet.list.length === 0" class="cce-cluster-create-subnet">
                <a href="javascript:void(0)" on-click="onCreateSubnet">创建子网</a>
            </div>
        </ui-form-item>

        <!-- 配置信息 -->
        <ui-form-item label="筛选：" label-width="{{labelWidth}}" inline class="filter-wrapper">
            <div class="content-item">
                <label>CPU核数:</label>
                <ui-select
                    on-change="onCpuChange"
                    width="100px"
                    value="{{filter.cpu}}"
                    datasource="{{datasource.cpusDatasource}}"
                />
            </div>
            <div class="content-item">
                <label>内存:</label>
                <ui-select
                    on-change="onMemoryChange"
                    width="100px"
                    value="{{filter.memory}}"
                    datasource="{{datasource.memorysDatasource}}"
                />
                <label class="memory-unit">GB</label>
            </div>
            <div class="content-item">
                <label>规格名称:</label>
                <div class="instance-search-box">
                    <ui-suggestion
                        placeholder="请输入实例规格如：G4"
                        value="{=searchbox.value=}"
                        datasource="{{searchbox.textDatasource}}"
                        layer-width="250px"
                        width="220px"
                        on-change="onInstanceSpecSelect"
                        hideNotMatchItem
                    />
                    <ui-button on-click="onInstanceSearch" icon="magnifier" />
                </div>
            </div>
        </ui-form-item>
        <ui-form-item label="架构：" label-width="{{labelWidth}}" inline s-if="{{withClassify}}">
            <ui-radio-select
                s-if="{{datasource.archList.length}}"
                datasource="{{datasource.archList}}"
                value="{=formData.arch=}"
                on-change="onArchChange"
            />
            <span s-else><ui-loading size="small" /> 加载中...</span>
        </ui-form-item>
        <ui-form-item label="类型：" label-width="{{labelWidth}}" inline s-if="{{withClassify}}">
            <ui-radio-select
                s-if="{{typeDatasource.length}}"
                datasource="{{typeDatasource}}"
                value="{=formData.type=}"
                on-change="onTypeChange"
            />
            <span s-else><ui-loading size="small" /> 加载中...</span>

            <span s-if="{{formData.type === 'storage'}}" class="text-red">
                <i>*</i>大数据机型不支持Raid
            </span>
        </ui-form-item>
        <ui-form-item label="配置：" label-width="{{labelWidth}}" inline>
            <ui-table
                width="{{990}}"
                s-if="{{!flags.flavorLoading}}"
                schema="{{flavorTable.schema}}"
                datasource="{{flavorTable.datasource}}"
                cell-builder="{{flavorTable.cellBuilder}}"
                on-selected-change="onFlavorChange"
                selected-index="{=flavorTable.selectedIndex=}"
                selected-items="{=flavorTable.selectedItems=}"
                select="single"
            >
                <div slot="h-cpu">
                    vCPU
                    <ui-tip layer-width="{{280}}" message="{{tips.instance.cpuConfigTip}}"></ui-tip>
                </div>
                <div slot="c-disk">
                    <div s-if="{{row.ssdInfo}}">SSD盘 {{row.ssdInfo}}</div>
                    <div s-if="{{row.sataInfo}}">SATA盘 {{row.sataInfo}}</div>
                    <div s-if="{{row.sasInfo}}">SAS盘 {{row.sasInfo}}</div>
                    <div s-if="{{row.hddInfo}}">HDD盘 {{row.hddInfo}}</div>
                </div>

                <div slot="c-cpuGhz">
                    <div>{{row.cpuGhz || '-'}} GHz</div>
                </div>

                <div slot="c-raids">
                    <s-tooltip s-if="{{row.raids.length > 1}}" content="{{row.raids|raidsText}}">
                        <div>{{row.raids[0]}}...</div>
                    </s-tooltip>
                    <div s-else>{{ row.raids.length === 1 ? row.raids[0] : '-'}}</div>
                </div>
            </ui-table>
            <span s-else><ui-loading size="small" /> 加载中...</span>
        </ui-form-item>

        <!-- 镜像 -->
        <ui-form-item label="镜像类型：" label-width="{{labelWidth}}" inline>
            <ui-radio-select
                s-if="{{datasource.imageType.length}}"
                datasource="{{datasource.imageType}}"
                value="{=formData.imageType=}"
                on-change="onImageTypeChange"
            />
            <span s-else>-</span>
            <ui-tip
                s-if="{{datasource.imageType.length}}"
                class="com-warning-tip"
                skin="warning"
                message="自定义镜像只能安装在相同机型上，其他机型暂不支持"
                layer-width="{{200}}"
            />
        </ui-form-item>
        <ui-form-item label="操作系统：" label-width="{{labelWidth}}" inline>
            <div s-if="{{!datasource.osNameMap}}">-</div>

            <span s-elif="noAvailableImage" class="bui-form-item-static"> 当前没有可用镜像 </span>

            <template s-else>
                <ui-select
                    datasource="{{datasource.osName}}"
                    value="{=formData.osName=}"
                    on-change="onOsNameChange"
                />
                <ui-select
                    datasource="{{datasource.imageId}}"
                    value="{=formData.imageId=}"
                    on-change="onImageIdChange"
                />

                <ui-tip
                    s-if="{{formData.imageType === 'bbcCommon'}}"
                    class="com-warning-tip"
                    skin="warning"
                    message="当前所展示的操作系统列表为该套餐所能适配的所有操作系统"
                    layer-width="{{200}}"
                />
                <div class="os-tip" s-if="{{formData.imageType === 'bbcCommon'}}">
                    若你需要挂载极速型L2并行文件存储（PFS），建议选择CentOS或者Ubuntu操作系统，<a
                        href="https://cloud.baidu.com/doc/PFS/s/olzz6s5bi"
                        target="_blank"
                        >详细说明</a
                    >。
                </div>
            </template>
        </ui-form-item>

        <!-- 存储 -->
        <ui-form-item label="Raid参数：" label-width="{{labelWidth}}" inline>
            <div s-if="{{formData.flavor}}">
                <ui-select datasource="{{datasource.raid}}" value="{=formData.raid=}" />
                <span>{{raidDesc}}</span>
                <a href="${tips.doc.raid}" target="_blank">
                    <i class="iconfont icon-link"></i>Raid参数介绍
                </a>
            </div>
            <div s-else class="bui-form-item-static">请先选择套餐</div>
        </ui-form-item>
        <ui-form-item label="系统盘" label-width="{{labelWidth}}" inline class="sys-disk-item">
            <span class="disk-info">
                容量： {{datasource.partitionSize.sysAndHomeSizeOnGB || '-'}}GB /
                {{datasource.partitionSize.sysAndHomeSize || '-'}}GiB
            </span>
            <a class="disk-info" href="${tips.doc.partition}" target="_blank">
                <i class="iconfont icon-link"></i>实例分区介绍
            </a>
        </ui-form-item>
        <ui-form-item
            label="文件系统："
            label-width="{{labelWidth}}"
            inline
            s-if="partitionResizeable"
            class="file-sys-item"
        >
            <ui-select datasource="{{partitionType}}" value="{=formData.rootPartitionType=}" />
        </ui-form-item>
        <ui-form-item
            label="系统盘分区："
            label-width="{{labelWidth}}"
            inline
            s-if="partitionResizeable"
        >
            <div class="system-partition">
                <div class="partition">
                    <span class="partition-classify">Root分区 &nbsp;&nbsp;/</span>

                    <ui-numbertext-line
                        min="{{config.sysDiskSize.min}}"
                        max="{{config.sysDiskSize.max}}"
                        class="com-ui-numbertextline"
                        value="{=formData.sysDiskSize=}"
                    />
                    <span>GB</span>

                    <span class="gib-info">{{datasource.partitionSize.root|gbToGib}}GiB</span>

                    <s-slider
                        class="ml20"
                        width="350"
                        min="{{config.sysDiskSize.min}}"
                        max="{{config.sysDiskSize.max}}"
                        mid="{{config.sysDiskSize.min|midValue(config.sysDiskSize.max)}}"
                        value="{=formData.sysDiskSize=}"
                        marks="{{sysDiskMarks}}"
                        on-change="onSysDiskSizeChange"
                    />
                </div>

                <div class="partition">
                    <span class="partition-classify">Swap分区 &nbsp;&nbsp;/swap</span>

                    <!--暂不支持修改-->
                    <ui-numbertext-line
                        min="{{datasource.partitionSize.swap}}"
                        max="{{datasource.partitionSize.swap}}"
                        class="com-ui-numbertextline"
                        value="{=datasource.partitionSize.swap=}"
                        disabled
                    />
                    <span>GB</span>
                    <span class="gib-info">{{datasource.partitionSize.swap|gbToGib}}GiB</span>
                </div>

                <div class="partition">
                    <span class="partition-classify">Home分区 &nbsp;&nbsp;/home</span>
                    <!--暂不支持修改-->
                    <ui-numbertext-line
                        min="{{datasource.partitionSize.home}}"
                        max="{{datasource.partitionSize.home}}"
                        class="com-ui-numbertextline"
                        value="{=datasource.partitionSize.home=}"
                        disabled
                    />
                    <span>GB</span>
                    <span class="gib-info">{{datasource.partitionSize.home|gbToGib}}GiB</span>
                </div>
            </div>
        </ui-form-item>
        <ui-form-item label="数据盘" label-width="{{labelWidth}}" inline class="data-disk-item">
            <span class="disk-info">
                容量：{{datasource.partitionSize.dataDiskSizeOnGB || '-'}}GB /
                {{datasource.partitionSize.dataDiskSize || '-'}}GiB
            </span>
            <span
                class="ml20 disk-info"
                s-if="{{isWindowsImage && datasource.partitionSize.dataDiskSizeOnGB}}"
            >
                ${tips.instance.dataDiskTip}
            </span>
        </ui-form-item>
        <ui-form-item
            label="文件系统："
            label-width="{{labelWidth}}"
            inline
            s-if="partitionResizeable"
            class="file-sys-item"
        >
            <ui-select datasource="{{partitionType}}" value="{=formData.dataPartitionType=}" />
        </ui-form-item>

        <login-config
            s-if="!isNodeGroupCreate"
            s-ref="systemConfigPanel"
            showNuma="{{isNumaWhiteList}}"
            isBbc="{{true}}"
            style="display: {{!hideCommonItem ? 'block' : 'none'}}"
        />

        <!-- 高级配置 -->
        <advanced-config-panel
            s-if="{{isUserDataWhiteList && !isNodeGroupCreate}}"
            s-ref="advancedConfigPanel"
            sdk="{{sdk.bcc}}"
            user-data="{=userData=}"
            disabled="{=!formData.supportUserData=}"
            style="display: {{!hideCommonItem ? 'block' : 'none'}}"
        />

        <ui-form-item
            s-if="!isNodeGroupCreate"
            label="{{isNodeGroupCreate ? '期望节点数：' : '台数：'}}"
            label-width="{{labelWidth}}"
            inline
            style="display: {{!hideCommonItem && !editData ? 'block' : 'none'}}"
        >
            <template slot="label">
                {{isNodeGroupCreate ? '期望节点数：' : '台数：'}}
                <s-tip
                    class="expected-node-tip"
                    s-if="isNodeGroupCreate"
                    placement="top"
                    content="当节点组内节点数不等于期望节点数时，弹性伸缩服务会自动进行扩缩容，确保节点组内始终保持该数量的实例数。"
                />
            </template>
            <ui-numbertext-line
                min="{{purchaseNumMin}}"
                max="{{config.purchaseNum.max}}"
                value="{=formData.purchaseNum=}"
                on-input="onPurchaseNumChange"
            />
            <p class="color-gray-light bbc-quota-created-tip mt10" s-if="!scaleLimitMessage">
                BBC实例已创建数量：{{quota.bbcQuotaCreated}}个 /
                可创建总数：{{quota.bbcQuotaTotal}}个。如需增加配额请到<a
                    href="http://ticket.bce.baidu.com"
                    >工单</a
                >申请。
            </p>
            <div class="scale-info mt10" s-if="scaleLimitMessage">{{scaleLimitMessage}}</div>
            <p class="node-group-create-tip" s-if="isNodeGroupCreate">
                设置期望节点数后，节点组会自动扩容或缩容，将节点数目维持在指定的期望节点数量，无须人工干预。
            </p>
        </ui-form-item>

        <deployset-config-panel
            s-if="!isNodeGroupCreate"
            style="display: {{!hideCommonItem ? 'block':'none'}}"
            s-ref="deploysetConfigPanel"
            logical-zone="{{formData.logicalZone}}"
            quota="{{quota}}"
        />
    </ui-form>
</div> `;

@invokeBceSanUI
class AddBbcCustom extends Component {
    static components = {
        'advanced-config-panel': AdvancedConfigPanel,
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-radio-select': RadioSelect,
        'ui-select': Select,
        'ui-loading': Loading,
        'ui-numbertext-line': NumberTextline,
        'ui-table': Table,
        'ui-tip': Tip,
        'ui-dragger': Dragger,
        'deployset-config-panel': deploysetConfigPanel,
        'ui-suggestion': Suggestion,
        'ui-button': Button,
        's-slider': Slider,
        's-tooltip': Tooltip,
        'login-config': LoginConfig,
        'pay-config': PayConfig,
        's-tip': BceTip,
        'product-type-selector': ProductTypeSelector,
    };

    static template = template;

    static computed = {
        quotaTip() {
            const bbcQuotaCreated = this.data.get('quota.bbcQuotaCreated') || '';
            const bbcQuotaTotal = this.data.get('quota.bbcQuotaTotal') || '';
            const ticketLink = '<a href="' + $context.getDomains().ticket + '">' + '工单' + '</a>';
            return `BBC实例已创建数量：${bbcQuotaCreated}个 / 可创建总数：${bbcQuotaTotal}个。
                如需增加配额请到${ticketLink}申请。`;
        },
        isSmartCard() {
            const flavor = this.data.get('formData.flavor');
            return isSmartCard(flavor);
        },
        typeDatasource() {
            const arch = this.data.get('formData.arch');
            const types = this.data.get('datasource.type');
            return types ? types.filter(el => el.arch === arch) : [];
        },
        withClassify() {
            const filter = this.data.get('filter');
            const filterByKeyword = this.data.get('filterByKeyword');
            // cpu、内存、筛选的时候，不展示分类
            return !(filter.cpu || filter.memory || filterByKeyword);
        },
        partitionType() {
            const xfsWhiteTag = this.data.get('bbcEnableXfs');
            return PartitionType.toArray(..._.compact(['EXT4', xfsWhiteTag && 'XFS']));
        },
        sysDiskMarks() {
            let {min = 20, max = 100} = this.data.get('config.sysDiskSize');
            const mid = _.round((min + max) / 2);
            return {
                [min]: `${min}GB`,
                [mid]: `${mid}GB`,
                [max]: `${max}GB`,
            };
        },
        supportCds() {
            const flavor = this.data.get('formData.flavor');
            return flavor && (flavor.id === 'BBC-Bf2-01S' || +flavor.gen === 3);
        },
        partitionResizeable() {
            return !this.data.get('supportCds') && !this.data.get('isWindowsImage');
        },
        isWindowsImage() {
            const osName = this.data.get('formData.osName');
            const osList = this.data.get('datasource.osName');
            const selectedItem = _.find(osList, item => item.value === osName);
            return selectedItem && selectedItem.osType === 'windows';
        },
        scaleLimitMessage() {
            const masterFlavor = this.data.get('masterFlavor');
            const existedNodeNum = this.data.get('existedNodeNum');
            if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                const num = masterFlavorNum - existedNodeNum;
                return `当前集群规模节点数量上限为${masterFlavorNum}，已有${existedNodeNum}个节点，当前最多可添加${
                    num < 0 ? 0 : num
                }个节点`;
            }
        },
        purchaseNumMin() {
            const purchaseNumMax = this.data.get('config.purchaseNum.max');
            const initPurchaseNumMin = this.data.get('initPurchaseNumMin');
            return purchaseNumMax > 1 ? initPurchaseNumMin ?? 1 : 0;
        },
    };

    static filters = {
        midValue(min, max) {
            return Math.floor((min + max) / 2);
        },
        gbToGib(value) {
            return gbToGib(value, 1);
        },
        productTypeContent(value) {
            return PayType.getTextFromValue(value);
        },
        raidsText(raids) {
            return raids.join('、');
        },
    };

    initData() {
        return {
            tips: tips,
            filter: {},
            searchbox: {
                value: '',
                textDatasource: [],
                placeholder: '请输入实例规格如：G4',
            },
            datasource: {
                cpusDatasource: [],
                memorysDatasource: [],
                archList: [],
                subnetTypes: [
                    // BBC机器分为一代机器、二代机器（根据实例套餐可区分：不带S的是一代，带S的是二代及以后的）
                    // * 一代机器只能用BBC专属型子网
                    // * 二代机器只能使用通用型子网
                    // 通过该方法判断  isSmartCard   true代表2代机
                    {text: '通用型', value: 'common'},
                    {text: 'BBC专属型', value: 'bbc'},
                ],
            },
            subnetType: 'common',
            labelWidth: 100,
            formData: {
                productType: PayType.POSTPAY,
                purchaseNum: 1,
            },
            config: {
                purchaseNum: {},
                sysDiskSize: {
                    min: 20,
                    max: 100,
                },
            },
            flavorTable: {
                schema: [
                    {name: 'id', label: '套餐'},
                    {name: 'cpu', label: 'vCPU', width: 100},
                    {name: 'memory', label: '内存', width: 100},
                    {name: 'cpuGhz', label: '处理器主频', width: 100},
                    {name: 'disk', label: '本地存储'},
                    {name: 'raids', label: 'RAID'},
                    {name: 'networkCard', label: '网络'},
                ],
                cellBuilder: (row, key) => row[key] || '-',
                datasource: [],
                selectedIndex: [],
                selectedItems: [],
            },
            flags: {
                flavorLoading: true,
            },
            sdk: {
                bcc: getSdk('BCC'),
            },
            quota: {},
            isUserDataWhiteList: false,
            userData: '',
            isNumaWhiteList: false,
            is480White: false,
            subnet: {
                loading: true,
            },
            rules: getRules(this),
        };
    }

    inited() {
        this.$childs = [];

        // 初始化付费类型
        const payConfig = this.data.get('payConfig');
        this.data.set('formData.productType', payConfig?.payType || ProductType.POSTPAY);

        const networkMode = this.data.get('networkMode');
        const isVpcHybird = networkMode === NetworkMode.VPC_HYBRID;
        this.data.set('isVpcHybird', isVpcHybird);

        // 编辑回显
        const editData = this.data.get('editData') || this.data.get('fillData');
        if (editData) {
            const {productType, logicalZone, vpcId, bbcOption} = editData;

            // 付费类型
            if (productType) {
                this.data.set(
                    'formData.productType',
                    productType === ProductType.BIDDING ? ProductType.POSTPAY : productType,
                );
            }
            // 可用区
            if (logicalZone) {
                this.data.set('formData.logicalZone', logicalZone);
            }
            // vpc
            vpcId && this.data.set('vpcId', vpcId);

            // subnetType
            if (bbcOption?.flavor) {
                const subnetType = isSmartCard({id: bbcOption.flavor}) ? 'common' : 'bbc';
                this.data.set('subnetType', subnetType);
            }
        }
    }

    async attached() {
        this.initFormData();
        await this.initDatasource();

        // 编辑时回显
        this.nextTick(() => {
            this.handleEditData();
        });

        // 创建节点组编辑回显
        this.nextTick(() => {
            this.handleFillData();
        });
    }

    handleProductType() {
        const editData = this.data.get('editData') || this.data.get('fillData');
        //pay-config
        const payConfigRef = this.ref('payConfig');
        if (
            editData.productType === 'prepay' &&
            editData.instancePreChargingOption &&
            this.data.get('showPayType')
        ) {
            const {
                purchaseTime = 1,
                autoRenew = false,
                autoRenewTime = 1,
                autoRenewTimeUnit = 'month',
            } = editData.instancePreChargingOption;
            if (payConfigRef) {
                const data = {
                    payType: 'prepay',
                    duration: purchaseTime,
                    autoRepay: autoRenew,
                    repayCycle: autoRenewTimeUnit === 'month' ? autoRenewTime : autoRenewTime * 12,
                    repayCycleType: autoRenewTimeUnit.toUpperCase(),
                };
                payConfigRef.data.set('formData', data);
            }
        }
    }

    handleEditData() {
        // 编辑回显
        const editData = this.data.get('editData');
        if (editData) {
            //pay-config
            this.handleProductType();

            // 机型，架构，类型
            const {flavor} = this.data.get('editData.bbcOption') || {};
            const allFlavors = this.data.get('allFlavors');
            const selected = allFlavors?.find(e => e.id === flavor);
            if (selected) {
                const arch = BbcFlavorType.valueIndex?.[selected.type]?.arch;
                this.data.set('formData.arch', arch);
                this.data.set('formData.type', selected.type);
            }

            // 镜像类型
            if (editData?.image?.imageId) {
                let {imageType, imageId, osName} = editData.image;
                if (imageType) {
                    imageType =
                        Object.entries(ImageTypeMap).find(e => e[1] === imageType)?.[0] ||
                        imageType;
                }

                const {osNameMap, osVersionMap} = this.data.get('datasource');
                const osNameDS = (osNameMap && osNameMap[imageType]) || [];
                const osVersionDS = (osVersionMap && osVersionMap[imageType]) || [];
                const imageIdDS = osVersionDS?.[osName] || [];

                this.data.merge('datasource', {
                    osVersion: osVersionDS,
                    osName: osNameDS,
                    imageId: imageIdDS,
                });
                imageType && this.data.set('formData.imageType', imageType);
                osName && this.data.set('formData.osName', osName);
                this.nextTick(() => {
                    imageId && this.data.set('formData.imageId', imageId);
                });
            }
            // raid 文件系统
            const {diskInfo, rootPartitionType, dataPartitionType} =
                this.data.get('editData.bbcOption') || {};
            if (diskInfo) {
                this.data.set('formData.raid', diskInfo);
            }
            if (rootPartitionType) {
                this.data.set('formData.rootPartitionType', rootPartitionType);
            }
            if (dataPartitionType) {
                this.data.set('formData.dataPartitionType', dataPartitionType);
            }
            // 系统盘
            if (editData?.cdsData?.sysDiskData?.sysDiskInGB) {
                this.data.set('formData.sysDiskSize', editData.cdsData.sysDiskData.sysDiskInGB);
            }
            // 实例名称回显
            const {instanceName, sshKeyID} = editData;

            const systemConfig = this.ref('systemConfigPanel');
            if (systemConfig?.data?.set) {
                if (instanceName) {
                    systemConfig.data.merge('formData', {nameType: 'custom', name: instanceName});
                }

                if (sshKeyID) {
                    systemConfig.data.merge('formData', {
                        adminPassType: 'keypair',
                        keypairId: sshKeyID,
                    });
                }
            }
            const {deploySetID} = editData;
            // 部署集
            const deploysetConfigPanel = this.ref('deploysetConfigPanel');
            if (deploysetConfigPanel && deploysetConfigPanel?.data?.set && deploySetID) {
                deploysetConfigPanel.data.set('formData.deployId', deploySetID);
            }

            //备选机型隐藏一些字段
            const hideCommonItem = this.data.get('hideCommonItem');
            if (hideCommonItem) {
                const loginComp = this.ref('systemConfigPanel');
                if (loginComp?.data?.set) {
                    loginComp.data.set('formData.adminPassType', 'random');
                }
            }
        }
    }

    handleFillData() {
        const fillData = this.data.get('fillData');
        if (fillData) {
            //pay-config
            this.handleProductType();
            // 机型，架构，类型
            const {bbcOption = {}, name, sshKeyID, deploySetID, replicas} = fillData;
            const {flavor} = bbcOption;
            const allFlavors = this.data.get('allFlavors');
            const selected = allFlavors?.find(e => e.id === flavor);
            if (selected) {
                const arch = BbcFlavorType.valueIndex?.[selected.type]?.arch;
                this.data.set('formData.arch', arch);
                this.data.set('formData.type', selected.type);
            }
            // 镜像类型
            if (fillData.image) {
                let {imageType, imageId, osName} = fillData.image;
                if (imageType) {
                    imageType =
                        Object.entries(ImageTypeMap).find(e => e[1] === imageType)?.[0] ||
                        imageType;
                }
                const {osNameMap, osVersionMap} = this.data.get('datasource');
                const osNameDS = (osNameMap && osNameMap[imageType]) || [];
                const osVersionDS = (osVersionMap && osVersionMap[imageType]) || [];
                const imageIdDS = osVersionDS?.[osName] || [];
                this.data.merge('datasource', {
                    osVersion: osVersionDS,
                    osName: osNameDS,
                    imageId: imageIdDS,
                });
                imageType && this.data.set('formData.imageType', imageType);
                osName && this.data.set('formData.osName', osName);
                this.nextTick(() => {
                    imageId && this.data.set('formData.imageId', imageId);
                });
            }
            // raid 文件系统
            const {diskInfo, rootPartitionType, dataPartitionType} = bbcOption;
            if (diskInfo) {
                this.data.set('formData.raid', diskInfo);
            }
            if (rootPartitionType) {
                this.data.set('formData.rootPartitionType', rootPartitionType);
            }
            if (dataPartitionType) {
                this.data.set('formData.dataPartitionType', dataPartitionType);
            }
            // 系统盘
            if (fillData?.cdsData?.sysDiskData?.sysDiskInGB) {
                this.data.set('formData.sysDiskSize', fillData.cdsData.sysDiskData.sysDiskInGB);
            }
            // 实例名称回显
            const systemConfig = this.ref('systemConfigPanel');
            if (systemConfig?.data?.set && name) {
                if (name) {
                    systemConfig.data.merge('formData', {nameType: 'custom', name});
                }

                if (sshKeyID) {
                    systemConfig.data.merge('formData', {
                        adminPassType: 'keypair',
                        keypairId: sshKeyID,
                    });
                }
            }
            // 台数
            if (replicas) {
                this.data.set('formData.purchaseNum', replicas);
            }
            // 部署集
            const deploysetConfigPanel = this.ref('deploysetConfigPanel');
            if (deploysetConfigPanel && deploysetConfigPanel?.data?.set && deploySetID) {
                deploysetConfigPanel.data.set('formData.deployId', deploySetID);
            }
        }
    }

    // 原getSubmitData
    getFormData() {
        const {
            productType,
            logicalZone,
            type,
            flavor,
            imageType,
            osName,
            osType,
            imageId,
            raid,
            rootPartitionType,
            dataPartitionType,
            sysDiskSize,
            purchaseNum,
            selectedSubnetItem,
            selectedSecurityItem,
            subnetId,
        } = this.data.get('formData');

        // 系统
        const isNumaWhiteList = this.data.get('isNumaWhiteList');
        const systemConfigPanel =
            this.ref('systemConfigPanel') && this.ref('systemConfigPanel').getFormData();
        const {nameType, adminPassType, keypairId, adminPass, name, enableNuma, hostname} =
            systemConfigPanel || {};

        // 高级配置 数据注入
        const isUserDataWhiteList = this.data.get('isUserDataWhiteList');
        const userData =
            this.ref('advancedConfigPanel') && this.ref('advancedConfigPanel').getConfig();

        const {osVersion} = this.data.get('datasource');
        const value = _.find(osVersion[osName], item => item.value === imageId);

        const deploySetData =
            this.ref('deploysetConfigPanel') && this.ref('deploysetConfigPanel').getComponentData();
        const {deploySetID, deploySetName} = deploySetData || {};

        //付费
        const instanceChargingType = productType === 'prepay' ? 'Prepaid' : 'Postpaid';
        let payConfig = {
            instanceChargingType: instanceChargingType,
        };
        const {duration, autoRepay, repayCycle, repayCycleType} =
            this.ref('payConfig')?.getFormData() || {};
        if (productType === 'prepay') {
            payConfig.instancePreChargingOption = {
                purchaseTime: duration,
                purchaseTimeUnit: 'month',
                autoRenew: autoRepay,
            };
            if (autoRepay) {
                payConfig.instancePreChargingOption.autoRenewTime = repayCycle;
                payConfig.instancePreChargingOption.autoRenewTimeUnit = repayCycleType || 'month';
            }
        }
        if (!this.data.get('showPayType')) {
            payConfig = {}; // 创建集群的时候不需要
        }

        let result = {
            type,
            flavorId: flavor ? flavor.id : '',
            flavor,
            imageType,
            osName,
            osType,
            imageId,
            osVersion: value ? value.name : '',
            userData: isUserDataWhiteList && userData ? userData.userData : '',
            raid,
            diskInfo: raid,
        };
        if (this.data.get('partitionResizeable')) {
            result.rootPartitionType = rootPartitionType;
            result.dataPartitionType = dataPartitionType;
        }

        const formData = {
            serviceType: 'BBC',
            purchaseNum,
            selectedSubnetItem,
            selectedSecurityItem,
            productType,
            logicalZone,
            subnetId,
            nameType,
            name,
            hostname,
            enableNuma: isNumaWhiteList ? enableNuma : false,
            adminPassType,
            adminPass,
            keypairId,
            adminPassConfirm: adminPass,
            deploySetID,
            deploySetName,
            ...payConfig,
        };

        return _.extend({}, formData, {bbc: result}, {cds: {sysDiskSize: +sysDiskSize}});
    }

    /**
     * 初始化表单数据
     * 主要是需要初始化
     */
    initFormData() {
        this.data.merge('formData', {
            region: $context.getCurrentRegionId(),
            type: BbcFlavorType.IO,
            imageType: ImageType.COMMON,
            sysDiskSize: 20,
            purchaseNum: 1,
            rootPartitionType: PartitionType.EXT4,
            dataPartitionType: PartitionType.EXT4,
        });
    }

    async initDatasource() {
        this.watchers();

        await Promise.all([this.getQuota(), this.getLogicalZone(), this.getImageAndOs()]);
        await Promise.all([this.getFlavors(), this.getSubnetList()]);
        this.loadBbcWhiteTags();
    }

    watchers() {
        this.watch('formData.flavor', flavor => {
            const eBPFEnabled = this.data.get('eBPFEnabled');
            this.data.merge('formData', {
                imageType: eBPFEnabled ? ImageType.CUSTOM : ImageType.COMMON,
            });

            this.updateImages();
            this.updateRaid();
            this.updatePurchaseNum();

            this.dispatch('instance_flavor_disabled', {
                disable: flavor ? !!flavor.xui__disabled : true,
                message: flavor ? (flavor.xui__disabled ? '当前配置不可用' : '') : '请选择一个配置',
            });
        });

        this.watch('formData.imageType', () => {
            this.nextTick(() => this.updateOsNameOsVersion());
        });

        this.watch('formData.osName', osName => {
            const {osVersion} = this.data.get('datasource');
            const osNameDS = this.data.get('datasource.osName');

            let osType = _.find(osNameDS, item => item.value === osName);

            if (osName && osVersion) {
                const imageIdDS = osVersion[osName];
                if (imageIdDS) {
                    this.data.merge('datasource', {imageId: imageIdDS});
                    this.data.merge('formData', {
                        osName,
                        osType: osType ? osType.osType : '',
                        imageId: imageIdDS.length ? imageIdDS[0].value : '',
                        supportUserData: imageIdDS.length ? imageIdDS[0].supportUserData : false,
                    });
                }
            }
        });

        this.watch('formData.imageId', () => this.onClearUserData());

        this.watch('formData.raid', raid => this.updatePartition());

        this.watch('formData.sysDiskSize', sysDiskSize => {
            this.updatePartition();
        });
    }

    async getQuota() {
        let quota = null;

        try {
            const result = await this.$http.bbcInstanceQuota();
            quota = {
                ...result,
                available: result.bbcQuotaTotal - result.bbcQuotaCreated,
            };
        } catch (error) {
            quota = {
                // 此默认值跟后端是一样的……
                bbcQuotaTotal: 2,
                bbcQuotaCreated: 0,
                isPostpayWhite: false, // 值为true的时候需要展示后付费tab
            };
        }

        this.data.set('quota', quota);

        this.data.set('config.purchaseNum.max', quota.available);

        const masterFlavor = this.data.get('masterFlavor');
        const existedNodeNum = this.data.get('existedNodeNum');
        if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
            const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
            const scaleMax = masterFlavorNum - existedNodeNum;
            const purchaseNum = this.data.get('formData.purchaseNum') || 0;
            this.data.set(
                'config.purchaseNum.max',
                scaleMax < purchaseNum ? purchaseNum : scaleMax,
            );
        }

        return quota;
    }

    loadBbcWhiteTags() {
        return this.$http.getBbcWhiteTags().then(({result: res}) => {
            if (res) {
                // 系统盘480g白名单
                this.data.set('is480White', !!res.extendSysDiskSup);
                this.data.set('bbcEnableXfs', !!res.allowPartitionSelection);
                this.updateSysLimit();
                // 数据注入白名单
                this.data.set('isUserDataWhiteList', !!res.userData);
                // numa开关白名单
                this.data.set('isNumaWhiteList', !!res.enableNuma);
            }
        });
    }

    updateSysLimit() {
        const sysDiskSize = this.data.get('formData.sysDiskSize');
        const max = this.data.get('is480White') ? 480 : 100;

        this.data.set('config.sysDiskSize.max', max);
        this.data.set('formData.sysDiskSize', Math.min(sysDiskSize, max));
    }

    updateRaid() {
        const {flavors} = this.data.get('datasource');
        const {flavor} = this.data.get('formData');
        if (flavor?.diskInfos) {
            const raidDS = _.map(flavor.diskInfos, item => ({
                text: item.raidDisplay,
                value: item.raid,
                tip: item.raidDisplay,
            }));

            this.data.merge('datasource', {raid: raidDS});

            let raid = _.contains(_.keys(flavor.diskInfos), 'Raid5')
                ? 'Raid5'
                : _.keys(flavor.diskInfos)[0];
            this.data.merge('formData', {raid});
        }
    }

    updatePurchaseNum() {
        let formData = this.data.get('formData');
        let quota = this.data.get('quota');

        let max = formData.flavor
            ? Math.min(formData.flavor?.count, quota.bbcQuotaTotal - quota.bbcQuotaCreated)
            : quota.bbcQuotaTotal - quota.bbcQuotaCreated;

        // 如果当前购买的套餐已售罄，purchaseNumw为0时，切换到别的可购买套餐时，需要改变购买插件的初始值
        if (max > 0 && formData.purchaseNum === 0) {
            this.data.merge('formData', {
                purchaseNum: 1,
            });
        }

        const masterFlavor = this.data.get('masterFlavor');
        const existedNodeNum = this.data.get('existedNodeNum');
        if (!(masterFlavor && (existedNodeNum || existedNodeNum == 0))) {
            this.data.merge('config.purchaseNum', {max});
        }
        this.data.merge('formData', {
            purchaseNum: max === 0 ? 0 : 1,
        });
    }

    updatePartition() {
        const {flavor, sysDiskSize, raid} = this.data.get('formData');

        const diskInfo = (flavor.diskInfos && flavor.diskInfos[raid]) || {};
        const swapSize = this.data.get('is480White') ? 0 : 1;

        let partition = [
            {name: 'swap分区', value: swapSize, width: 5},
            {name: '根分区', value: sysDiskSize},
            {name: '/home分区', value: +diskInfo.sysAndHomeSize - sysDiskSize - swapSize},
        ];
        const sysAndHomeSizeOnGB = gibToGb(+diskInfo.sysAndHomeSize);
        const dataDiskSizeOnGB = gibToGb(+diskInfo.dataDiskSize);
        let partitionSize = {
            root: sysDiskSize,
            swap: swapSize,
            home: sysAndHomeSizeOnGB - sysDiskSize - swapSize,
            dataDiskSize: +diskInfo.dataDiskSize,
            sysAndHomeSize: +diskInfo.sysAndHomeSize,
            dataDiskSizeOnGB,
            sysAndHomeSizeOnGB,
        };

        if (diskInfo.dataDiskSize) {
            partition.push({
                name: diskInfo.dataDiskName,
                value: +diskInfo.dataDiskSize,
            });
        }

        this.data.set('raidDesc', diskInfo.description);
        this.data.merge('datasource', {partition, partitionSize});
    }

    onInstanceChange(e) {
        if (e.osType) {
            this.data.set('osType', e.osType);
        }

        if (e.config) {
            let instanceConfig = {
                ...e.config,
            };
            if (e.config.ephemeralDiskConfig) {
                instanceConfig.ephemeralDiskConfig = (e.config.ephemeralDiskConfig || []).filter(
                    // 这种类型的盘iaas不建议使用过滤掉
                    v => !(v.type === 'LOCAL_PV_SSD' && v.capacityInGb === 480),
                );
            }
            this.data.set('instanceConfig', instanceConfig);
        }
    }

    async getLogicalZone() {
        try {
            this.data.set('zone.loading', true);
            const {result: data} = await this.$http.getBbcZoneList();
            const logicalZone = data.map(item => ({
                text: window.$zone.getLabel(item),
                value: item,
            }));

            this.data.merge('datasource', {logicalZone});
            this.data.merge('formData', {
                logicalZone: this.data.get('formData.logicalZone') || logicalZone[0]?.value,
            });
            this.data.set('zone.loading', false);
        } catch (error) {
            this.data.set('zone.loading', false);
            notification.error('获取可用区列表失败');
        }
    }

    async getFlavors() {
        this.data.set('flags.flavorLoading', true);
        const isVpcHybird = this.data.get('isVpcHybird');
        const {productType, logicalZone} = this.data.get('formData');

        let {result: flavors = []} = await this.$http.getBbcInstanceFlavor(
            {productType, logicalZone},
            silence,
        );
        const subnetType = this.data.get('subnetType');
        flavors = flavors.filter(e => {
            const filterFlag = isSmartCard(e);
            return subnetType === 'common' ? filterFlag : !filterFlag;
        });
        flavors = flavors.map(item => {
            return {
                ...item,
                raids: item.diskInfos ? Object.keys(item.diskInfos) : [],
                /* eslint-disable @typescript-eslint/naming-convention,camelcase */
                xui__disabled: item.count < 1 || (isVpcHybird && !isSmartCard(item)),
                /* eslint-enable @typescript-eslint/naming-convention,camelcase */
            };
        });
        this.data.set('allFlavors', _.cloneDeep(flavors));
        this.initSearchBoxDs(flavors);
        flavors = _.groupBy(flavors, 'type');
        const flavorsDS = {};
        Object.keys(flavors).forEach(key => {
            flavorsDS[key] = flavors[key].map(item => item);
        });

        const {clusterType, networkMode} = this.data.get();

        const type = [];
        _.each(BbcFlavorType.toArray(), item => {
            // AI HPC集群 非VPC路由模式 隐藏高性能实例
            if (
                !(
                    clusterType === ClusterType.AI_HPC &&
                    networkMode !== NetworkMode.AUTO_DETECT &&
                    item.alias === 'HPC'
                )
            ) {
                type.push({
                    ...item,
                    // editData不能修改类型，如果需要知道editData的类型，从flavorsDS里面找
                    disabled: this.data.get('editData') || !flavors[item.value],
                });
            }
        });

        // 根据type可用，判断对应arch架构是否可用
        const archGroup = _.groupBy(type, 'arch');
        const archList = Arch.toArray('X86', 'ISOMERISM').map(item => {
            const archTypes = archGroup[item.value];
            return {
                ...item,
                disabled: archTypes.every(el => el.disabled),
            };
        });
        this.data.merge('datasource', {
            flavors: flavorsDS,
            type,
            archList,
        });
        const {flavor} =
            this.data.get('editData.bbcOption') || this.data.get('fillData.bbcOption') || {};
        const allFlavors = this.data.get('allFlavors');
        const selected = allFlavors.find(e => e.id === flavor);
        if ((this.data.get('editData') || this.data.get('fillData')) && selected) {
            const arch = BbcFlavorType.valueIndex?.[selected.type]?.arch;
            this.data.set('formData.arch', arch);
            this.data.set('formData.type', selected.type);
            this.onTypeChange({value: selected.type});
            let flavorTableDs = flavorsDS[selected.type] || [];
            const {cpu, memory} = this.data.get('filter');
            const keyword = this.data.get('searchbox.value');
            cpu && (flavorTableDs = flavorTableDs.filter(el => +el.cpu === +cpu));
            memory && (flavorTableDs = flavorTableDs.filter(el => +el.memory === +memory));
            if (keyword) {
                flavorTableDs = flavorTableDs.filter(
                    item =>
                        item.id && _.includes(item.id.toLowerCase(), keyword.trim().toLowerCase()),
                );
            }
            if (this.data.get('hideCommonItem')) {
                flavorTableDs = flavorTableDs.filter(
                    e => e.cpu === selected.cpu && e.memory === selected.memory,
                );
            }
            let index = flavorTableDs.findIndex(e => e.id === flavor);
            if (index < 0) {
                index = flavorTableDs.findIndex(e => !e.xui__disabled);
                index < 0 && (index = 0);
            }
            this.data.merge('flavorTable', {
                datasource: flavorTableDs,
                selectedIndex: index + '',
            });
            this.data.merge('formData', {
                flavor: selected,
            });
            this.nextTick(() => {
                this.data.set('editDataInited', true);
            });
        } else {
            const availableArch = _.find(archList, item => !item.disabled);
            let defaultArch = availableArch ? availableArch.value : archList[0].value;
            this.data.merge('formData', {
                arch: defaultArch,
            });
            this.onArchChange({value: defaultArch});
            this.filterInstance();
        }
        this.data.set('flags.flavorLoading', false);
    }

    onArchChange(e) {
        const typeDatasource =
            this.data.get('datasource.type').filter(item => item.arch === e.value) || [];
        const target = typeDatasource.find(e => !e.disabled) || typeDatasource[0];
        if (target) {
            this.data.set('formData.type', target.value);
            this.onTypeChange({value: target.value});
        }
    }

    filterByClassify() {
        const allFlavors = this.data.get('allFlavors');
        const type = this.data.get('formData.type');
        const flavorGroup = _.groupBy(allFlavors, 'type');
        const classifiedFlavors = flavorGroup[type];
        const index = _.findIndex(classifiedFlavors, item => !item.xui__disabled); // 默认都售罄的话取第一个
        const defaultIndex = index < 0 ? 0 : index;
        this.data.merge('flavorTable', {
            datasource: classifiedFlavors,
            selectedIndex: `${defaultIndex}`,
        });
        this.data.merge('formData', {
            flavor: classifiedFlavors?.[defaultIndex],
        });
    }

    filterBySearch() {
        if (this.data.get('editData') && !this.data.get('editDataInited')) {
            return;
        }
        let allFlavors = this.data.get('allFlavors');
        const {flavor} = this.data.get('editData.bbcOption') || {};
        const selected = allFlavors.find(e => e.id === flavor);
        if (selected && this.data.get('formData.type')) {
            allFlavors = allFlavors.filter(e => e.type === this.data.get('formData.type'));
        }
        const {cpu, memory} = this.data.get('filter');
        const keyword = this.data.get('searchbox.value');
        cpu && (allFlavors = allFlavors.filter(el => +el.cpu === +cpu));
        memory && (allFlavors = allFlavors.filter(el => +el.memory === +memory));
        keyword &&
            (allFlavors = allFlavors.filter(
                ({id}) => id && _.includes(id.toLowerCase(), keyword.trim().toLowerCase()),
            ));
        let avaliableIndex = _.findIndex(allFlavors, item => !item.xui__disabled);
        // 都售罄取第一个
        avaliableIndex < 0 && (avaliableIndex = 0);
        this.data.merge('flavorTable', {
            datasource: allFlavors,
            selectedIndex: `${avaliableIndex}`,
        });
        this.data.merge('formData', {
            flavor: allFlavors[avaliableIndex],
        });
    }

    getImageAndOs() {
        return Promise.all([
            this.getFlavorImageslist({
                imageType: ImageType.COMMON,
                keyword: 'active',
                keywordType: 'status',
            }),
            this.getImageslist({
                imageType: ImageType.CUSTOM,
                keyword: 'active',
                keywordType: 'status',
            }),
            this.getSharingImageList({
                imageType: ImageType.SHARING,
                keyword: 'active',
                keywordType: 'status',
            }),
        ]);
    }

    // 初始化所有返回的规格（供搜索框提示用）
    initSearchBoxDs(flavors) {
        const flavorIds = [];
        let cpus = [];
        let memorys = [];
        const hideCommonItem = this.data.get('hideCommonItem');
        const {flavor} = this.data.get('editData.bbcOption') || {};
        const allFlavors = this.data.get('allFlavors');
        const selected = allFlavors.find(e => e.id === flavor);
        // 根据返回的套餐，提取所有的套餐、cpu数、内存数、排序后作为下拉框数据源
        flavors.forEach(flavor => {
            flavorIds.every(el => el.value !== flavor.id) &&
                flavorIds.push({text: flavor.id, value: flavor.id, flavor});

            !_.includes(cpus, flavor.cpu) && cpus.push(flavor.cpu);

            !_.includes(memorys, flavor.memory) && memorys.push(flavor.memory);
        });
        const cpusDatasource = cpus
            .sort((a, b) => a - b)
            .map(item => ({text: `${item}核`, value: item}));
        const memorysDatasource = memorys
            .sort((a, b) => a - b)
            .map(item => ({text: `${item}`, value: item}));
        if (hideCommonItem && selected) {
            // 备选机型
            this.data.set(
                'searchbox.textDatasource',
                flavorIds.filter(
                    e =>
                        e.flavor.cpu === selected.cpu &&
                        e.flavor.memory === selected.memory &&
                        e.flavor.type === selected.type,
                ),
            );
            this.data.merge('filter', {
                cpu: selected.cpu,
                memory: selected.memory,
            });
            this.data.merge('datasource', {
                cpusDatasource: cpusDatasource.filter(e => e.value === selected.cpu),
                memorysDatasource: memorysDatasource.filter(e => e.value === selected.memory),
            });
        } else {
            this.data.set('searchbox.textDatasource', flavorIds);
            this.data.merge('datasource', {
                cpusDatasource: [{text: '全部', value: ''}, ...cpusDatasource],
                memorysDatasource: [{text: '全部', value: ''}, ...memorysDatasource],
            });
        }
    }
    onInstanceSearch() {
        const keyword = this.data.get('searchbox.value');
        this.data.set('filterByKeyword', !!keyword);
        this.filterInstance();
    }

    onInstanceSpecSelect(e) {
        this.data.set('searchbox.value', e.value);
        this.onInstanceSearch();
    }

    onCpuChange(e) {
        this.data.set('filter.cpu', e.value);
        this.filterInstance();
    }

    onMemoryChange(e) {
        this.data.set('filter.memory', e.value);
        this.filterInstance();
    }

    onSysDiskSizeChange(e) {
        const sysDiskSize = +e.value;
        this.data.merge('formData', {sysDiskSize});
    }

    resetSearchStatus() {
        const {cpu, memory} = this.data.get('filter');
        const keyword = this.data.get('searchbox.value');
        if (!cpu && !memory && !keyword) {
            this.data.set('filterByKeyword', false);
        }
    }

    filterInstance() {
        this.resetSearchStatus();
        const withClassify = this.data.get('withClassify');
        // 通过分类筛选
        if (withClassify) {
            this.filterByClassify();
        }
        // 通过cpu、内存、关键字筛选
        else {
            this.filterBySearch();
        }
    }

    // 套餐对应镜像列表
    getFlavorImageslist(params) {
        return this.$http.getBbcFlavorImageList(params, silent).then(({result: data}) => {
            let osList = _.map(data, res => ({
                id: res.flavorId,
                Os: this.transforImageAndOs(res.images),
            }));
            this.data.set(`datasource.${params.imageType}`, osList);
        });
    }

    /**
     * 得到所有类型的镜像列表.
     *
     * @param {Object} params 请求参数.
     * @return {er.Deferred}
     */
    getImageslist(params) {
        return this.$http.getBbcFlavorCustomImageList(params, silent).then(({result: data}) => {
            let osList = _.map(data, res => ({
                id: res.flavorId,
                Os: this.transforImageAndOs(res.images),
            }));

            this.data.set(`datasource.${params.imageType}`, osList);
        });
    }

    getSharingImageList(params) {
        return this.$http.getBbcFlavorSharingImageList(params, silent).then(data => {
            let osList = _.map(data, res => ({
                id: res.flavorId,
                Os: this.transforImageAndOs(res.images),
            }));
            this.data.set(`datasource.${params.imageType}`, osList);
        });
    }

    transforImageAndOs(result) {
        let osName = [];
        let osVersion = {};
        let osData = _.groupBy(result, 'osName');

        _.each(osData, (items, key) => {
            osName.push({
                text: key,
                name: key,
                value: key,
                osType: items[0].osType,
            });
            osVersion[key] = _.map(items, item => {
                return {
                    text: item.name,
                    name: item.name,
                    value: item.id,
                    imageId: item.id,
                    supportUserData: item.supportUserData, // 支持数据注入
                };
            });
        });
        return {osName, osVersion};
    }

    updateImages() {
        const eBPFEnabled = this.data.get('eBPFEnabled');
        let formData = this.data.get('formData');

        let flavorId = formData?.flavor?.id;

        let commonImages = _.find(
            this.data.get(`datasource.${ImageType.COMMON}`),
            item => item.id === flavorId,
        );
        let commonOs = commonImages ? commonImages.Os : {osName: [], osVersion: {}};

        let customImages = _.find(
            this.data.get(`datasource.${ImageType.CUSTOM}`),
            item => item.id === flavorId,
        );
        let customOs = customImages ? customImages.Os : {osName: [], osVersion: {}};

        let sharingImages = _.find(
            this.data.get(`datasource.${ImageType.SHARING}`),
            item => item.id === flavorId,
        );
        let sharingOs = sharingImages ? sharingImages.Os : {osName: [], osVersion: {}};

        let osNameMap = {
            [ImageType.COMMON]: commonOs.osName,
            [ImageType.CUSTOM]: customOs.osName,
            [ImageType.SHARING]: sharingOs.osName,
        };
        let osVersionMap = {
            [ImageType.COMMON]: commonOs.osVersion,
            [ImageType.CUSTOM]: customOs.osVersion,
            [ImageType.SHARING]: sharingOs.osVersion,
        };

        this.data.merge('datasource', {
            osNameMap,
            osVersionMap,
        });

        // imageType
        let imageType = ImageType.toArray('COMMON', 'CUSTOM', 'SHARING').map(item => ({
            ...item,
            disabled: !(osNameMap && osNameMap[item.value] && osNameMap[item.value].length > 0),
        }));
        if (eBPFEnabled) {
            imageType = imageType.map(d => {
                return {
                    ...d,
                    disabled: d.value === 'bbcCommon' || d.disabled,
                    tip:
                        d.value === 'bbcCommon' && 'DataPath V2 不支持公共镜像，请选择其他类型镜像',
                };
            });
        }
        this.data.merge('datasource', {imageType});

        this.updateOsNameOsVersion();
    }

    updateOsNameOsVersion() {
        const {osNameMap, osVersionMap} = this.data.get('datasource');
        const {imageType} = this.data.get('formData');
        let osName = osNameMap[imageType]?.length
            ? osNameMap[imageType]?.filter(e => ['Ubuntu', 'CentOS'].includes(e.value))?.[0].name
            : '';
        let imageId =
            osName && osVersionMap[imageType][osName].length
                ? osVersionMap[imageType][osName][0].value
                : '';
        let supportUserData = imageId ? osVersionMap[imageType][osName][0].supportUserData : false;

        let osNameDS = (osNameMap && osNameMap[imageType]) || [];
        const osVersionDS = (osVersionMap && osVersionMap[imageType]) || [];
        const imageIdDS = osVersionDS?.[osName] || [];

        // 屏蔽Ubuntu和CentOS之外的镜像
        osNameDS = osNameDS.filter(e => ['Ubuntu', 'CentOS'].includes(e.value));

        this.data.merge('datasource', {
            osVersion: osVersionDS,
            osName: osNameDS,
            imageId: imageIdDS,
        });
        this.data.merge('formData', {osName, imageId, supportUserData});
    }

    onFlavorChange(e) {
        const flavor = e.selectedItems[0];
        this.data.merge('formData', {flavor});
    }

    // logicalzone变化
    async onLogicalZoneChange({value}) {
        this.data.merge('formData', {
            logicalZone: value,
        });
        this.getSubnetList();
        this.getFlavors();
    }

    /**
     * 配置类型变化
     *
     * @param {Object} e
     */
    onTypeChange(e) {
        const {flavors} = this.data.get('datasource');
        const flavorTableDs = flavors[e.value];

        const index = _.findIndex(flavorTableDs, item => !item.xui__disabled); // 默认都售罄的话取第一个
        const defaultIndex = index < 0 ? 0 : index;
        const flavor = flavorTableDs?.[defaultIndex];

        this.data.merge('flavorTable', {
            datasource: flavorTableDs,
            selectedIndex: defaultIndex + '',
        });

        this.data.merge('formData', {flavor});
    }

    async validateForm() {
        let tasks = [
            this.ref('form')?.validateForm(),
            this.ref('systemConfigPanel')?.validateForm(),
            this.ref('deploysetConfigPanel')?.valid(),
            this.ref('advancedConfigPanel') && this.ref('advancedConfigPanel').valid(),
        ];
        const isSmartCard = this.data.get('isSmartCard');
        if (isSmartCard) {
            const security = this.ref('security');
            security && tasks.push(security.validateSecurity());
        }
        return Promise.all(tasks);
    }

    onClearUserData() {
        this.ref('advancedConfigPanel') &&
            this.ref('advancedConfigPanel').data.set('formData.userData', '');
    }

    onSubnetChange(e) {
        this.data.merge('formData', {
            subnetId: e ? e.value : '',
        });
        const item = _.find(this.data.get('subnet.list'), d => d.value === e.value);
        this.data.set('formData.selectedSubnetItem', item || {});
    }

    onSubnetTypeChange() {
        this.nextTick(() => {
            this.getSubnetList();
            this.getFlavors();
        });
    }

    getSubnetList() {
        this.data.set('subnet.loading', true);
        const subnetType = this.data.get('subnetType');
        // 智能网卡BBC只能使用普通子网
        // 非智能网卡BBC智能使用BBC专用子网
        return this.data
            .get('sdk.bcc')
            .getSubnetList({
                subnetTypes: subnetType === 'common' ? [SubnetType.BCC] : [SubnetType.BBC],
                vpcId: this.data.get('vpcId'),
                zone: this.data.get('formData.logicalZone'),
            })
            .then(data => {
                let list = _.map(data || [], item => ({
                    text: getSubnetName(item.name) + (item.cidr ? '（' + item.cidr + '）' : ''),
                    value: item.shortId,
                }));
                if (list.length) {
                    const {vpcSubnetId} =
                        this.data.get('editData') || this.data.get('fillData') || {}; // 编辑回显
                    const target = list.find(e => e.value === vpcSubnetId);
                    if (target) {
                        this.data.set('formData.subnetId', target.value);
                        this.data.set('formData.selectedSubnetItem', target);
                    } else {
                        this.data.set('formData.subnetId', list[0].value);
                        this.data.set('formData.selectedSubnetItem', list[0]);
                    }
                } else {
                    this.data.set('formData.subnetId', null);
                    this.data.set('formData.selectedSubnetItem', null);
                }
                this.data.set('subnet.list', list);
                this.data.set('subnet.loading', false);
            })
            .catch(() => {
                this.data.set('subnet.loading', false);
                this.data.set('subnet.list', []);
            });
    }

    onCreateSubnet() {
        const selectedVpcItem = this.data.get('selectedVpcItem');
        if (selectedVpcItem) {
            const subnetType = this.data.get('subnetType');
            const payload = {
                availableService: ['BBC'],
                subnetTypes: subnetType === 'common' ? [SubnetType.BCC] : [SubnetType.BBC],
                vpcInfo: {
                    name: selectedVpcItem.text,
                    ipv6Cidr: selectedVpcItem.ipv6Cidr,
                    cidr: selectedVpcItem.cidr,
                    auxiliaryCidr: selectedVpcItem.auxiliaryCidr,
                    vpcId: selectedVpcItem.value,
                },
            };
            const dialog = new SubnetCreateDialog({
                sdk: new VpcSDK({
                    client: new HttpClient(),
                    context: window.$context,
                }),
                ...payload,
            });
            dialog.attach(document.body);
            dialog.on('create', () => {
                notification.success('创建子网成功');
                this.getSubnetList();
            });
            dialog.on('cancel', () => dialog && dialog.dispose());
            this.$childs.push(dialog);
        }
    }

    onPurchaseNumChange({value}) {
        this.data.merge('formData', {purchaseNum: value});
    }

    onPayConfigChange(data) {
        store.dispatch('setPayConfig', data);
    }
    onProductTypeChange(data) {
        if (data.value !== 'prepay') {
            store.dispatch('setPayConfig', {payType: 'postpay'});
        }
    }

    disposed() {
        _.each(this.$childs, comp => {
            comp.dispose();
        });
        this.$childs = [];
    }
}

@asComponent('@cce-cluster-create-add-bbc')
export default class AddBbcCustomStore extends connect.san(StoreMap)(AddBbcCustom) {}
