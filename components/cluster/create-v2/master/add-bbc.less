.ln30 {
    line-height: 30px;
}
.color-gray-light {
    color: #999;
}

.text-red {
    color: #ea2e2e;
}

.bbc-instance-create {
    .bui-radioselect .bui-radio-block {
        min-width: 80px;
    }

    .purchase-length-select .bui-radio-block {
        margin-bottom: 10px !important;
    }

    .app-legend {
        width: 980px;
        margin-top: 20px;
        padding: 15px 20px;
        box-sizing: border-box;
        border: 1px solid #ebebeb;

        > .bui-form-item > .bui-form-item-content {
            max-width: 80%;
        }
    }

    .sale-tip {
        position: relative;
        overflow: visible;

        &:after {
            content: '';
            display: block;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAALCAMAAABf9c24AAAAOVBMVEX0Z2f0Z2f0Z2f0Z2f0Z2f////+6+v3kJD+9fX4pKT1cXH1e3v7vr75rq72hYX94eH81tb7zMz4mpraofUuAAAABHRSTlPmyUg/UD89gQAAAHZJREFUGNOFj+0KhEAIRadSx6+m2vd/2FUhWGir82PmwtELtmWCW6alNXig7IFIPf59j6dSx8QAQn9IQRjAmMep6VzPLBXdV/+jQRxjQnkY6085lS4Mh2CwXrd9UzC2TaKHrlqPXOusEblT1nZMYvDt7rk92PkLiW0GyVNhGCcAAAAASUVORK5CYII=)
                no-repeat 0 0;
            width: 30px;
            height: 11px;
            position: absolute;
            top: -5px;
            left: 10px;
        }
    }

    .page-footer {
        display: flex;

        .shopping-cart {
            flex: inherit;
        }
    }

    .hidden {
        display: none;
    }

    .buybucket-tip {
        margin-left: 20px;
        margin-top: 10px;
        color: #f39000;
    }

    .order-confirm {
        .order-legend .content .item {
            display: flex;
            width: 33%;

            span {
                flex: 1;
            }
        }
    }

    .bui-form-item-static {
        line-height: 30px;
    }

    .ln30 {
        line-height: 30px;
    }

    .deployset-config-wrapper {
        .create-button {
            vertical-align: bottom;
            padding: 0;
        }
    }

    // ================= system-config =================
    .system-config-panel {
        .bui-form-item-instanceHostname {
            .bui-form-item-content {
                max-width: 80%;
            }
        }
    }
}

.bui-tiplayer.bui-tiplayer-tc {
    .detail-layer .detail-item > div {
        display: flex;

        > label {
            width: 70px;
        }

        > span {
            flex: 1;
        }
    }
}
.cce-cluster-master-add-bbc {
    .scale-info {
        color: #84868c;
    }
    .bui-table .bui-table-cell:first-child .bui-table-cell-text {
        padding-left: 16px;
    }
    .pay-config-new {
        .common-form-item .s-form-item-label {
            flex-basis: 112px;
        }
        .recommend-msg {
            left: 123px;
        }
    }
    .cce-cluster-create-subnet {
        display: inline;
    }
    .bui-form-item-securityGroupId {
        .bui-form-item-content {
            width: 1000px;
        }
    }
    .filter-wrapper {
        .content-item {
            justify-content: middle;
            align-items: center;
            label {
                margin-right: 12px;
            }
            display: inline-flex;
            margin-right: 26px;
            .instance-search-box {
                border-radius: 4px;
                input {
                    width: 248px !important;
                }
                .bui-button {
                    border: none;
                    padding: 0;
                    margin-left: -25px;
                    position: relative;
                    z-index: 99;
                }
            }
            .memory-unit {
                margin-left: 4px;
            }
        }
    }
    .system-partition {
        .partition {
            display: flex;
            line-height: 30px;
            .partition-classify {
                width: 150px;
            }
            .gib-info {
                width: 80px;
                text-align: right;
                margin-right: 20px;
            }
            .s-slider-mark-wrapper {
                margin-top: 0;
            }
        }
        .partition:not(:first-child) {
            padding-top: 8px;
        }
    }
    .disk-info {
        line-height: 28px;
    }
    .sys-disk-item,
    .data-disk-item {
        margin-bottom: 24px !important;
        .bui-form-item-label {
            font-weight: bold;
        }
    }
    .file-sys-item {
        width: 1100px;
        padding-top: 24px;
        border-top: 1px solid #e7f3fd;
    }
    .expected-node-tip {
        position: relative;
        top: 3px;
        left: -6px;
    }
    .node-group-create-tip {
        color: #ff9326;
    }
    .os-tip {
        display: flex;
        color: #84868c;
        margin-top: 8px;
    }
}
.instance-search-box {
    border-radius: 4px;
}
