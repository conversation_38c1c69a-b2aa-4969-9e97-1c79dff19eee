/**
 * @file components/cluster/create-v2/master/add-custom.js
 * <AUTHOR>
 */

import _ from 'lodash';
import $ from 'jquery';
import {Component, createComponentLoader} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {
    Form,
    FormItem,
    NumberTextline,
    CheckBox,
    TextBox,
    RadioSelect,
    Loading,
} from '@baiducloud/bce-ui/san';
import {Notification, InputNumber, Select} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';
import {
    StorageConfigPanel as BccStorageConfigPanel,
    InstanceConfigPanelPerf,
} from '@baidu/bce-bcc-sdk-san';
import {connect, store} from 'san-store';
import * as AsyncValidator from 'async-validator';
import tips from '../../../../utils/tips';
import moneyUtil from '@utils/money-util';
import {ClusterType, ProductType} from '../../../../utils/enums';
import cluster from '../../../../utils/cluster';
import {getSdk} from '../../../../utils/sdk';
import client from '../../../../utils/client';
import {isAbilityBanned} from '@utils/flavor-ability';
import ProductTypeSelector from '@components/product-type';
import {compareVersion, checkIsKMSRegion} from '@utils/util';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import deploysetConfigPanel from './bbc-partial/deployset-config-panel';
import ZoneSubnets from './zone-subnets';
import LoginConfig from '../../../common/login-config';
import EipConfig from '../../../cluster/eip-config';
import PayConfig from '../../../../pages/cluster/create-v2/components/pay-config';
import {NodeType} from '../enums';

const $flag = ServiceFactory.resolve('$flag');

const {asComponent, invokeBceSanUI} = decorators;

const Schema = AsyncValidator.default;

const biddingRules = new Schema({
    bidTimeout: [
        {required: true, message: '请填写抢占超时时间'},
        {pattern: /^[1-9][0-9]*$/, message: '请填写正整数'},
    ],
});

let UnsupportedInstanceTypes = null;
let specAvailableStatusMap = {
    value: null,
};

const instanceConfigLoader = createComponentLoader(async () => {
    try {
        const [unsupport, specs] = await Promise.all([
            client.unsupportedInstanceTypes(),
            client.getSpecAvailableStatus(),
        ]);
        // 不支持的机型
        UnsupportedInstanceTypes = unsupport?.instanceTypes;
        // 所有机型是否可用，是否适配的map
        if (specs?.machineSpecList) {
            if (!specAvailableStatusMap.value) {
                specAvailableStatusMap.value = {};
            }
            specs.machineSpecList.forEach(item => {
                specAvailableStatusMap.value[item.machineSpec] = item.status;
            });
        }
        return InstanceConfigPanelPerf;
    } catch (error) {
        UnsupportedInstanceTypes = null;
        specAvailableStatusMap = {value: null};
        return InstanceConfigPanelPerf;
    }
});

function waitFor(conditionFunction, retry = 300) {
    let count = 0;
    const poll = resolve => {
        if (conditionFunction() || count >= retry) {
            setTimeout(resolve, 0);
        } else {
            count++;
            setTimeout(() => poll(resolve), 100);
        }
    };

    return new Promise(poll);
}

/* eslint-disable */
const template = html`<div class="cce-cluster-master-add-custom">
    <ui-form s-ref="form" form-data="{=formData=}">
        <ui-form-item
            inline
            label-width="{{100}}"
            label="付费方式："
            required
            name="productType"
            style="display: {{!hideCommonItem ? 'block':'none'}}"
            s-if="showPayType"
        >
            <label slot="label" title="付费方式：">付费方式：</label>
            <ui-loading size="small" s-if="zoneLoading" />
            <product-type-selector
                s-else
                value="{=formData.productType=}"
                on-change="onProductTypeChange"
                datasource="{{productTypeList}}"
                layout="prepay,postpay,bidding"
            ></product-type-selector>
            <div s-if="isBidding" slot="help" class="bui-form-item-help-bidding">
                抢占式实例相对于按量付费有较大优惠，您能稳定持有实例一小时，之后当市场价格高于您的出价或者资源库存不足时，
                <span class="cce-tip-warn">实例会自动释放，请做好数据备份工作。</span>
                有状态应用，比如数据库，不宜使用抢占实例。
                <a href="${tips.doc.bidding}" target="_blank">详细说明</a>
            </div>
        </ui-form-item>

        <pay-config
            s-ref="payConfig"
            initPayType="prepay"
            s-if="showPayType && formData.productType === 'prepay'"
            style="display: {{!hideCommonItem ? 'block':'none'}}"
            on-change="onPayConfigChange"
        />

        <x-zone-subnets
            s-ref="zoneSubnets"
            sdk="{{sdk}}"
            instanceNodeType="{{instanceNodeType}}"
            nodeType="{{nodeType}}"
            form-data="{=formData=}"
            zoneLoading="{=zoneLoading=}"
            eniVPCSubnetIDs="{{eniVPCSubnetIDs}}"
            on-zoneChange="onZoneChange"
        />

        <div
            class="cce-cluster-create-custom-instance-wrap {{imageDisabledByUbuntu24 && clusterUuid ? 'invalid-image' : ''}}"
        >
            <x-instance
                s-ref="instance"
                isEni="{{isEni}}"
                isCce="{{true}}"
                showPagination="{{false}}"
                specAvailableStatusMap="{{specAvailableStatusMap}}"
                resourceType="{{resourceType}}"
                zone="{{formData.logicalZone}}"
                sdk="{{sdk}}"
                instance="{{initialInstanceData}}"
                ehcClusterId="{{ehcClusterId}}"
                supportBuySoldout="{{supportBuySoldout}}"
                cceSelectedEnhance="{{cceSelectedEnhance}}"
                isNodeGroupCreate="{{isNodeGroupCreate}}"
                formData="{{{imageType: initialImageType}}}"
                on-change="onInstanceChange"
                flavor-filter="{{flavorFilter}}"
                product-type="{{formData.productType}}"
                biddingPrices="{{biddingPrices}}"
                unsupportClassify="{{unsupportClassify}}"
                sceneTabsdisplayNone="{{true}}"
                isSupport20GDisk="{{isSupport20GDisk}}"
                hiddenProductClassify
                armInstanceWhiteList="{{armInstanceWhiteList}}"
                bccCustomDriver
                enableCheckGPUDriver
                useNewImageApi
                prepareData="{{bccPrepareData}}"
                imageTypeFilter="{{imageTypeFilter}}"
                imageOsFilter="{{imageOsFilter}}"
                osDataFilter="{{osDataFilter}}"
            >
                <ui-form
                    slot="bindding-config"
                    s-ref="biddingForm"
                    form-data="{=biddingFormData=}"
                    rules="{{biddingRules}}"
                    class="cce-cluster-create-custom-bidding"
                    s-if="isBidding && selectedFlavor"
                >
                    <ui-form-item
                        inline
                        label-width="{{100}}"
                        label="抢占超时时间："
                        name="bidTimeout"
                        help="若抢占超时，则自动取消该抢占实例订单，节点创建失败。"
                    >
                        <ui-text-box
                            value="{=biddingFormData.bidTimeout=}"
                            width="100"
                        />&nbsp;&nbsp;<span>分钟</span>
                    </ui-form-item>
                </ui-form>
                <div slot="image-info">
                    <div
                        s-if="imageDisabledByUbuntu24 && clusterUuid"
                        class="error-msg error-by-network-plugin"
                    >
                        当前集群CCE Network Plugin组件版本不支持，请前往<a
                            href="#/cce/ai/helm/release?clusterUuid={{clusterUuid}}&active=network"
                            target="_blank"
                        >
                            组件管理 </a
                        >升级CCE Network Plugin版本
                    </div>

                    <div s-if="imageType==='public'" class="image-info-tip">
                        若你需要挂载极速型L2并行文件存储（PFS），请选择CentOS或者Ubuntu操作系统，<a
                            href="https://cloud.baidu.com/doc/PFS/s/olzz6s5bi"
                            target="_blank"
                        >
                            详情说明</a
                        >。
                    </div>
                </div>
            </x-instance>
        </div>
        <x-bcc-cds
            s-if="instanceConfig"
            s-ref="cds"
            isCce="{{true}}"
            sdk="{{sdk}}"
            enableCfs="{{false}}"
            instance-config="{{instanceConfig}}"
            isSupport20GDisk="{{isSupport20GDisk}}"
            ebableDataDisk="{{isEnableDataDisk}}"
            on-cds-item-add="onCdsAdd"
            on-cds-item-delete="onCdsDelete"
            bcc-quota="{{bccQuota}}"
            prepareData="{{prepareData}}"
            osVersion="{{osVersionForCDS}}"
            isEncrypt="{{isEncrypt}}"
            initSysDiskInGB="100"
            autoSnaphotEnabled="{{!isNodeGroupCreate}}"
        >
            <div slot="cds-item-extra" class="cds-item-extra">
                <ui-check-box
                    title="指定挂载目录"
                    checked="{=cdsFormData.ebsSize[index].checked=}"
                    on-change="onCheckChange($event, index)"
                />
                <ui-text-box
                    width="120"
                    value="{=cdsFormData.ebsSize[index].path=}"
                    s-if="cdsFormData.ebsSize[index].checked"
                    on-input="onPathChange($event, index)"
                />
                <label
                    class="cce-custom-cds-bui-form-item-invalid-label"
                    s-if="cdsFormData.ebsSize[index].error"
                >
                    {{cdsFormData.ebsSize[index].error}}
                </label>
            </div>
        </x-bcc-cds>
        <template s-if="isNodeGroupCreate">
            <ui-form-item s-if="{{showHt}}" inline label="CPU线程配置：">
                <span slot="label">
                    <span>CPU线程配置：</span>
                    <s-tip class="login-config-tip" layer-width="{{235}}" content="{{htTip}}" />
                </span>
                <s-select
                    value="{=formData.cpuThreadConfig=}"
                    datasource="{{cpuThreadDatasource}}"
                    width="{{100}}"
                    track-id="ti_bcc_instance_create_enable_ht"
                    track-name="CPU线程配置"
                />
                <span style="margin-left: 8px" class="color-gray-light">{{openHtTip}}</span>
            </ui-form-item>
            <ui-form-item s-if="{{isShowNuma}}" inline label="NUMA配置：">
                <span slot="label">
                    <span>NUMA配置：</span>
                    <s-tip class="login-config-tip" layer-width="{{235}}" content="{{numaTip}}" />
                </span>
                <s-select value="{=formData.numaConfig=}" datasource="{{numaDatasource}}" />
            </ui-form-item>
        </template>

        <eip-config
            s-ref="eip"
            s-if="!$flag.CceClusterNoEip && !isNodeGroupCreate"
            style="display: {{!hideCommonItem ? 'block':'none'}}"
            productType="{{formData.productType}}"
        />

        <login-config
            s-if="!isNodeGroupCreate"
            s-ref="login"
            show-keypair="{{showKeypair}}"
            admin-name="{{adminName}}"
            hideCommonItem="{{hideCommonItem}}"
            instance-config="{{instanceConfig}}"
            show-domain-switch="{{showDomainSwitch}}"
        />

        <ui-form-item
            s-if="!isNodeGroupCreate"
            inline
            label-width="{{100}}"
            label="{{'数量：'}}"
            name="purchaseNum"
            class="purchaseNum-item"
            style="display: {{!hideCommonItem && !editData ? 'block':'none'}}"
        >
            <s-input-number
                value="{=formData.purchaseNum=}"
                step="1"
                step-strictly
                max="{{purchaseNumMax}}"
                min="{{purchaseNumMin}}"
            />
            <div
                class="purchaseNum-error-msg"
                s-if="{{formData.purchaseNum && selectedFlavor && selectedFlavor.xui__disabled}}"
            >
                所选实例规格已售罄，请选择其他实例规格或设置为0
            </div>
            <div class="purchaseNum-error-msg" s-elif="ehcLimitMessage">{{ehcLimitMessage}}</div>
            <div class="scale-info mt10" s-if="scaleLimitMessage">{{scaleLimitMessage}}</div>
        </ui-form-item>

        <deployset-config-panel
            style="display: {{!hideCommonItem ? 'block':'none'}}"
            s-ref="deploysetConfigPanel"
            logical-zone="{{formData.logicalZone}}"
            quota="{{bccQuota}}"
            type="bcc"
            deploySetID="{{deploySetID}}"
            s-if="!isNodeGroupCreate"
        />
    </ui-form>
</div> `;
/* eslint-enable */

@invokeBceSanUI
class AddCustom extends Component {
    static components = {
        'login-config': LoginConfig,
        'x-bcc-cds': BccStorageConfigPanel,
        'x-instance': instanceConfigLoader,
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-numbertext-line': NumberTextline,
        'eip-config': EipConfig,
        'pay-config': PayConfig,
        's-tip': Tip,
        's-select': Select,
        'ui-text-box': TextBox,
        'ui-loading': Loading,
        'ui-check-box': CheckBox,
        'deployset-config-panel': deploysetConfigPanel,
        'x-zone-subnets': ZoneSubnets,
        'ui-radio-select': RadioSelect,
        's-input-number': InputNumber,
        's-tip': Tip,
        'product-type-selector': ProductTypeSelector,
    };

    static template = template;

    static filters = {
        productTypeContent(value) {
            return ProductType.getTextFromValue(value);
        },
    };

    static computed = {
        showKeypair() {
            const osType = this.data.get('osType');
            return !$flag.CceClusterCreateNoKeypair && osType !== 'windows';
        },
        adminName() {
            const osType = this.data.get('osType');
            const os = cluster.OS[osType];
            return os ? os.ADMIN_NAME : 'root';
        },
        purchaseNumMin() {
            const purchaseNumMax = this.data.get('purchaseNumMax');
            const initPurchaseNumMin = this.data.get('initPurchaseNumMin');
            return purchaseNumMax > 1 ? initPurchaseNumMin ?? 1 : 0;
        },
        purchaseNumMax() {
            const defaultMax = this.data.get('quota.slave');
            const masterFlavor = this.data.get('masterFlavor');
            const existedNodeNum = this.data.get('existedNodeNum');
            let scaleMax = 0;
            if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                scaleMax = masterFlavorNum - existedNodeNum;
            }
            const nodeType = this.data.get('nodeType');
            // 已选择的已有节点
            const masterSelectedInstanceIds = this.data.get('masterSelectedInstanceIds') || [];
            // 自定义节点中的master节点
            const selectedCustomMaster = this.data.get('masterCustomInstances');
            let count = 0;
            _.each(selectedCustomMaster, v => {
                if (v && v.value) {
                    count += v.value.purchaseNum;
                }
            });
            const totalSelected = count + masterSelectedInstanceIds.length;
            if (nodeType === 'Master') {
                return (this.data.get('clusterHA') || 20) - totalSelected;
            }
            if (scaleMax) {
                const num = scaleMax - totalSelected;
                return num < 0 ? 0 : num;
            }
            return defaultMax;
        },
        ifBuyEip() {
            const clusterType = this.data.get('clusterType');
            if (clusterType === ClusterType.CLOUD_EDGE) {
                return '1';
            }

            return '0';
        },
        isBidding() {
            const curProductType = this.data.get('formData.productType');
            return curProductType === ProductType.BIDDING;
        },
        isARM() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.ARM;
        },
        armInstanceWhiteList() {
            // arm集群开启白名单
            return this.data.get('isARM');
        },
        isBcc() {
            return this.data.get('instanceNodeType') === NodeType.BCC;
        },
        isEbc() {
            return this.data.get('instanceNodeType') === NodeType.EBC;
        },
        resourceType() {
            return this.data.get('isBcc') ? 'bccResources' : 'ebcResources';
        },
        isEnableDataDisk() {
            const flavor = this.data.get('instanceConfig');
            return flavor && !isAbilityBanned([flavor], 'USE_CDS_DATA_VOLUME').disable;
        },
        showDomainSwitch() {
            const clusterType = this.data.get('clusterType');
            const isBcc = this.data.get('isBcc');

            return clusterType !== ClusterType.CLOUD_EDGE && isBcc;
        },

        // k8s 小于 1.24.4
        lessThan124() {
            const k8sVersion = this.data.get('k8sVersion');
            return !k8sVersion || compareVersion('1.24.4', k8sVersion);
        },
        scaleLimitMessage() {
            const masterFlavor = this.data.get('masterFlavor');
            const existedNodeNum = this.data.get('existedNodeNum');
            const ehcClusterCapacity = this.data.get('ehcClusterCapacity');
            if (masterFlavor && (existedNodeNum || existedNodeNum === 0)) {
                const masterFlavorNum = parseInt(masterFlavor?.replace('l', ''), 10);
                const num = masterFlavorNum - existedNodeNum;
                return `当前集群规模节点数量上限为${masterFlavorNum}，已有${existedNodeNum}个节点，当前最多可添加${
                    num < 0 ? 0 : num
                }个节点${
                    ehcClusterCapacity
                        ? `。当前选择弹性高性能计算集群实例数量上限为${ehcClusterCapacity}个`
                        : ''
                }`;
            }
            if (ehcClusterCapacity) {
                return `当前选择弹性高性能计算集群实例数量上限为${ehcClusterCapacity}个`;
            }
        },
        ehcLimitMessage() {
            const purchaseNum = this.data.get('formData.purchaseNum');
            const ehcClusterCapacity = this.data.get('ehcClusterCapacity');
            if (ehcClusterCapacity === 0 && purchaseNum) {
                return '所选ehc集群已售罄，请选择其他集群或设置为0';
            }
        },

        numaDatasource() {
            const instanceConfig = this.data.get('instanceConfig');

            const cpu = instanceConfig?.cpuModel?.split(' ')[0].toLowerCase() || '';
            if (!cpu) {
                return [];
            }
            const intelOptions = [
                {value: '1', label: '开启NUMA优化'},
                {value: '0', label: '关闭NUMA优化'},
            ];
            const amdOptions = [
                {value: '0', label: 'NPS0'},
                {value: '1', label: 'NPS1'},
                {value: '2', label: 'NPS2'},
                {value: '4', label: 'NPS4'},
                {value: 'auto', label: '自动'},
            ];
            return cpu === 'intel' ? intelOptions : amdOptions;
        },
    };

    static messages = {
        'ui:loadFlavorList'(e) {
            this.data.set('flavorListLoaded', true);

            this.dispatch('instance_list_loading', {
                disable: false,
            });

            // 修复编辑时 bcc sdk 不能正确设置 selectedFlavor 的问题
            if (this.data.get('editData') || this.data.get('fillData')) {
                e?.target.data.set('firstRender', true);
            }
        },
    };

    initData() {
        const sdk = getSdk('BCC', {
            getImageListOnCreate: payload => {
                // 获取bcc可用镜像，获取cce可用镜像，进行过滤，得到bcc和cce都可用镜像
                return Promise.all([
                    sdk.client.getImageListOnCreate(payload),
                    client.imageSupportedOs({}),
                    waitFor(() => this.data.get('flavorListLoaded') === true), // 等待flavorList加载完再展示镜像列表
                ]).then(results => {
                    const bccImages = results?.[0] || {};
                    const cceImages = results?.[1]?.result || [];
                    const cceImagesMap = {};
                    cceImages.forEach((image, index) => {
                        cceImagesMap[image.imageId] = index + 1; // 用于镜像过滤及排序
                    });
                    const filteredImages = (bccImages.images || [])
                        .filter(e => !!cceImagesMap[e.imageId])
                        .sort((a, b) => cceImagesMap[a.imageId] - cceImagesMap[b.imageId]);
                    const imageTypeSortList = [
                        'public',
                        'custom',
                        'subject_GPU',
                        'subject_NPU',
                        'sharing',
                        'internal',
                        'subject_FPGA',
                    ]; // 根据这个顺序进行类型排序
                    filteredImages.sort((a, b) => {
                        const aIndex =
                            imageTypeSortList.findIndex(e => e === a.categoryKey) + 1 ||
                            imageTypeSortList.length;
                        const bIndex =
                            imageTypeSortList.findIndex(e => e === b.categoryKey) + 1 ||
                            imageTypeSortList.length;
                        return aIndex - bIndex;
                    });
                    const filteredVisible = {};
                    const visible = bccImages.visible || {};
                    Object.keys(visible).forEach(key => {
                        const list = visible[key];
                        filteredVisible[key] = list.filter(id => !!cceImagesMap[id]);
                    });
                    return {images: filteredImages, visible: filteredVisible};
                });
            },
        });
        let unsupportClassify = [];
        if ($flag.CceClusterCreateNoGpuArch) {
            unsupportClassify = ['gpu', 'kunlun', 'fpga', 'vgpu'];
        }
        return {
            imageType: null,
            isEncrypt: checkIsKMSRegion(),
            formData: {
                productType: ProductType.POSTPAY,
                purchaseNum: 1,
                numaConfig: '1',
                cpuThreadConfig: '2',
            },
            sdk,
            osType: '',
            htTip: tips.instance.ht,
            openHtTip: tips.instance.openHtTip,
            numaTip: tips.instance.numaTip,
            flavorFilter: item => {
                // 这里只能隐藏flavor，不能再置灰加tip了，置灰需要到sdk里处理
                return !_.includes(UnsupportedInstanceTypes, item.instanceType + '');
            },
            specAvailableStatusMap,
            cdsFormData: {
                ebsSize: [],
            },
            $flag,
            productTypeList: [
                {
                    text: ProductType.getTextFromValue(ProductType.POSTPAY),
                    value: ProductType.POSTPAY,
                },
                {
                    text: ProductType.getTextFromValue(ProductType.PREPAY),
                    value: ProductType.PREPAY,
                },
            ],
            biddingFormData: {
                bidTimeout: 5,
            },
            selectedFlavor: null,
            biddingRules,
            unsupportClassify,
            isSupport20GDisk: $flag.CceClusterCreateIsSupport20GDisk ? false : true,
            editFramework: null, // 编辑时使用
            zoneLoading: true,
            flavorListLoaded: false,
            osVersionForCDS: {
                minDiskGb: 20, // bcc-sdk cds组件需要 默认值
            },
            imageTypeFilter: ({value}) => {
                if (this.data.get('isEbc')) {
                    // EBC 仅支持“公共镜像”、“自定义镜像”、“GPU镜像”、“共享镜像”、“内部镜像”、“NPU镜像”
                    const supportType = ['public', 'custom', 'subject_GPU', 'sharing', 'internal'];
                    const lessThan124 = this.data.get('lessThan124');
                    if (!lessThan124) {
                        // k8s 1.24及以上才支持npu
                        supportType.push('subject_NPU');
                    }
                    return supportType.includes(value);
                }
                // 去除“服务集成镜像”
                return value !== 'subject_integration';
            },
            imageOsFilter: imageOsData => {
                const isExclusiveEni = this.data.get('isExclusiveEni');
                const eBPFEnabled = this.data.get('eBPFEnabled');
                const imageType = this.ref('instance')
                    ?.ref('imageSelector')
                    ?.data?.get('form.imageType');
                let {osTypes} = imageOsData;
                if (imageType === 'public' || !imageType) {
                    // 开启eBPF增强后，添加节点选择公共镜像时，只允许选择Baidulinux 3.0
                    if (!isExclusiveEni && eBPFEnabled) {
                        osTypes = osTypes.filter(item =>
                            ['BaiduLinux', 'Ubuntu'].includes(item.value),
                        );
                    }
                }

                return {osTypes: osTypes};
            },
            osDataFilter: imageOsVersion => {
                const isExclusiveEni = this.data.get('isExclusiveEni');
                const eBPFEnabled = this.data.get('eBPFEnabled');
                const lessThan124 = this.data.get('lessThan124');
                if (imageOsVersion.categoryKey === 'public') {
                    // 开启eBPF增强后，添加节点选择公共镜像时，只允许选择Baidulinux 3.0
                    if (!isExclusiveEni && eBPFEnabled) {
                        if (imageOsVersion.osName === 'BaiduLinux') {
                            const allowedOsVersion = '3.0';
                            return imageOsVersion.osVersion.includes(allowedOsVersion);
                        } else if (imageOsVersion.osName === 'Ubuntu') {
                            const allowedOsVersion = '22.04 LTS';
                            return imageOsVersion.osVersion.includes(allowedOsVersion);
                        } else {
                            imageOsVersion.disabled = true;
                            imageOsVersion.tip = 'DataPath V2 不支持该操作系统';
                        }
                    }
                    if (lessThan124) {
                        // Ubuntu 22.04 只支持 k8s 1.24 及以上版本
                        if (imageOsVersion.osName === 'Ubuntu') {
                            return (
                                !imageOsVersion.osVersion.includes('22.04') &&
                                !imageOsVersion.osVersion.includes('24.04')
                            );
                        }
                    }
                }
                return true;
            },
            cpuThreadDatasource: [
                {value: '1', label: '1'},
                {value: '2', label: '2'},
            ],
        };
    }

    inited() {
        // 初始化付费类型
        const payConfig = this.data.get('payConfig');
        this.data.set('formData.productType', payConfig?.payType || ProductType.POSTPAY);

        const isNodeGroupCreate = this.data.get('isNodeGroupCreate');
        if (isNodeGroupCreate) {
            this.data.set('formData.purchaseNum', 0);
        }
        // 需要用的地方传进来的enableBidding，例如创建集群不支持，就没有这个值
        const enableBidding = this.data.get('enableBidding');
        enableBidding &&
            this.data.push('productTypeList', {
                text: ProductType.getTextFromValue(ProductType.BIDDING),
                value: ProductType.BIDDING,
            });

        if (!enableBidding && this.data.get('formData.productType') === ProductType.BIDDING) {
            this.data.set('formData.productType', ProductType.POSTPAY);
        }

        // 在bcc-sdk加载完成后，根据cce的业务逻辑处理sdk实例的data
        this.handleBccSdkCompData();

        // 编辑时回显
        this.handleEditData();

        // 节点组信息代入，节点组创建编辑 fillData
        this.handleFillData();

        this.dispatch('instance_list_loading', {
            disable: true,
            message: '数据加载中',
        });

        this.getNetworkPluginVersion();

        this.$childs = [];
        this.watch('instanceNodeType', val => {
            this.data.set('selectedFlavor', null);
        });
    }

    attached() {
        this.getBccPrice = _.debounce(this.getBccPrice, 500, {
            leading: false,
            trailing: true,
        });
        if (!this.data.get('isNodeGroupCreate')) {
            this.getQuota();
        }
        this.getBccQuota();

        this.watch('isBidding', value => {
            if (value) {
                this.getBccPrice();
            }
        });

        //备选机型隐藏一些字段，且机型必须和主机型一致
        const hideCommonItem = this.data.get('hideCommonItem');
        if (hideCommonItem) {
            const loginComp = this.ref('login');
            if (loginComp?.data?.set) {
                loginComp.data.set('formData.adminPassType', 'random');
            }
        }
    }

    handleEditData() {
        // 编辑信息回显
        const editData = this.data.get('editData');
        if (editData) {
            const {logicalZone, vpcSubnetId, bid, bidOption} = editData;
            // 付费类型
            this.handleProductType();
            this.watch('productTypeList', data => {
                if (!_.isEqual(data, this.data.get('productTypeList'))) {
                    this.handleProductType();
                }
            });
            // 可用区和子网
            if (logicalZone) {
                this.data.set('formData.logicalZone', logicalZone);
                this.data.set('formData.subnetId', vpcSubnetId);
            }
            // x-instance这个sdk用image字段来回显数据
            const {
                image,
                cdsData,
                needEIP,
                eipName,
                eipChargeType,
                eipBandwidth,
                eipPurchaseType,
                instanceName,
                sshKeyID,
                isOpenHostnameDomain,
                replicas,
                autoSnapshotID,
                deploySetID,
                framework,
                cpuThreadConfig,
                numaConfig,
                customImageValue,
                checkGPUDriver,
            } = editData;
            if (image) {
                this.data.set('bccPrepareData', {...image});
                this.data.set('initialInstanceData', {
                    ...image,
                    instanceType: image.instanceType,
                    spec: image.machineSpec,
                });
                this.data.set('initialImageType', image.imageType);
                this.data.set('imageType', image.imageType);
            }

            // 架构置灰 // TODO:危险代码
            // UnsupportedInstanceTypes有值后instance组件才resolve,UnsupportedInstanceTypes是通过接口拿的
            let getTime = 0; // 轮询10次，10次还没查到就不管了，爆炸
            const timer = setInterval(() => {
                if (UnsupportedInstanceTypes || getTime > 10) {
                    clearInterval(timer);
                    const bccComp = this.ref('instance');
                    if (bccComp) {
                        // 标志该组件已经回显完毕
                        // TODO:该组件的checkFlavorNeedReloadImage调用会导致amd类型cpu时疯狂请求镜像接口 ，先给干掉
                        // const tempFn = bccComp.checkFlavorNeedReloadImage;
                        bccComp.checkFlavorNeedReloadImage = () => {};

                        // 架构置灰(编辑节点组的时候，编辑主机型，不让改架构)
                        bccComp.watch('frameworkList', data => {
                            bccComp.nextTick(() => {
                                const {frameworkList} = bccComp.data.get();
                                bccComp.onFrameworkChange({value: framework});
                                if (!this.data.get('editFramework')) {
                                    this.data.set('editFramework', framework);
                                }
                                if (
                                    frameworkList?.length > 1 &&
                                    !frameworkList?.[0]?.hasOwnProperty('disabled')
                                ) {
                                    if (this.data.get('editFramework')) {
                                        const target = frameworkList.find(
                                            e => e.value === this.data.get('editFramework'),
                                        );
                                        if (target) {
                                            frameworkList.forEach(item => {
                                                item.disabled =
                                                    item.value !== this.data.get('editFramework');
                                            });
                                            bccComp.data.set(
                                                'frameworkList',
                                                _.cloneDeep(frameworkList),
                                            );
                                        }
                                    }
                                }
                            });
                        });

                        // framework = x86 需要选中cpu
                        bccComp.watch('cpuModelList', data => {
                            if (data?.length > 1) {
                                // 说明存在其他类型cpu
                                const {machineSpec} = image;
                                bccComp.nextTick(() => {
                                    // 从当前区下所有机型中到这个机型，然后看cpu是啥类型，在选中cpuModel
                                    const framework =
                                        this.data.get('editFramework') ||
                                        bccComp.data.get('framework');
                                    if (framework === 'x86') {
                                        const resource = bccComp.data.get('resource');
                                        const list = resource?.bccResources?.flavorGroups || [];
                                        list?.forEach(item => {
                                            (item.flavors || []).forEach(flavor => {
                                                if (flavor.spec === machineSpec) {
                                                    bccComp.updateFlavorIndex(machineSpec);
                                                    bccComp.data.set('firstRender', true);
                                                    bccComp.updateFlavorList();
                                                }
                                            });
                                        });
                                    }
                                });
                            }
                        });

                        // 可用区置灰
                        let editInitFramework;
                        let editInitFrameworkList;
                        let watchTime = 1;
                        bccComp.watch('frameworkList', data => {
                            bccComp.nextTick(() => {
                                const {frameworkList, rawZoneList} = bccComp.data.get();
                                const framework =
                                    this.data.get('editFramework') || bccComp.data.get('framework');
                                const disabledZone = [];
                                if (rawZoneList && framework) {
                                    const instanceNodeType =
                                        editData.instanceNodeType === 'EBC' ? 'ebc' : 'bcc';
                                    const resourceskey = {
                                        bcc: 'bccResources',
                                        ebc: 'ebcResources',
                                    }[instanceNodeType];
                                    rawZoneList.forEach(item => {
                                        if (item?.[resourceskey]?.flavorGroups) {
                                            const target = item[resourceskey].flavorGroups.find(
                                                e => e.arch === framework,
                                            );
                                            if (!target) {
                                                // 在这个区域找不着详情这种架构，则这个区要置灰
                                                item.logicalZone &&
                                                    disabledZone.push(item.logicalZone);
                                            }
                                        } else {
                                            item.logicalZone && disabledZone.push(item.logicalZone);
                                        }
                                    });
                                }
                                const zoneSubnets = this.ref('zoneSubnets');
                                if (disabledZone.length && zoneSubnets) {
                                    const list =
                                        this.ref('zoneSubnets').data.get('logicalZone.list');
                                    list?.forEach(item => {
                                        // 如果是false的时候，再置灰，否则组件内的置灰逻辑会失效
                                        !item.disabled &&
                                            (item.disabled = disabledZone.includes(item.value));
                                    });
                                    zoneSubnets.data.set('logicalZone.list', _.cloneDeep(list));
                                }

                                // 可用区切换的时候，架构不能变
                                if (watchTime === 1) {
                                    editInitFramework = framework;
                                    editInitFrameworkList = frameworkList;
                                    this.watch('formData.logicalZone', data => {
                                        bccComp.data.set(
                                            'frameworkList',
                                            _.cloneDeep(editInitFrameworkList),
                                        );
                                        bccComp.data.set('framework', editInitFramework);
                                        bccComp.data.set('firstRender', true);
                                    });
                                }
                                watchTime++;
                            });
                        });

                        //masterNodeInitSpec : 初始配置，架构不能变，备选机型不能改，配置按这个过滤
                        const masterNodeInitSpec = this.data.get('masterNodeInitSpec');
                        const hideCommonItem = this.data.get('hideCommonItem');
                        const debounceFilterHandler = _.debounce(data => {
                            if (data?.length) {
                                if (hideCommonItem) {
                                    const cpusDatasource = bccComp.data.get('cpusDatasource');
                                    const memorysDatasource = bccComp.data.get('memorysDatasource');

                                    bccComp.data.set(
                                        'cpusDatasource',
                                        cpusDatasource.filter(
                                            e => e.value === masterNodeInitSpec.cpu,
                                        ),
                                    );
                                    bccComp.data.set(
                                        'memorysDatasource',
                                        memorysDatasource.filter(
                                            e => e.value === masterNodeInitSpec.memory,
                                        ),
                                    );
                                    bccComp.data.set('filter.cpu', masterNodeInitSpec.cpu);
                                    bccComp.data.set('filter.memory', masterNodeInitSpec.memory);
                                    bccComp.filterInstance();
                                    bccComp.nextTick(() => {
                                        //TODO:上面的filter筛选会改变tableDatasource的值，是异步的
                                        // TODO:nextTick不能获得最新的,延迟获取,待优化
                                        setTimeout(() => {
                                            const list = bccComp.data.get('tableDatasource');
                                            const machineSpec = editData?.image?.machineSpec;
                                            if (machineSpec) {
                                                const item = list.find(
                                                    e => e.spec === editData.image.machineSpec,
                                                );
                                                bccComp.handleFlavorChange(item || list[0]);
                                            } else if (list?.[0]?.spec) {
                                                bccComp.handleFlavorChange(list[0]);
                                            }
                                        }, 50);
                                    });
                                }
                            }
                        }, 100);

                        // 表格内内容变化，过滤机型及架构
                        debounceFilterHandler(bccComp.data.get('tableDatasource'));
                        bccComp.watch('tableDatasource', data => {
                            if (
                                !data.every(
                                    e =>
                                        e.cpuCount === masterNodeInitSpec.cpu &&
                                        e.memoryCapacityInGB === masterNodeInitSpec.memory,
                                )
                            ) {
                                debounceFilterHandler(data);
                            }
                        });

                        // 镜像，添加备选机型的时候，没有镜像类型的话，gpu机型默认选择gpu镜像
                        // const debounceImageHandler = _.debounce(value => {
                        //     bccComp.data.set('formData.imageType', value);
                        //     bccComp.onImageTypeChange({value});
                        //     const osTypeList = bccComp.data.get('osTypeList');
                        //     if (osTypeList.length) {
                        //         bccComp.data.set('formData.osType', osTypeList[0].value);
                        //         bccComp.onOsTypeChange({value: osTypeList[0].value});
                        //     }
                        // }, 200);
                        // hideCommonItem &&
                        //     bccComp.watch('imageTypeList', data => {
                        //         const editFramework = this.data.get('editFramework');
                        //         const editImage = this.data.get('editData.image');
                        //         const gpuImageType = data?.find(e => e.value === 'gpuBccImage');
                        //         // 添加备选机型
                        //         if (
                        //             editFramework &&
                        //             editImage &&
                        //             !editImage?.imageType &&
                        //             gpuImageType
                        //         ) {
                        //             if (editFramework === 'isomerism') {
                        //                 // gpu机型默认选中GPU镜像
                        //                 debounceImageHandler(gpuImageType.value);
                        //             }
                        //         }
                        //     });

                        //抢占实例info
                        if (bid && bidOption) {
                            let echoFlag = false; // 是否已经回显过了
                            const debounceBidHandler = _.debounce(() => {
                                if (!echoFlag) {
                                    const price =
                                        bidOption.bidPrice && bidOption.bidPrice !== '0'
                                            ? moneyUtil
                                                  .convertPrice(bidOption.bidPrice)
                                                  .getPriceOfHour()
                                                  .toFixed(3)
                                            : 0;
                                    bccComp.data.set('formData.bidModel', bidOption.bidMode);
                                    bccComp.data.set('formData.defaultBidPrice', price);
                                    this.data.set(
                                        'biddingFormData.bidTimeout',
                                        bidOption.bidTimeout,
                                    );
                                    echoFlag = true;
                                }
                            }, 1000);
                            bccComp.watch('formData.bidModel', () => {
                                debounceBidHandler();
                            });
                            debounceBidHandler();
                        }

                        if (customImageValue) {
                            // @ts-ignore
                            bccComp.setCustomGpuDefaultValue(customImageValue);
                            bccComp.setCheckGpuDefaultValue(checkGPUDriver);
                        }
                    }
                }
                getTime++;
            }, 1000);

            // 磁盘数据 x-cds 回显
            if (cdsData) {
                if (autoSnapshotID) {
                    cdsData.aspId = autoSnapshotID;
                }
                this.data.set('prepareData', cdsData);
                if (cdsData.cdsDiskData?.length) {
                    this.nextTick(() => {
                        cdsData.cdsDiskData.forEach((item, index) => {
                            this.onCheckChange({value: !!item.path, path: item.path}, index);
                        });
                    });
                }
                // instanceConfig为选中的机型，当该值有值时cds组件才会渲染, 渲染后调用cds组件的初始化磁盘方法
                let cdsInited = false;
                this.watch('instanceConfig', () => {
                    !cdsInited &&
                        this.nextTick(() => {
                            const cds = this.ref('cds');
                            if (cds) {
                                cds.prepareDisk();
                                cdsInited = true;
                            }
                        });
                });
            }

            // 公网IP
            if (needEIP) {
                this.nextTick(() => {
                    const eipRef = this.ref('eip');
                    if (eipRef) {
                        eipRef.data.set('formData', {
                            ifBuyEip: needEIP ? '1' : '0',
                            bandwidthInMbps: eipBandwidth,
                            eipName: eipName,
                            eipPurchaseType: eipPurchaseType,
                        });
                        const transMap = {
                            ByTraffic: 'netraffic',
                            ByBandwidth: 'bandwidth',
                        };
                        eipRef.data.set('subProductType', transMap[eipChargeType] || '');
                        eipRef.nextTick(() => {
                            eipRef.data.set('formData.bandwidthInMbps', eipBandwidth);
                        });
                    }
                });
            } else {
                this.nextTick(() => {
                    const eipRef = this.ref('eip');
                    if (eipRef) {
                        eipRef.data.set('formData.ifBuyEip', '0');
                    }
                });
            }

            this.nextTick(() => {
                // 实例名称回显
                const loginComp = this.ref('login');
                if (loginComp?.data?.set) {
                    loginComp.data.set('formData.isOpenHostnameDomain', isOpenHostnameDomain);

                    if (instanceName) {
                        loginComp.data.merge('formData', {name: instanceName, nameType: 'custom'});
                    }

                    if (sshKeyID) {
                        loginComp.data.merge('formData', {
                            adminPassType: 'keypair',
                            keypairId: sshKeyID,
                        });
                    }
                    if (cpuThreadConfig || numaConfig) {
                        loginComp.data.merge('formData', {
                            cpuThreadConfig: cpuThreadConfig,
                            numaConfig: numaConfig,
                        });
                    }
                }
                // 部署集
                if (deploySetID) {
                    this.data.set('deploySetID', deploySetID);
                }
            });
            // 节点数量
            if (replicas || replicas === 0) {
                this.data.set('formData.purchaseNum', replicas);
            }
        }
    }

    handleFillData() {
        const {fillData} = this.data.get();
        if (fillData) {
            const {
                productType,
                bid,
                bidOption,
                logicalZone,
                vpcSubnetId,
                framework,
                image,
                customImageValue,
                checkGPUDriver,
                cdsData,
                eip,
                isOpenHostnameDomain,
                name,
                sshKeyID,
                replicas,
                deploySetID,
                cpuThreadConfig,
                numaConfig,
            } = fillData;
            const {instancePreChargingOption} = fillData;
            // 付费方式
            this.data.set('formData.productType', productType);
            this.nextTick(() => {
                if (
                    productType === 'prepay' &&
                    instancePreChargingOption &&
                    this.data.get('showPayType')
                ) {
                    const payConfigRef = this.ref('payConfig');
                    const {
                        purchaseTime = 1,
                        autoRenew = false,
                        autoRenewTime = 1,
                        autoRenewTimeUnit = 'month',
                    } = instancePreChargingOption;

                    if (payConfigRef) {
                        const data = {
                            payType: 'prepay',
                            duration: purchaseTime,
                            autoRepay: autoRenew,
                            repayCycle:
                                autoRenewTimeUnit === 'month' ? autoRenewTime : autoRenewTime * 12,
                            repayCycleType: autoRenewTimeUnit.toUpperCase(),
                        };
                        payConfigRef.data.set('formData', data);
                    }
                }
            });
            // 可用区
            if (logicalZone) {
                this.data.set('formData.logicalZone', logicalZone);
            }
            //子网
            if (vpcSubnetId) {
                this.data.set('formData.subnetId', vpcSubnetId);
            }
            // 回显机型&镜像
            if (image) {
                this.data.set('bccPrepareData', {...image});
                this.data.set('initialInstanceData', {
                    ...image,
                    instanceType: image.instanceType,
                    spec: image.machineSpec,
                });
                this.data.set('initialImageType', image.imageType);
                this.data.set('imageType', image.imageType);
            }

            this.nextTick(() => {
                //磁盘
                if (cdsData) {
                    this.data.set('prepareData', cdsData);
                    if (cdsData.cdsDiskData?.length) {
                        this.nextTick(() => {
                            cdsData.cdsDiskData.forEach((item, index) => {
                                this.onCheckChange({value: !!item.path, path: item.path}, index);
                            });
                        });
                    }
                    let cdsInited = false;
                    this.watch('instanceConfig', () => {
                        !cdsInited &&
                            this.nextTick(() => {
                                const cds = this.ref('cds');
                                if (cds) {
                                    cds.prepareDisk();
                                    cdsInited = true;
                                }
                            });
                    });
                }
                // 公网IP
                if (eip?.ifBuyEip === '1') {
                    const eipRef = this.ref('eip');
                    if (eipRef) {
                        eipRef.data.set('formData', {
                            ifBuyEip: eip.ifBuyEip,
                            bandwidthInMbps: eip.bandwidthInMbps,
                            eipName: eip.eipName,
                            eipPurchaseType: eip.eipPurchaseType,
                        });
                        eipRef.data.set('subProductType', eip.subProductType);
                        eipRef.nextTick(() => {
                            eipRef.data.set('formData.bandwidthInMbps', eip.bandwidthInMbps);
                        });
                    }
                }

                this.nextTick(() => {
                    // 实例名称回显
                    const loginComp = this.ref('login');
                    if (loginComp?.data?.set) {
                        loginComp.data.set('formData.isOpenHostnameDomain', isOpenHostnameDomain);
                        if (name) {
                            loginComp.data.merge('formData', {
                                name,
                                nameType: 'custom',
                            });
                        }
                        if (sshKeyID) {
                            loginComp.data.merge('formData', {
                                adminPassType: 'keypair',
                                keypairId: sshKeyID,
                            });
                        }
                        if (cpuThreadConfig || numaConfig) {
                            loginComp.data.merge('formData', {
                                cpuThreadConfig: cpuThreadConfig,
                                numaConfig: numaConfig,
                            });
                        }
                    }
                    // 部署集
                    if (deploySetID) {
                        this.data.set('deploySetID', deploySetID);
                    }
                });
            });

            // 节点数量
            // 暂时不复制节点数
            // if (replicas || replicas === 0) {
            //     this.data.set('formData.purchaseNum', replicas);
            // }

            // 设置bcc-sdk组件中的data
            let getTime = 0;
            const timer = setInterval(() => {
                if (UnsupportedInstanceTypes || getTime > 10) {
                    clearInterval(timer);
                    const bccComp = this.ref('instance');
                    if (bccComp) {
                        //抢占实例info
                        if (bid && bidOption) {
                            const firstRender = bccComp.data.get('firstRender'); // 是否已经回显过了
                            const debounceBidHandler = _.debounce(() => {
                                if (firstRender) {
                                    const bidMode = bidOption.bidMode;
                                    const price =
                                        bidOption.bidPrice && bidOption.bidPrice !== '0'
                                            ? moneyUtil
                                                  .convertPrice(bidOption.bidPrice)
                                                  .getPriceOfHour()
                                                  .toFixed(3)
                                            : 0;
                                    bccComp.data.set('formData.bidModel', bidMode);
                                    bidMode == 'CUSTOM_BID' &&
                                        bccComp.data.set('formData.defaultBidPrice', price);
                                    this.data.set(
                                        'biddingFormData.bidTimeout',
                                        bidOption.bidTimeout,
                                    );
                                }
                            }, 1000);
                            debounceBidHandler();
                            bccComp.watch('firstRender', () => {
                                debounceBidHandler();
                            });
                            debounceBidHandler();
                        }

                        // 设置GPU驱动
                        if (customImageValue) {
                            // @ts-ignore
                            bccComp.setCustomGpuDefaultValue(customImageValue);
                            bccComp.setCheckGpuDefaultValue(checkGPUDriver);
                        }
                    }
                }
                getTime++;
            }, 1000);
        }
    }

    handleBccSdkCompData() {
        let getTime = 0; // 轮询10次，10次还没查到就不管了，爆炸
        const timer = setInterval(() => {
            if (UnsupportedInstanceTypes || getTime > 10) {
                clearInterval(timer);
                this.handleStyleForBidding();
                const bccComp = this.ref('instance');
                if (bccComp) {
                    // arm架构相关
                    {
                        const isARM = this.data.get('isARM');
                        if (isARM) {
                            bccComp.watch('frameworkList', list => {
                                if (list?.[0] && !list[0].hasOwnProperty('disabled')) {
                                    bccComp.nextTick(() => {
                                        bccComp.data.set('framework', 'arm');
                                        bccComp.data.set(
                                            'frameworkList',
                                            list.map(e => ({
                                                ...e,
                                                disabled: e.value !== 'arm',
                                            })),
                                        );
                                        bccComp.updateClassifyList();
                                        bccComp.updateFlavorList();
                                    });
                                }
                            });
                        }
                        // 创建
                        const debounceFrameworkHandler = _.debounce((data, framework) => {
                            if (framework) {
                                bccComp.nextTick(() => {
                                    const list = data.filter(e => e.framework === framework);
                                    if (!_.isEqual(data, list)) {
                                        bccComp.onFrameworkChange({value: framework});
                                        bccComp.data.set('tableDatasource', list);
                                    }
                                });
                            }
                        }, 100);
                        // ARM集群 或者 编辑
                        if (isARM || this.data.get('editData')) {
                            bccComp.watch('tableDatasource', data => {
                                const framework = isARM
                                    ? 'arm'
                                    : this.data.get('editFramework') ||
                                      bccComp.data.get('framework');
                                debounceFrameworkHandler(data, framework);
                            });
                        }
                    }
                    // 镜像相关
                    {
                        const osTypeSortList = ['BaiduLinux', 'CentOS', 'Ubuntu', 'Rocky Linux'];
                        bccComp.watch('osTypeList', data => {
                            if (data?.length > 1) {
                                const sortList = _.cloneDeep(data).sort((a, b) => {
                                    const aIndex = osTypeSortList.findIndex(e => e === a.value) + 1;
                                    const bIndex = osTypeSortList.findIndex(e => e === b.value) + 1;
                                    return aIndex - bIndex;
                                });
                                if (!_.isEqual(sortList, data)) {
                                    bccComp.data.set('osTypeList', sortList);
                                    bccComp.onOsTypeChange({value: sortList[0]?.value});
                                }
                            }
                        });
                    }
                    // 默认勾选gpu驱动，注意编辑的时候如果没有userData字段，需要初始化取消勾选
                    {
                        const debounceHandler = _.debounce(() => {
                            const framework = bccComp.data.get('framework');
                            const imageType = bccComp.data.get('formData.imageType');
                            if (
                                framework === 'isomerism' &&
                                ['public', 'common'].includes(imageType)
                            ) {
                                setTimeout(() => {
                                    try {
                                        const gpuCustomDriverRef = bccComp.ref('gpuCustomDriver');
                                        if (gpuCustomDriverRef) {
                                            if (
                                                !(
                                                    this.data.get('editData') ||
                                                    this.data.get('fillData')
                                                )
                                            ) {
                                                // 创建节点默认勾选,编辑看编辑数据是否勾选
                                                gpuCustomDriverRef.data.set('useGpuDriver', true);
                                            }
                                        }
                                    } catch (error) {
                                        console.error('isGupDrive勾选 error', error);
                                    }
                                }, 500);
                            }
                        }, 100);
                        bccComp.watch('framework', () => {
                            bccComp.nextTick(() => debounceHandler());
                        });
                        bccComp.watch('formData.osVersion', () => {
                            bccComp.nextTick(() => debounceHandler());
                        });
                        bccComp.watch('formData.imageType', imageType => {
                            this.data.set('imageType', imageType);
                        });
                        this.watch('selectedFlavor', () => {
                            bccComp.nextTick(() => debounceHandler());
                        });
                    }

                    // ebc机型
                    {
                        bccComp.onResourceTypeChange({value: this.data.get('resourceType')});
                        this.watch('resourceType', resourceType => {
                            bccComp.onResourceTypeChange({value: resourceType});
                        });
                    }
                }
            }
            getTime++;
        }, 1000);
    }

    checkInternalUser() {
        return this.$http.checkInternalUser().then(data => data);
    }

    getBccQuota() {
        const sdk = this.data.get('sdk');
        return sdk.getQuota({objType: 'bcc'}).then(data => this.data.set('bccQuota', data));
    }

    getQuota() {
        return this.$http
            .getClusterQuota()
            .then(response => this.data.set('quota', response))
            .catch(() =>
                this.data.set('quota', {
                    cluster: 20,
                    slave: 200,
                }),
            );
    }

    async getNetworkPluginVersion() {
        try {
            const {clusterUuid} = this.data.get();
            if (!clusterUuid) {
                return;
            }
            const data = await this.$http.getCompsData(
                clusterUuid,
                'cce-vpc-native-cni,cce-network-v2',
            );
            if (data?.result?.items?.length === 2) {
                const networkV1 = data.result.items.find(
                    e => e.meta?.name === 'cce-vpc-native-cni',
                );
                const networkV2 = data.result.items.find(e => e.meta?.name === 'cce-network-v2');
                if (networkV2?.instance?.installedVersion) {
                    if (!compareVersion('2.12.18', networkV2?.instance?.installedVersion)) {
                        // 大于等于2.12.18
                        this.data.set('networkPluginBiggerThan2_12_18', true);
                    }
                } else if (networkV1?.instance?.installedVersion) {
                    if (!compareVersion('2.12.18', networkV1?.instance?.installedVersion)) {
                        // 大于等于2.12.18
                        this.data.set('networkPluginBiggerThan2_12_18', true);
                    }
                }
            }
        } catch (error) {
        } finally {
            const instance = this.ref('instance');
            if (instance) {
                const instanceData = instance.getConfig();
                const networkPluginBiggerThan2_12_18 = this.data.get(
                    'networkPluginBiggerThan2_12_18',
                );
                if (
                    this.data.get('currentImageType') === 'common' &&
                    instanceData?.osName === 'Ubuntu' &&
                    instanceData?.osVersion?.startsWith('24.') &&
                    !networkPluginBiggerThan2_12_18
                ) {
                    this.data.set('imageDisabledByUbuntu24', true);
                    this.addImageSelectErrorClass(true);
                } else {
                    this.data.set('imageDisabledByUbuntu24', false);
                    this.addImageSelectErrorClass(false);
                }
            }
        }
    }

    addImageSelectErrorClass(isErr) {
        this.nextTick(() => {
            const node = document.querySelector('#osVersion.os-version-select');
            if (node) {
                if (isErr) {
                    node.className = node.className + ' error-msg';
                } else {
                    node.className = node.className?.replaceAll('error-msg', '');
                }
            }
        });
    }

    onZoneChange(data) {
        const instanceRef = this.ref('instance');
        if (UnsupportedInstanceTypes && instanceRef) {
            setTimeout(() => {
                if (instanceRef.data.get('instance')) {
                    instanceRef.data.set('firstRender', false);
                    instanceRef.updateFlavorList();
                }
            }, 100);
        }
    }

    onInstanceChange(e) {
        const supportBuySoldout = this.data.get('supportBuySoldout');
        const clusterUuid = this.data.get('clusterUuid');
        const networkPluginBiggerThan2_12_18 = this.data.get('networkPluginBiggerThan2_12_18');
        e.osVersion && this.data.set('currentImageType', e.osVersion.imageType);
        if (e.osVersion && clusterUuid) {
            if (
                this.data.get('currentImageType') === 'common' &&
                e.osVersion.osName === 'Ubuntu' &&
                e.osVersion.osVersion?.startsWith('24.') &&
                !networkPluginBiggerThan2_12_18
            ) {
                this.data.set('imageDisabledByUbuntu24', true);
                this.addImageSelectErrorClass(true);
            } else {
                this.data.set('imageDisabledByUbuntu24', false);
                this.addImageSelectErrorClass(false);
            }
        }
        if (e.osType) {
            this.data.set('osType', e.osType);
        }
        if (e.config) {
            let instanceConfig = {
                ...e.config,
            };
            if (e.config.ephemeralDiskConfig) {
                instanceConfig.ephemeralDiskConfig = (e.config.ephemeralDiskConfig || []).filter(
                    // 这种类型的盘iaas不建议使用过滤掉
                    v => !(v.type === 'LOCAL_PV_SSD' && v.capacityInGb === 480),
                );
            }
            this.data.set('instanceConfig', instanceConfig);
            this.getIsShowNuma();

            this.nextTick(() => {
                try {
                    // 机型切换后重新初始化磁盘类型，解决cds-sdk bug
                    const cds = this.ref('cds');
                    cds?.prepareDisk();
                } catch (error) {}
            });
        }
        const instance = this.ref('instance');
        if (instance) {
            const instanceData = instance.getConfig();
            const editData = this.data.get('editData');
            if (
                instanceData?.selectedFlavor &&
                (supportBuySoldout || !instanceData.selectedFlavor.xui__disabled)
            ) {
                // 售罄的需要数量改成0;只有节点组主机型支持选择售罄的；编辑不需要调整数量
                if (instanceData.selectedFlavor.xui__disabled && !editData) {
                    this.data.set('formData.purchaseNum', 0);
                }
                this.dispatch('instance_flavor_disabled', {
                    disable: false,
                });
                this.data.set('selectedFlavor', instanceData.selectedFlavor);
                if (!instanceData.osVersion) {
                    this.dispatch('instance_flavor_disabled', {
                        disable: true,
                        message: '请选择镜像',
                    });
                } else {
                    this.dispatch('instance_flavor_disabled', {
                        disable: false,
                    });
                }
                if (instanceData.ehcClusterId) {
                    this.data.set('ehcClusterCapacity', instanceData.ehcClusterCapacity);
                } else {
                    this.data.set('ehcClusterCapacity', null);
                }
            } else {
                this.dispatch('instance_flavor_disabled', {
                    disable: true,
                    message: '请选择实例',
                });
                this.data.set('selectedFlavor', null);
            }
        }

        // 只有抢占实例才询价
        if (this.data.get('isBidding')) {
            const priceReq = [
                'flavor',
                'cpuCustom',
                'memoryCustom',
                'withoutFPGACheck',
                'mktImage',
                'osType',
            ];
            _.each(priceReq, item => {
                if (e.hasOwnProperty(item)) {
                    this.getBccPrice();
                }
            });
        }

        if (e.osVersion) {
            this.data.set('osVersionForCDS', e.osVersion);
        }
    }

    checkCdsPath() {
        const result = {};
        const ebsSize = this.data.get('cdsFormData.ebsSize');
        const cdsData = this.ref('cds')?.getConfig();
        if (cdsData?.ephemeralDisks?.length) {
            cdsData.ephemeralDisks.forEach(item => {
                result[item.diskPath] = item.diskPath;
            });
        }
        let flag = true;
        _.each(ebsSize, (item, i) => {
            this.data.set(`cdsFormData.ebsSize[${i}].error`, null);

            if (!result[item.path]) {
                result[item.path] = item.path;
            } else {
                flag = false;
                this.data.set(`cdsFormData.ebsSize[${i}].error`, '挂载目录不能重复');
                console.error('挂载目录不能重复');
                this.nextTick(() =>
                    this.focusTo('.cce-custom-cds-bui-form-item-invalid-label', 'center'),
                );
            }
        });

        return flag;
    }

    async validateForm() {
        const form = this.ref('zoneSubnets');
        const instance = this.ref('instance');
        const instanceData = instance ? instance.getConfig() : {};
        const cds = this.ref('cds');
        const eip = this.ref('eip');
        const login = this.ref('login');
        const biddingForm = this.ref('biddingForm');
        await new Promise((resolve, reject) => {
            if (instanceData.selectedFlavor) {
                if (instance.valid()) {
                    resolve();
                } else {
                    reject();
                }
                return;
            }
            if (!instanceData.selectedFlavor) {
                this.dispatch('instance_flavor_disabled', {
                    disable: true,
                    message: '请选择实例',
                });
            }
            if (!instanceData.osVersion) {
                this.dispatch('instance_flavor_disabled', {
                    disable: true,
                    message: '请选择镜像',
                });
            }
            console.error('请选择实例');
            this.nextTick(() => this.focusTo('.instance-config-panel .error-msg', 'center'));
            reject();
        });
        if (this.data.get('imageDisabledByUbuntu24')) {
            this.nextTick(() => this.focusTo('.instance-config-panel .error-msg', 'center'));
            return Promise.reject('请选择镜像');
        }
        return Promise.all([
            form.validateForm(),
            new Promise((resolve, reject) => {
                if (!cds.valid()) {
                    console.error('cds error');
                    this.nextTick(() => this.focusTo('.ephemeral-config', 'center'));
                    reject();
                    return;
                }
                if (!this.checkCdsPath()) {
                    reject();
                    return;
                }
                resolve();
            }),
            new Promise((resolve, reject) => {
                if (
                    this.data.get('formData.purchaseNum') !== 0 &&
                    !this.data.get('editData') &&
                    (instanceData.selectedFlavor?.xui__disabled || this.data.get('ehcLimitMessage'))
                ) {
                    console.error('purchaseNum error');
                    this.nextTick(() => this.focusTo('.purchaseNum-item', 'center'));
                    reject();
                } else {
                    resolve();
                }
            }),
            biddingForm ? biddingForm.validateForm() : Promise.resolve(),
            eip ? eip.validateForm() : Promise.resolve(),
            login ? login.validateForm() : Promise.resolve(),
            this.ref('deploysetConfigPanel')
                ? this.ref('deploysetConfigPanel').valid()
                : Promise.resolve(),
        ]);
    }

    getDriverData(customVersion) {
        if (customVersion?.length === 3) {
            const version =
                '#!/bin/bash\n' +
                `DRIVER_VERSION="${customVersion[1]}"\n` +
                `CUDA_VERSION="${customVersion[0]}"\n` +
                `CUDNN_VERSION="${customVersion[2]}"\n` +
                'WORK_DIR="/root/auto_install"\n' +
                'SCRIPT_URL="http://mirrors.baidubce.com/nvidia-binary-driver/api/auto_install.sh"\n';
            const content = `mkdir \${WORK_DIR}
pushd \${WORK_DIR}
for ((i=0; i<120; i++))
do
    wget --timeout=10 -t 10 \${SCRIPT_URL}
    if [ $? -eq 0 ]; then
        break
    else
        sleep 1
    fi
done
bash \${WORK_DIR}/$(basename \${SCRIPT_URL}) \${DRIVER_VERSION} \${CUDA_VERSION} \${CUDNN_VERSION}
popd
rm -rf \${WORK_DIR}

cmdline=$(cat /proc/cmdline)
if [[ "\${cmdline}" =~ "pci=realloc" ]]; then
    echo "remove 'pci=realloc' cmdline arg and update grub"
    default_grub_arg="/etc/default/grub"
    sed -i 's/pci=realloc//g' \${default_grub_arg}
    if command -v grub2-mkconfig; then
        efi_grub_cfg=/boot/efi/EFI/centos/grub.cfg
        if [ -f /boot/efi/EFI/rocky/grub.cfg ]; then
            efi_grub_cfg=/boot/efi/EFI/rocky/grub.cfg
        fi
        grub2-mkconfig -o $efi_grub_cfg
    fi
    if command -v update-grub; then
        update-grub
    fi
    reboot
else
    echo "there is no 'pci=realloc' arg in current cmdline, do nothing"
fi`;
            return btoa(version + content);
        } else {
            return null;
        }
    }

    getFormData() {
        const formData = this.data.get('formData');
        const instance = this.ref('instance');
        const cds = this.ref('cds');
        if (!instance || !cds) {
            return;
        }

        const instanceData = instance ? instance.getConfig() : {};

        if (instanceData?.customImageValue) {
            instanceData.userData = this.getDriverData(instanceData.customImageValue);
        }

        const zoneSubnetsData = this.ref('zoneSubnets')
            ? this.ref('zoneSubnets').getFormData()
            : {};
        instanceData.logicalZone = zoneSubnetsData?.logicalZone;

        const cdsData = cds ? cds.getConfig() : null;

        if (cdsData) {
            const ebsSize = this.data.get('cdsFormData.ebsSize');
            _.map(cdsData.ebsSize, (item, i) => {
                if (ebsSize[i]) {
                    item.path = ebsSize[i].path || '';
                }
            });
        }

        const eipData = this.ref('eip')?.getFormData() || {};
        const loginData = this.ref('login')?.getFormData() || {};
        if (loginData.nameType === 'random') {
            loginData.name = '';
        }
        const deploySetData = this.ref('deploysetConfigPanel')
            ? this.ref('deploysetConfigPanel').getComponentData()
            : {};
        const {deploySetID, deploySetName} = deploySetData;

        const biddingForm = this.ref('biddingForm');
        const bidOption = {};

        if (biddingForm) {
            const bidFormData = biddingForm.getFormData();
            bidOption.bidTimeout = +bidFormData.bidTimeout;
            bidOption.bidMode = instanceData.bidModel;
            // 自动出价
            if (instanceData.bidModel === 'MARKET_PRICE_BID') {
                bidOption.bidPrice = '0';
            } else {
                bidOption.bidPrice = instanceData.bidPrice + '';
            }
        }
        const productType = formData.productType;
        const instanceChargingType = productType === 'prepay' ? 'Prepaid' : 'Postpaid';
        let payConfig = {
            instanceChargingType: instanceChargingType,
        };
        const {duration, autoRepay, repayCycle, repayCycleType} =
            this.ref('payConfig')?.getFormData() || {};
        if (productType === 'prepay') {
            payConfig.instancePreChargingOption = {
                purchaseTime: duration,
                purchaseTimeUnit: 'month',
                autoRenew: autoRepay,
            };
            if (autoRepay) {
                payConfig.instancePreChargingOption.autoRenewTime = repayCycle;
                payConfig.instancePreChargingOption.autoRenewTimeUnit = repayCycleType || 'month';
            }
        }
        if (!this.data.get('showPayType')) {
            payConfig = {}; // 创建集群的时候不需要
        }
        const data = _.extend(
            {
                classify: instance.data.get('classify'),
                framework: instance.data.get('framework'),
                serviceType: this.data.get('instanceNodeType'),
                deploySetID,
                deploySetName,
                bid: !_.isEmpty(bidOption),
                bidOption,
            },
            formData,
            zoneSubnetsData,
            {bcc: instanceData},
            {cds: cdsData},
            {eip: eipData},
            loginData,
            payConfig,
        );
        const isShowNuma = this.data.get('isShowNuma');
        const showHt = this.data.get('showHt');
        if (!isShowNuma) {
            delete data.numaConfig;
        }
        if (!showHt) {
            delete data.cpuThreadConfig;
        }
        return data;
    }

    onCheckChange(e, index) {
        this.data.set(`cdsFormData.ebsSize[${index}].checked`, e.value);
        const path = this.data.get(`cdsFormData.ebsSize[${index}].path`);
        if (!path && e.value) {
            if (index === 0) {
                this.data.set(`cdsFormData.ebsSize[${index}].path`, e.path || '/home/<USER>');
            } else {
                this.data.set(`cdsFormData.ebsSize[${index}].path`, e.path || `/disk${index}`);
            }
        }
        if (!e.value) {
            this.data.set(`cdsFormData.ebsSize[${index}].path`, '');
        }
        this.checkCdsPath();
    }

    onPathChange(e, index) {
        this.data.set(`cdsFormData.ebsSize[${index}].path`, e.value);

        this.checkCdsPath();
    }

    onCdsAdd() {
        this.data.push('cdsFormData.ebsSize', {checked: false, path: ''});
    }

    onCdsDelete(index) {
        this.data.removeAt('cdsFormData.ebsSize', index);
    }

    focusTo(selector, pos) {
        $(selector)?.[0]?.scrollIntoView({behavior: 'smooth', block: pos});
    }

    getBccPrice() {
        const formData = this.getFormData();
        const instance = this.ref('instance');
        const instanceData = instance ? instance.getConfig() : {};
        if (!instanceData?.selectedFlavor) {
            return; // 没有选中机型不继续
        }
        let payload = _.extend(
            {
                productType: formData.productType,
                ephemeralSizeGb: formData.cds.diskSize,
                logicalZone: formData.logicalZone,
                rootDiskSizeInGb: formData,
            },
            _.pick(
                formData.bcc,
                'containsFpga',
                'cpu',
                'fpgaCard',
                'fpgaCount',
                'gpuCard',
                'gpuCount',
                'instanceType',
                'kunlunCard',
                'kunlunCount',
                'memory',
            ),
            _.pick(formData.bcc?.selectedFlavor, 'spec', 'specId'),
            _.pick(formData.cds, 'rootDiskSizeInGb', 'rootDiskStorageType'),
        );
        if (!payload.containsFpga) {
            delete payload.fpgaCard;
            delete payload.fpgaCount;
        }
        const params = {
            purchaseNum: formData.purchaseNum || 1,
            bcc: payload,
            cds: {
                productType: formData.productType,
                diskConfigs: [],
            },
        };
        const instanceNodeType = this.data.get('instanceNodeType');

        return this.$http
            .bccInstancePriceV2(params)
            .then(({result}) => {
                let bccPrice = _.filter(
                    result?.priceDetail || [],
                    item => item.servicetype === instanceNodeType,
                );
                let biddingPrices = bccPrice.length > 0 ? bccPrice[0].price : [];
                this.data.set('biddingPrices', biddingPrices);
                this.nextTick(() => {
                    const price =
                        instance?.data?.get('formData.defaultBidPrice') ||
                        instance?.data?.get('formData.bidPrice');
                    // 竞价实例回显用的默认价格，用过就清空
                    instance?.data?.set('formData.defaultBidPrice', null);
                    instance?.data?.set('formData.bidPrice', price);
                    instance?.onBiddingCustomPriceInput({
                        value: price,
                    });
                });
            })
            .catch(err => {
                this.data.set('biddingPrices', []);
                instance?.data?.set('formData.bidPrice', null);
                instance?.onBiddingCustomPriceInput({value: null});
                Notification.error('询价失败，请重试');
            });
    }
    // 回显时计费类型不能改
    handleProductType() {
        const {editData, productTypeList} = this.data.get();
        if (editData?.productType) {
            const {productType, instancePreChargingOption} = editData;
            this.data.set('formData.productType', productType);
            productTypeList.forEach(item => {
                item.disabled = item.value !== productType;
            });
            this.data.set('productTypeList', _.cloneDeep(productTypeList));
            this.nextTick(() => {
                if (
                    productType === 'prepay' &&
                    instancePreChargingOption &&
                    this.data.get('showPayType')
                ) {
                    const payConfigRef = this.ref('payConfig');
                    const {
                        purchaseTime = 1,
                        autoRenew = false,
                        autoRenewTime = 1,
                        autoRenewTimeUnit = 'month',
                    } = instancePreChargingOption;
                    if (payConfigRef) {
                        const data = {
                            payType: 'prepay',
                            duration: purchaseTime,
                            autoRepay: autoRenew,
                            repayCycle:
                                autoRenewTimeUnit === 'month' ? autoRenewTime : autoRenewTime * 12,
                            repayCycleType: autoRenewTimeUnit.toUpperCase(),
                        };
                        payConfigRef.data.set('formData', data);
                    }
                }
            });
        }
    }

    // 处理抢占实例场景，镜像选择服务集成镜像的时候，样式错乱问题
    handleStyleForBidding() {
        // 操作系统FormItem的父元素是<dd>标签, 监听这个标签高度
        try {
            const dd = document.querySelector('dd');
            if (dd) {
                const style = document.createElement('style');
                // bca-disable-next-line
                style.innerHTML = `.cce-cluster-create-custom-bidding {bottom: 119px !important;}`;
                const observer = new MutationObserver(() => {
                    try {
                        if (dd?.clientHeight > 40) {
                            document.head.appendChild(style);
                        } else {
                            document.head.removeChild(style);
                        }
                    } catch (error) {}
                });
                observer.observe(dd, {
                    childList: true,
                    arrtibutes: true,
                    subtree: true,
                });
            }
        } catch (error) {}
    }

    onPayConfigChange(data) {
        store.dispatch('setPayConfig', data);
    }
    onProductTypeChange(data) {
        if (data.value !== 'prepay') {
            // 兼容bidding
            store.dispatch('setPayConfig', {payType: 'postpay'});
        }
        this.data.set('selectedFlavor', null);
    }
    getIsShowNuma() {
        const instanceConfig = this.data.get('instanceConfig');
        this.data.set('isShowNuma', instanceConfig?.adjustNumaOptions || false);
        this.data.set('showHt', instanceConfig?.adjustCpuThreadOptions || false);
    }
}

@asComponent('@cce-cluster-create-add-custom')
export default class AddCustomStore extends connect.san(StoreMap)(AddCustom) {}
