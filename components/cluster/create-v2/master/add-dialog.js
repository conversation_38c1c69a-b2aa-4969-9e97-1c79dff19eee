/**
 * @file components/cluster/create-v2/master/add-dialog.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {Dialog, Form, FormItem, Button, RadioSelect} from '@baiducloud/bce-ui/san';
import {connect, store} from 'san-store';

import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {ClusterType} from '../../../../utils/enums';
import {CreateType, NodeType, NetworkModeCni, ExistHPASNodeType} from '../enums';
import AddCustom from './add-custom';
import AddExist from './add-exist';
import AddBbc from './add-bbc';

const $flag = ServiceFactory.resolve('$flag');

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<template>
    <ui-dialog
        s-ref="dialog"
        title="{{title}}"
        open="{=open=}"
        width="{{width}}"
        height="{{height}}"
    >
        <ui-form
            form-data="{=formData=}"
            class="cce-cluster-add-dialog {{isCloudEdge ? 'cce-cluster-add-dialog-edge' : ''}}"
        >
            <ui-form-item
                inline
                label-width="{{100}}"
                label="节点创建方式："
                name="createType"
                help="{{helpTip}}"
            >
                <label slot="label" title="节点创建方式：">节点创建方式：</label>
                <ui-radio-select
                    datasource="{{types}}"
                    value="{=formData.createType=}"
                    disabled="{{isExistEdit}}"
                    on-change="onChange"
                />
            </ui-form-item>
            <div s-if="isCustom">
                <ui-form-item
                    inline
                    label-width="{{100}}"
                    label="节点类型："
                    name="instanceNodeType"
                >
                    <label slot="label" title="节点类型：">节点类型：</label>
                    <ui-radio-select
                        datasource="{{nodeTypeList}}"
                        value="{=formData.instanceNodeType=}"
                        on-change="onTypeChange"
                    />
                    <div slot="help" s-if="{{formData.instanceNodeType === 'BBC'}}">
                        裸金属服务器BBC不支持挂载弹性网卡ENI，VPC-ENI模式下容器IP地址为分配的主网卡辅助IP
                    </div>
                </ui-form-item>
                <cce-cluster-create-add-bbc
                    s-ref="form"
                    s-if="isBbc"
                    node-type="{{nodeType}}"
                    showPayType="{{showPayType}}"
                />
                <!--enableBidding="{{isExpand}}" 节点扩容可以使用抢占实例-->
                <cce-cluster-create-add-custom
                    s-ref="form"
                    s-if="isBcc"
                    instanceNodeType="{{formData.instanceNodeType}}"
                    node-type="{{nodeType}}"
                    enableBidding="{{isExpand}}"
                    showPayType="{{showPayType}}"
                    isEni="{{isEni}}"
                    clusterUuid="{{clusterUuid}}"
                />
                <cce-cluster-create-add-custom
                    s-ref="form"
                    s-if="isEbc"
                    instanceNodeType="{{formData.instanceNodeType}}"
                    node-type="{{nodeType}}"
                    enableBidding="{{isExpand}}"
                    showPayType="{{showPayType}}"
                    isEni="{{isEni}}"
                    clusterUuid="{{clusterUuid}}"
                />
            </div>
            <div s-else>
                <cce-cluster-create-add-exist
                    s-ref="form"
                    node-type="{{nodeType}}"
                    form-data="{{formData}}"
                    enableBidding="{{isExpand}}"
                    isEni="{{isEni}}"
                    clusterUuid="{{clusterUuid}}"
                />
            </div>
        </ui-form>

        <div slot="foot">
            <ui-button on-click="onCancel">取消</ui-button>
            <ui-button
                on-click="onConfirm"
                skin="primary"
                disabled="{{disable}}"
                tip="{{disabledTip}}"
            >
                确定
            </ui-button>
        </div>
    </ui-dialog>
</template> `;

@invokeBceSanUI
class AddDialog extends Component {
    static template = template;

    static components = {
        'ui-dialog': Dialog,
        'ui-button': Button,
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-radio-select': RadioSelect,
        'cce-cluster-create-add-custom': AddCustom,
        'cce-cluster-create-add-exist': AddExist,
        'cce-cluster-create-add-bbc': AddBbc,
    };

    static messages = {
        instance_list_loading(e) {
            this.data.set('operations.instance_list_loading', e.value);
            this.disableConfirm();
        },
        instance_list_no_selected(e) {
            this.data.set('operations.instance_list_no_selected', e.value);
            this.disableConfirm();
        },
        form_validate(e) {
            this.data.set('operations.form_validate', e.value);
            this.disableConfirm();
        },
        instance_flavor_disabled(e) {
            this.data.set('operations.instance_flavor_disabled', e.value);
            this.disableConfirm();
        },
    };

    static computed = {
        isCustom() {
            const type = this.data.get('formData.createType');
            return type === CreateType.CUSTOM;
        },
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },
        isARM() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.ARM;
        },
        isExistEdit() {
            return !_.isUndefined(this.data.get('formData.index'));
        },
        isBcc() {
            const type = this.data.get('formData.instanceNodeType');
            return type === NodeType.BCC;
        },
        isEbc() {
            const type = this.data.get('formData.instanceNodeType');
            return type === NodeType.EBC;
        },
        isBbc() {
            const type = this.data.get('formData.instanceNodeType');
            return type === NodeType.BBC;
        },
        isHpas() {
            const type = this.data.get('formData.instanceNodeType');
            return type === ExistHPASNodeType.HPAS;
        },
        isMaster() {
            const nodeType = this.data.get('nodeType');
            return nodeType === 'Master';
        },
        isWorker() {
            const nodeType = this.data.get('nodeType');
            return nodeType === 'Worker';
        },
        isEni() {
            const networkMode = this.data.get('networkMode');
            return !_.isEmpty(NetworkModeCni.fromValue(networkMode));
        },
        helpTip() {
            const isCloudEdge = this.data.get('isCloudEdge');
            const isMaster = this.data.get('isMaster');
            if (isCloudEdge && isMaster) {
                return '云边集群Master节点需要公网访问，节点实例必须绑定EIP，或确保VPC内有NAT网关，并且Master节点子网已添加到NAT路由';
            }
            const isCustom = this.data.get('isCustom');
            return isCustom ? '' : '已有节点只支持选择当前集群私有网络下的可用实例';
        },
    };

    initData() {
        return {
            title: '添加节点',
            open: false,
            width: 1300,
            height: 500,
            disable: false,
            operations: {},
            types: CreateType.toArray(),
            formData: {
                createType: CreateType.CUSTOM,
                instanceNodeType: NodeType.BCC,
            },
            nodeTypeList: $flag.CceClusterCreateBBC
                ? NodeType.toArray()
                : NodeType.toArray('BCC', 'EBC'),
        };
    }

    inited() {
        const isNewBec = this.data.get('isNewBec');
        if (isNewBec) {
            this.data.set('types', CreateType.toArray('EXIST'));
            this.data.set('formData.createType', CreateType.EXIST);
            store.dispatch('setCreateType', CreateType.EXIST);

            return;
        }
        const createType = this.data.get('createType');
        if (!createType) {
            const formData = this.data.get('formData');
            if (!(formData && formData.createType)) {
                store.dispatch('setCreateType', CreateType.CUSTOM);
            }
        } else {
            this.data.set('formData.createType', createType);
        }
        const region = window.$context.getCurrentRegion().id;
        this.data.set('isHk', region === window.$context.getEnum('AllRegion').HKG);
        const {isEni, isCloudEdge} = this.data.get();
        if (isCloudEdge) {
            // 云边集群的master worker都是只支持bcc
            this.data.set('nodeTypeList', NodeType.toArray('BCC'));
        }

        const isARM = this.data.get('isARM');
        if (isARM) {
            this.data.set('types', CreateType.toArray('CUSTOM'));
            this.data.set('nodeTypeList[2].disabled', true);
        }
    }

    disableConfirm() {
        const operations = this.data.get('operations');
        const isHpas = this.data.get('isHpas');
        const invalid = !_.isEmpty(operations) && _.find(operations, value => value.disable);

        if (invalid && !isHpas) {
            this.data.set('disable', invalid.disable);
            this.data.set('disabledTip', invalid.message);
        } else {
            this.data.set('disable', false);
            this.data.set('disabledTip', '');
        }
    }

    onConfirm() {
        const form = this.ref('form');
        return form
            .validateForm()
            .then(() => {
                let formData = form.getFormData();
                const {createType, index} = this.data.get('formData');
                this.fire('confirm', {
                    value: formData,
                    type: createType,
                    index,
                });

                this.onCancel();
            })
            .catch(e => {
                console.error(new Error(e));
                this.nextTick(() => {
                    this.data.set('disable', false);
                    this.data.set('disabledTip', '');
                });
            });
    }

    onCancel() {
        this.data.set('open', false);
    }

    onChange(e) {
        this.data.set('formData.createType', e.value);
        this.data.set('operations', {});
        this.disableConfirm();
    }

    onTypeChange() {
        this.data.set('operations', {});
        this.disableConfirm();
    }
}

@asComponent('@cce-cluster-create-add-dialog')
export default class AddDialogStore extends connect.san(StoreMap)(AddDialog) {}
