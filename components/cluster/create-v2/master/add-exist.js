/**
 * @file components/cluster/create-v2/master/add-exist.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {utils as bccUtils} from '@baidu/bce-bcc-sdk-san';
import {Form, FormItem, RadioSelect, Switch, TextBox, Select} from '@baiducloud/bce-ui/san';
import {Checkbox, Input, Form as SanForm} from '@baidu/sui';
import * as AsyncValidator from 'async-validator';
import {connect} from 'san-store';
import {Tooltip} from '@baidu/sui';
import {GpuCustomDriver} from '@baidu/bce-bcc-sdk-san';
import {InstanceType, ImageType} from '@baidu/bce-bcc-sdk-enum';
import {ClusterType} from '../../../../utils/enums';
import cluster from '../../../../utils/cluster';
import {getSdk} from '../../../../utils/sdk';
import {compareVersion} from '@utils/util';
import {ExistNodeType, ExistHPASNodeType, BecNodeType, BecExistNodeType} from '../enums';
import Transfer from '../instance/transfer';
import TransferBec from '../instance-bec/transfer';
import {WhiteTagFormItem} from '../../../white-tag';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import LoginConfig from '../../../common/login-config';
import {Confirm} from '@components/common/confirm';
import AddHpasImageOs from '@components/cluster/create-v2/master/hpas-image-os';
import {findFlavor} from '@utils/service';

const {asComponent, invokeBceSanUI} = decorators;
const $flag = ServiceFactory.resolve('$flag');
const Schema = AsyncValidator.default;

const rules = new Schema({
    os: {
        validator(rule, value, callback, source) {
            // BBC暂时不支持重装操作系统
            if (
                source.nodeType === ExistNodeType.BBC ||
                source.nodeType === ExistHPASNodeType.HPAS
            ) {
                return callback();
            }
            if ((!source.osType || !source.osVersion) && source.rebuild) {
                return callback('请选择操作系统');
            }

            callback();
        },
    },
    adminPassword: {
        validator(rule, value, callback, source) {
            if (source.rebuild || source.allPasswordFill) {
                return callback();
            }

            if (!value) {
                return callback('请输入root密码');
            }

            callback();
        },
    },
    adminPasswordConfirm: {
        validator(rule, value, callback, source) {
            if (source.rebuild || source.allPasswordFill) {
                return callback();
            }

            if (!value) {
                return callback('请输入root密码');
            }

            if (value && value !== source.adminPassword) {
                return callback('密码不一致');
            }

            callback();
        },
    },
    dataDevice: {
        validator(rule, value, callback, source) {
            if (!source.needFormat) {
                return callback();
            }
            if (!value?.trim()) {
                return callback('设备名称不能为空');
            }
            callback();
        },
    },
    diskPath: {
        validator(rule, value, callback, source) {
            if (!source.needFormat) {
                return callback();
            }
            if (!value?.trim()) {
                return callback('挂载目录不能为空');
            }
            callback();
        },
    },
});

const DefaultFormData = {
    nodeType: ExistNodeType.BCC,
    instanceType: '',
    rebuild: true,
    adminPass: '',
    keypairId: '',
    needFormat: false,
    dataDevice: '',
    diskPath: '/home/<USER>',
};

const template = html`<div class="cce-cluster-master-add-exist">
    <ui-form s-ref="form" rules="{{rules}}" form-data="{=formData=}" errors="{=formErrors=}">
        <!-- 节点类型 -->
        <ui-form-item inline label-width="{{100}}" label="节点类型：" name="nodeType">
            <label slot="label" title="节点类型：">节点类型：</label>
            <ui-radio-select
                datasource="{{nodeTypeList}}"
                value="{=formData.nodeType=}"
            ></ui-radio-select>
            <div slot="help" s-if="{{formData.nodeType === 'BBC'}}">
                裸金属服务器BBC不支持挂载弹性网卡ENI，VPC-ENI模式下容器IP地址为分配的主网卡辅助IP
            </div>
        </ui-form-item>

        <!-- 重装操作系统 -->
        <cce-white-tag-form-item
            featureType="enableVirtualEth"
            s-if="!isBbc && !isBec && !isNewBec"
        >
            <ui-form-item inline label-width="{{100}}" label="重装操作系统：" name="rebuild">
                <label slot="label" title="重装操作系统：">重装操作系统：</label>
                <s-tooltip
                    class="system-tip"
                    trigger="focus"
                    visible="{{isEbc && showSystemTip && !ebcReinstallSystem}}"
                    placement="right"
                >
                    <div slot="content">
                        <div class="system-title">重装操作系统</div>
                        <div class="system-des">
                            您可以为弹性裸金属服务器EBC进行重装操作系统操作
                        </div>
                    </div>
                    <ui-switch
                        checked="{=formData.rebuild=}"
                        disabled="{{!allowRebuild}}"
                    ></ui-switch>
                </s-tooltip>
                <div s-if="formData.rebuild && allowRebuild" class="cluster-node-rebuild-tip">
                    将对所选的实例进行操作系统重装，会清除系统盘内数据且不可恢复（云磁盘数据不受影响）。
                </div>
                <div s-if="!allowRebuild" class="cluster-node-rebuild-tip">
                    FPGA实例暂不支持，您可以在BCC重装系统后直接添加到集群
                </div>
            </ui-form-item>
        </cce-white-tag-form-item>

        <!-- 实例节点选择穿梭框 -->
        <ui-form-item label=" " inline label-width="{{100}}">
            <!-- nodeType 节点类型为 bec -->
            <cce-cluster-create-transfer-bec
                s-if="isBec || isNewBec"
                s-ref="transfer"
                instance-node-type="{=formData.nodeType=}"
                isRebuild="{{formData.rebuild}}"
                on-password-change="onPasswordChange"
            ></cce-cluster-create-transfer-bec>

            <!-- nodeType 节点类型不是 bec -->
            <cce-cluster-create-transfer
                s-else
                s-ref="transfer"
                instance-node-type="{=formData.nodeType=}"
                instance-list="{{formData.instanceList}}"
                nodeType="{{nodeType}}"
                flavors="{{flavors}}"
                on-change="onInstanceListChange($event)"
                enableBidding="{{enableBidding}}"
                isEbc="{{isEbc}}"
                isHpas="{{isHpas}}"
                isEni="{{isEni}}"
                on-showEbcTip="showEbcTip"
                isRebuild="{{formData.rebuild}}"
                on-password-change="onPasswordChange"
            >
            </cce-cluster-create-transfer>
            <div class="tip-confirm-wrap" s-if="{{!formData.rebuild && !isHpas}}">
                <tip-confirm label="我已知晓【不重装操作系统使用风险】" s-ref="confirm">
                    <div slot="desc">
                        添加节点不重装操作系统，服务器实例上的旧数据可能会导致节点添加失败或出现其它问题，推荐选择重装操作系统
                    </div></tip-confirm
                >
            </div>
        </ui-form-item>

        <!--数据盘挂载-->
        <ui-form-item
            inline
            label-width="{{100}}"
            label="磁盘挂载："
            name="needFormat"
            s-if="!isNewBec"
        >
            <s-checkbox
                label="将容器和镜像数据存储在数据盘或本地盘中"
                checked="{=formData.needFormat=}"
            />
            <div class="disk-info" s-if="formData.needFormat">
                <ui-form-item inline label-width="{{0}}" label="" name="dataDevice">
                    <s-input
                        addonBefore="设备名称"
                        value="{=formData.dataDevice=}"
                        placeholder="请输入设备名称或分区。如/dev/vdb"
                        width="220"
                    />
                </ui-form-item>
                <ui-form-item inline label-width="{{0}}" label="" name="diskPath">
                    <s-input
                        addonBefore="挂载目录"
                        value="{=formData.diskPath=}"
                        placeholder="请输入挂载目录。如/home/<USER>"
                        width="220"
                    />
                </ui-form-item>
            </div>
            <div class="tip" s-if="formData.needFormat">
                系统将会对所选的全部节点设备格式化为ext4,已经格式化的设备将直接挂载目录。
                <span>格式化将会导致原有数据丢失，请注意备份。 </span>
                若输入的设备名称与所选节点的实际名称不匹配，系统会
                <span>对不匹配的节点报错并终止节点初始化流程</span>
                ，请确保填写的设备名称符合预期。若填写的挂载目录不存在，系统会
                <span>自动创建对应目录</span>
            </div>
        </ui-form-item>

        <!-- 未选择重装操作系统时，输入 root 密码 -->
        <ui-form-item
            s-if="!formData.rebuild || isBec"
            inline
            label-width="{{100}}"
            label="管理员名称："
        >
            <label slot="label" title="管理员名称">管理员名称：</label>
            <div class="mt5">root</div>
        </ui-form-item>
        <ui-form-item
            s-if="!formData.rebuild || isBec"
            inline
            required
            label-width="{{100}}"
            label="密码："
            name="adminPassword"
        >
            <label slot="label" title="密码">密码：</label>
            <s-input
                value="{=formData.adminPassword=}"
                type="password"
                showPasswordIcon
                placeholder="请输入root密码"
                autocomplete="new-password"
            />
        </ui-form-item>
        <ui-form-item
            s-if="!formData.rebuild || isBec"
            inline
            required
            label-width="{{100}}"
            label="确认密码："
            name="adminPasswordConfirm"
        >
            <label slot="label" title="确认密码">确认密码：</label>
            <s-input
                value="{=formData.adminPasswordConfirm=}"
                type="password"
                showPasswordIcon
                placeholder="请再次输入root密码"
                autocomplete="new-password"
            />
            <div class="cluster-node-rebuild-tip">
                若节点密码一致，只需在此输入密码并确认即可，若各节点密码不同，请在上方已选资源列表中逐一输入。<br />
                请务必为所选实例输入正确的root用户密码，否则Kubernetes将部署失败。
            </div>
        </ui-form-item>

        <!-- 镜像类型 -->
        <ui-form-item
            s-if="!isBec && formData.rebuild && !isHpas"
            inline
            label-width="{{100}}"
            label="镜像类型："
            name="imageType"
        >
            <label slot="label" title="镜像类型：">镜像类型：</label>
            <ui-radio-select
                value="{=imageType=}"
                datasource="{{imageTypeList}}"
                on-change="onImageTypeChange"
            ></ui-radio-select>
        </ui-form-item>

        <!-- 操作系统 -->
        <ui-form-item
            s-if="!isBec && formData.rebuild && !isHpas"
            inline
            label-width="{{100}}"
            label="操作系统："
            name="os"
        >
            <label slot="label" title="操作系统：">操作系统：</label>
            <ui-select
                value="{=formData.osType=}"
                datasource="{{osType.list}}"
                on-change="onOsTypeChange($event)"
                tip-position="lt"
                width="150"
            ></ui-select>
            <ui-select
                class="image-version-select {{imageDisabledByUbuntu24 ? 'error-select' : ''}}"
                value="{=formData.osVersion=}"
                datasource="{{osType.versions}}"
                on-change="onOsVersionChange($event)"
                tip-layer-width="{{200}}"
                tip-position="lt"
            ></ui-select>
            <div s-if="imageDisabledByUbuntu24 && clusterUuid" class="error-by-network-plugin">
                当前集群CCE Network Plugin组件版本不支持，请前往<a
                    href="#/cce/ai/helm/release?clusterUuid={{clusterUuid}}&active=network"
                    target="_blank"
                >
                    组件管理 </a
                >升级CCE Network Plugin版本
            </div>
            <div s-if="imageType==='public'" class="os-tip">
                若你需要挂载极速型L2并行文件存储（PFS），建议选择CentOS或者Ubuntu操作系统，<a
                    href="https://cloud.baidu.com/doc/PFS/s/olzz6s5bi"
                    target="_blank"
                    >详细说明</a
                >。
            </div>
        </ui-form-item>
        <s-form
            s-ref="hpasForm"
            data="{=osFormData=}"
            s-if="formData.rebuild && isHpas && false"
            rules="{{sanRules}}"
        >
            <add-hpas-image-os s-ref="hpasImageOs" formData="{=osFormData=}" appInfo="{{appInfo}}"
        /></s-form>

        <!--是否安装GPU驱动-->
        <x-gcd
            enableCheckGPUDriver
            s-if="!isBec && formData.rebuild && enableCustomDriver"
            id="gpuDriver"
            s-ref="gpuCustomDriver"
            on-change="handleGpuDriverChange"
            driverTip="{{tip}}"
            sdk="{{sdk}}"
            osItem="{{osItem}}"
            flavor="{{selectedFlavor}}"
        />

        <!-- 登录方式，密钥对设置与自定义密码 -->
        <login-config
            s-ref="login"
            s-if="!isBec && formData.rebuild"
            showName="{{false}}"
            showKeypair="{{showKeypair}}"
            showRandomAdminPassType="{{false}}"
            adminName="{{adminName}}"
        ></login-config>
    </ui-form>
</div> `;

const MODE = {
    EDIT: 'EDIT',
    CREATE: 'CREATE',
};

const DefaultImageTypeList = [
    ImageType.COMMON,
    ImageType.GPU_BCC_IMAGE,
    ImageType.CUSTOM,
    ImageType.SHARING,
    ImageType.VGPU_BCC_IMAGE,
    ImageType.GPU_BCC_CUSTOM,
];

// 根据所有的镜像列表 分类出支持的镜像类型。
const getImageClassify = images => {
    const imageGroups = _.groupBy(images, 'categoryKey');
    // 共享镜像和自定义镜像按时间顺序排序
    if (imageGroups.sharing) {
        imageGroups.sharing.reverse();
    }
    if (imageGroups.custom) {
        imageGroups.custom.reverse();
    }

    const classify = Object.keys(imageGroups).map(key => {
        const text = imageGroups[key][0]?.category;
        return {value: key, text: text || key};
    });

    return {classify, imageGroups};
};

@invokeBceSanUI
class AddExist extends Component {
    static components = {
        'add-hpas-image-os': AddHpasImageOs,
        'login-config': LoginConfig,
        'ui-form': Form,
        's-form': SanForm,
        's-form-item': SanForm.Item,
        'ui-form-item': FormItem,
        'ui-radio-select': RadioSelect,
        'ui-switch': Switch,
        'ui-text-box': TextBox,
        'ui-select': Select,
        'cce-cluster-create-transfer': Transfer,
        'cce-white-tag-form-item': WhiteTagFormItem,
        'cce-cluster-create-transfer-bec': TransferBec,
        's-checkbox': Checkbox,
        's-input': Input,
        's-tooltip': Tooltip,
        'tip-confirm': Confirm,
        'x-gcd': GpuCustomDriver,
    };

    static template = template;

    static computed = {
        isBbc() {
            const nodeType = this.data.get('formData.nodeType');
            return nodeType === ExistNodeType.BBC;
        },
        isBec() {
            const nodeType = this.data.get('formData.nodeType');
            return nodeType === BecNodeType.BEC;
        },
        isEbc() {
            const nodeType = this.data.get('formData.nodeType');
            return nodeType === ExistNodeType.EBC;
        },
        isHpas() {
            const nodeType = this.data.get('formData.nodeType');
            return nodeType === ExistHPASNodeType.HPAS;
        },
        showKeypair() {
            const {osType} = this.data.get('formData');
            return !$flag.CceClusterCreateNoKeypair && osType !== 'windows';
        },
        adminName() {
            const osType = this.data.get('osTypeValue');
            const os = cluster.OS[osType];
            return os ? os.ADMIN_NAME : 'root';
        },
        isMaster() {
            const nodeType = this.data.get('nodeType');
            return nodeType === 'Master';
        },
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },

        // k8s 小于 1.24.4
        lessThan124() {
            const k8sVersion = this.data.get('k8sVersion');

            return !k8sVersion || compareVersion('1.24.4', k8sVersion);
        },
        enableCustomDriver() {
            const imageType = this.data.get('imageType');
            const instanceList = this.data.get('formData.instanceList') || [];
            const item = instanceList.find(item => item.cardCount > 0);

            return !!(item && ~['common', 'public'].indexOf(imageType));
        },
        osItem() {
            const osVersion = this.data.get('formData.osVersionText');
            const osName = this.data.get('formData.osType');
            return {
                osVersion,
                osName,
            };
        },
        imageDisabledByUbuntu24() {
            const osVersionList = this.data.get('osType.versions');
            const imageType = this.data.get('imageType');
            const osVersion = this.data.get('formData.osVersion');
            const networkPluginBiggerThan2_12_18 = this.data.get('networkPluginBiggerThan2_12_18');
            const target = osVersionList?.find(e => e.value === osVersion);

            if (target) {
                if (
                    imageType === 'public' &&
                    target.osName === 'Ubuntu' &&
                    target.osVersion?.startsWith('24.') &&
                    !networkPluginBiggerThan2_12_18
                ) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        },

        appInfo() {
            const data = this.data.get('formData.instanceList') || [];
            return data[0] || {};
        },
    };

    initData() {
        return {
            formData: DefaultFormData,
            osFormData: {
                imageType: '',
                osType: '',
                osName: '',
                osVersion: '',
            },
            formErrors: {},
            rules,
            sanRules: {
                imageType: [{required: true, message: '请选择镜像类型'}],
                osType: [{required: true, message: '请选择操作系统'}],
            },
            nodeTypeList: $flag.CceClusterCreateBBC
                ? ExistNodeType.toArray()
                : ExistNodeType.toArray('BCC'),
            flavorType: {
                list: [],
            },
            imageType: {
                list: [],
            },
            osType: {
                list: [],
                versions: [],
            },
            sdk: getSdk('BCC'),
            imageTypeList: [],
            allowRebuild: true,
            // 当前组件的模式 create 为新增, edit 为编辑, 默认为新增
            mode: MODE.CREATE,
            // 编辑模式下，缓存一下开始选中的实例列表
            cachedInstanceList: [],
            showSystemTip: false,
            ebcReinstallSystem: false,
            instanceList: [],
            tip: '开启后将由系统自动为您安装GPU驱动、CUDA、cuDNN库，免去手动安装GPU必备环境，同时安装过程预计耗时约 10 到 20 分钟，实例启动时间会更长，并伴有自动重启。',
        };
    }

    inited() {
        this.getSpecFlavorList();
        this.configInit();
        this.getNetworkPluginVersion();
        const isNewBec = this.data.get('isNewBec');
        if (isNewBec) {
            this.becConfigInit();
        } else {
            this.configInit();
        }
    }

    async getNetworkPluginVersion() {
        try {
            const {clusterUuid} = this.data.get();
            if (!clusterUuid) {
                return;
            }
            const data = await this.$http.getCompsData(
                clusterUuid,
                'cce-vpc-native-cni,cce-network-v2',
            );
            if (data?.result?.items?.length === 2) {
                const networkV1 = data.result.items.find(
                    e => e.meta?.name === 'cce-vpc-native-cni',
                );
                const networkV2 = data.result.items.find(e => e.meta?.name === 'cce-network-v2');
                if (networkV2?.instance?.installedVersion) {
                    if (!compareVersion('2.12.18', networkV2?.instance?.installedVersion)) {
                        // 大于等于2.12.18
                        this.data.set('networkPluginBiggerThan2_12_18', true);
                    }
                } else if (networkV1?.instance?.installedVersion) {
                    if (!compareVersion('2.12.18', networkV1?.instance?.installedVersion)) {
                        // 大于等于2.12.18
                        this.data.set('networkPluginBiggerThan2_12_18', true);
                    }
                }
            }
        } catch (error) {}
    }

    configInit() {
        const serviceInfo = window.$context.SERVICE_TYPE?.HPAS?.region || {};
        if (serviceInfo[window.$context.getCurrentRegionId()]) {
            this.data.set('nodeTypeList', [
                ...ExistNodeType.toArray(),
                ...ExistHPASNodeType.toArray(),
            ]);
        }

        const formData = this.data.get('formData');
        this.data.set('cachedInstanceList', formData.instanceList || []);
        // 有选中的实例列表时，表明是编辑模式
        const mode =
            formData.instanceList && formData.instanceList.length > 0 ? MODE.EDIT : MODE.CREATE;
        this.data.set('mode', mode);

        // 如果是编辑模式，则将镜像类型同步至组件实例 data 中
        if (mode === MODE.EDIT) {
            formData.imageType && this.data.set('imageType', formData.imageType);

            const allFill = formData.instanceList.every(item => !!item.password);
            this.data.set('formData.allPasswordFill', allFill);
        }

        const region = window.$context.getCurrentRegion().id;
        this.data.set('isHk', region === window.$context.getEnum('AllRegion').HKG);

        // 云边集群Master 支持bcc和ebc
        const {isMaster, isCloudEdge, isEni} = this.data.get();
        if (isCloudEdge) {
            this.data.set('nodeTypeList', ExistNodeType.toArray('BCC', 'EBC'));
        }
        // worker节点 云边集群 也要支持bcc
        if (!isMaster && isCloudEdge) {
            this.data.push('nodeTypeList', BecNodeType.fromAlias('BEC'));
        }

        // 根据指定顺序排序
        const osTypeSortList = ['BaiduLinux', 'CentOS', 'Ubuntu', 'Rocky Linux'];
        this.watch('osType.list', data => {
            if (data?.length > 1) {
                const sortList = _.cloneDeep(data).sort((a, b) => {
                    const aIndex = osTypeSortList.findIndex(e => e === a.value) + 1;
                    const bIndex = osTypeSortList.findIndex(e => e === b.value) + 1;
                    return aIndex - bIndex;
                });
                if (!_.isEqual(sortList, data)) {
                    this.data.set('osType.list', sortList);
                    this.onOsTypeChange({value: sortList[0]?.value});
                }
            }
        });
    }
    becConfigInit() {
        const nodeTypeList = BecExistNodeType.toArray();
        // const BEC_EBC = nodeTypeList.find(e => e.value === BecExistNodeType.BEC_EBC);
        // if (BEC_EBC) {
        //     BEC_EBC.disabled = true;
        //     BEC_EBC.tip = '暂不支持，敬请期待';
        // }
        this.data.set('nodeTypeList', nodeTypeList);
        this.data.set('formData.nodeType', BecExistNodeType.BEC);
        this.data.set('formData.rebuild', false);
    }

    showEbcTip() {
        this.data.set('showSystemTip', true);
        localStorage.setItem('ebcReinstallSystem', 'true');
    }

    async attached() {
        const isNewBec = this.data.get('isNewBec');
        if (isNewBec) {
            return;
        }
        const ebcReinstallSystem = localStorage.getItem('ebcReinstallSystem');
        this.data.set('ebcReinstallSystem', ebcReinstallSystem === 'true');
        const formData = this.data.get('formData');
        const mode = this.data.get('mode');
        const {isMaster, isCloudEdge} = this.data.get();

        if (_.isUndefined(formData.nodeType)) {
            this.data.set('formData', DefaultFormData);
        }

        let tasks = [];
        // 获取BBC套餐列表
        if ($flag.CceClusterCreateBBC && !(isMaster && isCloudEdge)) {
            tasks.push(this.getFlavors('zoneA'));
        }

        Promise.all(tasks);

        this.watch('formData.nodeType', () => {
            this.data.set('showSystemTip', false);
            if (this.data.get('isBbc')) {
                this.data.set('formData.rebuild', false);
            } else {
                this.data.set('formData.rebuild', !this.data.get('isBec'));
            }

            if (this.data.get('isHpas')) {
                this.data.set('formData.needFormat', false);
            }

            this.data.set('formErrors', null);
        });

        this.watch('formData.osType', value => {
            this.updateOsVersion(value);
        });

        this.watch('formErrors', value => {
            if (value && _.find(value, d => d)) {
                this.dispatch('form_validate', {
                    disable: true,
                    message: '参数校验失败',
                });
            } else {
                this.dispatch('form_validate', {disable: false});
            }
        });
        this.watch('formData.rebuild', data => {
            if (!data) {
                this.data.set('formErrors', null);
            }
        });
        await this.getCceImages();
        await this.initImageTypeDatasource(formData.instanceList, mode === MODE.CREATE);

        // 如果是编辑模式，则初始化操作系统数据
        if (mode === MODE.EDIT) {
            const imageType = this.data.get('formData.imageType');
            let originOsData = this.data.get(`imageDatasource.${imageType}`);
            this.data.set('osData', originOsData);
            this.data.set('osType.list', originOsData);

            // 更新系统版本
            this.updateOsVersion(formData.osType);
        }
    }

    // 监听选择的实例列表发生变化
    onInstanceListChange(instanceList = []) {
        this.data.set('formData.instanceList', instanceList);
        // 如果回调的实例列表为空, 则不做处理
        if (!instanceList.length) {
            return;
        }
        this.setInfo(instanceList);
        const mode = this.data.get('mode');
        // 如果是新增模式，则初始化镜像类型
        if (mode === MODE.CREATE) {
            this.initImageTypeDatasource(instanceList);
            return;
        }

        // 如果是编辑模式
        if (mode === MODE.EDIT) {
            let cachedInstanceList = this.data.get('cachedInstanceList');
            if (cachedInstanceList.length) {
                let instanceIds = instanceList.map(item => item.instanceId);
                this.data.set(
                    'cachedInstanceList',
                    cachedInstanceList.filter(item => instanceIds.indexOf(item.instanceId) === -1),
                );
            } else {
                this.initImageTypeDatasource(instanceList);
            }
        }
    }

    getFlavors(logicalZone) {
        if (this.data.get('isHk')) {
            return;
        }
        return this.$http
            .getBbcInstanceFlavor({logicalZone})
            .then(flavors => this.data.set('flavors', flavors));
    }

    getImageData(instanceList) {
        if (this.data.get('isHpas')) {
            return;
        }
        const instanceIds = instanceList.map(item => item.instanceId);
        const payload = {instanceIds};

        return this.$http.getImageListOnRebuild(payload).then(({result}) => {
            const cceImagesMap = this.data.get('cceImagesMap');
            const {commonImages = [], customImages = []} = result;
            const allImages = [...commonImages, ...customImages];
            const filteredImages = allImages
                .filter(item => cceImagesMap[item.imageId] !== undefined)
                .sort((a, b) => cceImagesMap[a.imageId] - cceImagesMap[b.imageId]);
            let {classify, imageGroups} = getImageClassify(filteredImages);

            classify = classify.filter(item => item.value !== 'subject_integration');

            classify.forEach(item => {
                const imageType = item.value;
                const osDS = imageGroups[imageType];

                this.data.merge('imageDatasource', {
                    [imageType]: this.transforImageAndOs(imageType, osDS),
                });

                if (item.value !== ImageType.MKT && osDS) {
                    item.disabled = osDS.length === 0 || osDS.some(o => o.disabled);
                }
            });

            this.data.set('imageTypeList', classify);
        });
    }
    async getSpecFlavorList() {
        const {result: data = []} = await this.$http.getSpecFlavorList({});
        this.data.set('flavorData', data);
    }
    async setInfo(instanceList) {
        try {
            const machineType = this.data.get('formData.nodeType');
            const data = this.data.get('flavorData');
            const item = instanceList.find(item => item.cardCount > 0);
            const nodeInfo = {
                logicalZone: item.logicalZone,
                cpu: item.cpu,
                instanceType: item.instanceType,
                memory: item.memory,
                machineType: machineType,
                spec: item.spec,
            };

            const flavorData = findFlavor(nodeInfo, data);
            this.data.set('selectedFlavor', flavorData.flavor);
        } catch (error) {}
    }

    transforImageAndOs(imageType, result) {
        let osData = [];

        _.each(_.groupBy(result, 'osName'), (items, key) => {
            const osVersions = _.map(items, item => {
                return {
                    ...item,
                    // text: item.osVersion,
                    text: item.name,
                    value: item.imageId,
                };
            });
            const osType = {
                text: key,
                value: key,
                osType: items[0].osType,
                children: osVersions,
            };

            const isExclusiveEni = this.data.get('isExclusiveEni');
            const eBPFEnabled = this.data.get('eBPFEnabled');
            const lessThan124 = this.data.get('lessThan124');

            if (imageType === 'public') {
                // 开启eBPF增强后，添加节点选择公共镜像时，只允许选择Baidulinux 3.0
                if (!isExclusiveEni && eBPFEnabled) {
                    if (key === 'BaiduLinux') {
                        const allowedOsVersion = '3.0';

                        osType.children = osVersions.filter(osVersion =>
                            osVersion.osVersion.includes(allowedOsVersion),
                        );
                    } else if (key === 'Ubuntu') {
                        const allowedOsVersion = '22.04 LTS';
                        osType.children = osVersions.filter(osVersion =>
                            osVersion.osVersion.includes(allowedOsVersion),
                        );
                    } else {
                        osType.disabled = true;
                        osType.tip = 'DataPath V2 不支持该操作系统';
                    }
                } else if (lessThan124) {
                    // Ubuntu 22.04 只支持 k8s 1.24 及以上版本
                    if (key === 'Ubuntu') {
                        osType.children = osVersions.filter(
                            osVersion =>
                                !osVersion.osVersion.includes('22.04') &&
                                !osVersion.osVersion.includes('24.04'),
                        );
                    }
                }
            }

            osData.push(osType);
        });

        return _.sortBy(osData, 'text');
    }

    async getCceImages() {
        return this.$http
            .imageSupportedOs({imageTypeList: DefaultImageTypeList})
            .then(({result}) => {
                const cceImagesMap = {};

                result.forEach((image, index) => {
                    cceImagesMap[image.imageId] = index; // 用于镜像过滤及排序
                });

                this.data.set('cceImagesMap', cceImagesMap);
            });
    }

    async onImageTypeChange({value}) {
        try {
            this.data.set('imageType', value);

            // 同时将 镜像类型 设置进 formData 中
            this.data.set('formData.imageType', value);

            let originOsData = this.data.get(`imageDatasource.${value}`);
            this.data.set('osData', originOsData);
            this.data.set('osType.list', originOsData);
            const osType = _.get(originOsData, '[0].value', '');
            this.data.set('formData.osType', osType);
            this.onOsTypeChange({value: osType});
        } catch (error) {}
    }

    check(prop, value) {
        return _.some(this.data.get('instanceList'), instance => instance[prop] === value);
    }

    async initImageTypeDatasource(instanceList = [], initImageType = true) {
        const rebuild = this.data.get('formData.rebuild');
        if (!rebuild) {
            return;
        }
        await this.getImageData(instanceList);

        this.data.set('instanceList', instanceList);

        if (this.check('instanceType', InstanceType.FPGA)) {
            this.data.set('formData.rebuild', false);
            this.data.set('allowRebuild', false);
        }
        // 传入的参数为 true 时，初始化操作，在 attached 中 edit 模式下为 false
        if (initImageType && !this.data.get('isHpas')) {
            const imageType = this.data.get('imageTypeList')[0].value;

            this.data.set('imageType', imageType);
            this.onImageTypeChange({value: imageType});
        }
    }

    updateOsVersion(osType) {
        const {instanceList, cachedInstanceList} = this.data.get();
        let list = this.data.get('osType.list');
        let index = _.findIndex(list, item => item.value === osType);
        let osVersionList = _.get(list, `[${index}].children`, '');
        if (instanceList.length) {
            const imageType = this.data.get('imageType');
            // ? rebuildFilterImage 和 rebuildFilterOsVersion 的过滤规则是什么
            osVersionList = bccUtils.rebuildFilterImage(
                [imageType],
                osVersionList,
                instanceList,
                false,
            );
            osVersionList = bccUtils.rebuildFilterOsVersion(osVersionList, instanceList);
        }

        const filterOsVersionList = osVersionList?.filter(e => !e.osArch.includes('aarch64'));
        this.data.set('osType.versions', filterOsVersionList);

        let item = _.find(osVersionList, item => !item.disabled);

        if (item && cachedInstanceList.length === 0) {
            this.data.set('formData.osVersion', item.value);
            this.data.set('formData.osVersionText', item.text);
        }
    }

    onOsTypeChange(e) {
        const list = this.data.get('osType.list');
        const selectedItem = _.find(list, item => item.value === e.value);
        this.data.set('osTypeValue', selectedItem ? selectedItem.osType : '');
        this.data.set('formData.osVersion', '');
        this.data.set('formData.osVersionText', '');
        this.updateOsVersion(e.value);
    }

    onOsVersionChange(e) {
        const item = _.find(this.data.get('osType.versions'), d => d.value === e.value);
        if (item) {
            this.data.set('formData.osVersionText', item.text);
        }
    }

    onPasswordChange(allFill) {
        this.data.set('formData.allPasswordFill', allFill);

        const formErrors = this.data.get('formErrors');
        // 当每个实例都有输入密码时，root 密码不需要再输入
        if (allFill && formErrors?.adminPassword) {
            this.data.set('formErrors', {...formErrors, adminPassword: null});
        }
    }

    async validateForm() {
        const form = this.ref('form');
        const transfer = this.ref('transfer');
        const hpasForm = this.ref('hpasForm');
        const login = this.ref('login');
        const confirmRef = this.ref('confirm');
        const tasks = [form.validateForm(), transfer.validateForm()];

        if (login) {
            tasks.push(login.validateForm());
        }

        if (hpasForm) {
            tasks.push(hpasForm.validateFields());
        }

        if (this.data.get('imageDisabledByUbuntu24') && !hpasForm) {
            return Promise.reject('请选择镜像');
        }

        if (confirmRef) {
            await Promise.all(tasks);
            return confirmRef.validateForm();
        }

        return Promise.all(tasks);
    }

    handleGpuDriverChange(e) {
        this.data.set('formData.gpuDriver', e);
    }

    getFormData() {
        const baseData = this.data.get('formData');
        const transfer = this.ref('transfer');
        const hpasImageOsRef = this.ref('hpasImageOs');
        const {imageType, osType} = hpasImageOsRef?.getFormData() || {};
        imageType && (baseData.imageType = imageType);
        osType && (baseData.osVersion = osType);
        const login = this.ref('login');

        const transferData = transfer.getFormData();
        const loginData = login ? login.getFormData() : {};
        baseData.userData = this.getDriverData(baseData.gpuDriver);

        if (this.ref('gpuCustomDriver')) {
            const checkGPUDriver = this.ref('gpuCustomDriver').data.get('checkGPUDriver');
            baseData.checkGPUDriver = checkGPUDriver;
        }

        return _.extend(
            {},
            _.omit(baseData, 'allPasswordFill', 'adminPasswordConfirm'),
            transferData,
            _.pick(loginData, 'adminPass', 'keypairId', 'adminPassType'),
        );
    }
    getDriverData(customVersion) {
        if (customVersion?.length === 3) {
            const version =
                '#!/bin/bash\n' +
                `DRIVER_VERSION="${customVersion[1]}"\n` +
                `CUDA_VERSION="${customVersion[0]}"\n` +
                `CUDNN_VERSION="${customVersion[2]}"\n` +
                'WORK_DIR="/root/auto_install"\n' +
                'SCRIPT_URL="http://mirrors.baidubce.com/nvidia-binary-driver/api/auto_install.sh"\n';
            const content = `mkdir \${WORK_DIR}
pushd \${WORK_DIR}
for ((i=0; i<120; i++))
do
    wget --timeout=10 -t 10 \${SCRIPT_URL}
    if [ $? -eq 0 ]; then
        break
    else
        sleep 1
    fi
done
bash \${WORK_DIR}/$(basename \${SCRIPT_URL}) \${DRIVER_VERSION} \${CUDA_VERSION} \${CUDNN_VERSION}
popd
rm -rf \${WORK_DIR}

cmdline=$(cat /proc/cmdline)
if [[ "\${cmdline}" =~ "pci=realloc" ]]; then
    echo "remove 'pci=realloc' cmdline arg and update grub"
    default_grub_arg="/etc/default/grub"
    sed -i 's/pci=realloc//g' \${default_grub_arg}
    if command -v grub2-mkconfig; then
        efi_grub_cfg=/boot/efi/EFI/centos/grub.cfg
        if [ -f /boot/efi/EFI/rocky/grub.cfg ]; then
            efi_grub_cfg=/boot/efi/EFI/rocky/grub.cfg
        fi
        grub2-mkconfig -o $efi_grub_cfg
    fi
    if command -v update-grub; then
        update-grub
    fi
    reboot
else
    echo "there is no 'pci=realloc' arg in current cmdline, do nothing"
fi`;
            return btoa(version + content);
        } else {
            return null;
        }
    }
}

@asComponent('@cce-cluster-create-add-exist')
export default class AddExistStore extends connect.san(StoreMap)(AddExist) {}
