/**
 * @file components/cluster/create-v2/master/add-hpas.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, HttpClient} from '@baiducloud/runtime';
import {notification, RadioSelect, Tip} from '@baiducloud/bce-ui/san';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {Tip as BceTip, TipRadio, TipSelect} from '@baidu/sui-biz';
import {Slider, Tooltip, Form, Select, Loading, Button, Radio} from '@baidu/sui';
import {AdvancedConfigPanel} from '@baidu/bce-bcc-sdk-san';
import {SubnetType} from '@baidu/bce-bcc-sdk-enum';
import {SubnetCreateDialog} from '@baidu/bce-vpc-sdk-san';
import {VpcSDK} from '@baidu/bce-vpc-sdk';
import {connect} from 'san-store';
import {PayType} from '../../../../utils/enums';
import {NetworkModeCni} from '@components/cluster/create-v2/enums';
import {getSubnetName} from '../../../../utils/helper';
import {getSubnetRemainingIP} from '../../../../utils/util';
import AddHpasImageOs from './hpas-image-os';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import './add-hpas.less';
import ProductTypeSelector from '@components/product-type';
import PayConfig from '../../../../pages/cluster/create-v2/components/pay-config';

const getRules = self => {
    return {
        productType: [{required: true, message: '请选择付费方式：'}],
        logicalZone: [{required: true, message: '请选择可用区'}],
        subnetId: [{required: true, message: '请选择子网'}],
        appType: [{required: true, message: '请选择应用类型'}],
        appPerformanceLevel: [{required: true, message: '请选择性能指标等级'}],
        imageType: [{required: true, message: '请选择镜像类型'}],
        osType: [{required: true, message: '请选择操作系统'}],
    };
};

const template = html`<div class="cce-cluster-add-hpas">
    <s-form
        s-ref="form"
        label-align="left"
        label-width="{{200}}"
        data="{=formData=}"
        rules="{{rules}}"
    >
        <!-- 付费地域 -->
        <s-form-item inline label-width="{{100}}" label="付费方式：" prop="productType">
            <label slot="label" title="付费方式：">付费方式：</label>
            <product-type-selector
                value="{=formData.productType=}"
                on-change="onProductTypeChange"
                layout="postpay"
            ></product-type-selector>
        </s-form-item>

        <s-form-item inline label-width="{{100}}" label="可用区：" prop="logicalZone">
            <s-loading s-if="zoneLoading" size="small" loading="{{true}}" />
            <s-tipradio-group
                s-else
                value="{=formData.logicalZone=}"
                radioType="button"
                on-change="onLogicalZoneChange"
            >
                <s-tipradio
                    s-for="item in logicalZoneList"
                    label="{{item.text}}"
                    value="{{item.value}}"
                    content="{{item.disabled ? item.tip : '不支持修改'}}"
                    disabled="{{!!editData || item.disabled}}"
                    isDisabledVisibile="{{true}}"
                />
            </s-tipradio-group>
        </s-form-item>

        <s-form-item inline label-width="{{100}}" label="子网：" prop="subnetId">
            <s-loading s-if="subnetLoading" size="small" loading="{{true}}" />
            <s-select
                s-else
                width="300"
                disabled="{{!!editData}}"
                datasource="{{subnetList}}"
                value="{=formData.subnetId=}"
                on-change="onSubnetChange"
            />
            <s-button class="s-icon-button" s-if="!subnet.loading" on-click="getSubnetList">
                <s-icon-refresh class="button-icon" is-button="{{false}}" />
            </s-button>
            <div class="subnet-tip" s-if="{{formData.subnetId}}">
                <div class="remain-ips">当前子网剩余可用 IP 数：<b>{{subnetRemainIps}}</b>个。</div>
                <div class="cce-cluster-create-subnet">
                    如需创建子网，可以
                    <a href="javascript:void(0)" on-click="onCreateSubnet">去创建子网</a>
                </div>
            </div>
        </s-form-item>
        <s-form-item inline label-width="{{100}}" label="应用类型" prop="appType">
            <product-type-selector
                datasource="{{appTypeDatasource}}"
                value="{=formData.appType=}"
                on-change="onAppTypeChange"
            ></product-type-selector>
        </s-form-item>
        <s-form-item inline label-width="{{100}}" label="性能指标等级" prop="appPerformanceLevel">
            <s-select
                width="300"
                disabled="{{!!editData}}"
                value="{=formData.appPerformanceLevel=}"
                datasource="{{appPerformanceDatasource}}"
                placeholder="请选择性能指标等级"
            >
            </s-select>
        </s-form-item>
        <add-hpas-image-os
            s-ref="hpasImageOs"
            appInfo="{{appInfo}}"
            formData="{=formData=}"
            editData="{{editData || fillData}}"
        />
        <s-form-item label="EHC集群：">
            <s-tip-select
                placeholder="{{defaultLabel[ehcClusterStatus]}}"
                disabled="{{ehcClusterStatus !== 'done' || !!editData}}"
                value="{=formData.ehcClusterId=}"
                width="{{300}}"
                data-track-id="ti_bcc_instance_create_ehccluster"
                data-track-name="ehcCluster"
                on-change="onEhcClusterChange"
            >
                <s-tip-option
                    s-for="item in ehcClusterList"
                    value="{{item.value}}"
                    label="{{item.label}}"
                    disabled="{{!supportBuySoldout && item.disabled}}"
                    content="{{item.tip}}"
                    isDisabledVisibile
                ></s-tip-option>
            </s-tip-select>
            <s-button s-if="!editData" class="s-icon-button mr8" on-click="getEhcClusterList">
                <s-icon-refresh class="button-icon" />
            </s-button>
            <p class="color-gray-light mt4">
                如需创建新的集群，可以去<a target="_blank" href="/bcc/#/ehc/cluster/list"
                    >创建弹性高性能计算集群</a
                >
            </p>
            <p class="color-orange mt4">
                同一EHC集群中的实例可通过RDMA网络高速互联，不同EHC集群中的实例相互隔离，请注意将Pod调度到同一EHC集群中的节点上实现高性能网络通信。
            </p>
        </s-form-item>
    </s-form>
</div> `;

class AddHpasCustom extends Component {
    static components = {
        'add-hpas-image-os': AddHpasImageOs,
        'advanced-config-panel': AdvancedConfigPanel,
        's-form': Form,
        's-form-item': Form.Item,
        's-tipradio': TipRadio,
        's-tipradio-group': TipRadio.RadioGroup,
        's-radio-group': Radio.RadioGroup,
        's-tip-select': TipSelect,
        's-tip-option': TipSelect.Option,
        'ui-radio-select': RadioSelect,
        's-select': Select,
        's-option': Select.Option,
        's-loading': Loading,
        'ui-tip': Tip,
        's-button': Button,
        's-slider': Slider,
        's-tooltip': Tooltip,
        'pay-config': PayConfig,
        's-tip': BceTip,
        's-icon-refresh': OutlinedRefresh,
        'product-type-selector': ProductTypeSelector,
    };

    static template = template;

    static computed = {
        onWorkVPCNIMode() {
            const networkMode = this.data.get('networkMode');
            return !_.isEmpty(NetworkModeCni.fromValue(networkMode));
        },
    };

    static filters = {};

    initData() {
        return {
            onWorkVPCNIMode: false,
            eniInZonesKeys: {},
            defaultLabel: {
                loading: '加载中...',
                done: '请选择',
                none: '暂无数据',
                error: '加载失败',
            },
            allFlavors: {},
            appInfo: {},
            appPerformanceDatasource: [],
            appTypeDatasource: [],
            imageList: [],
            customImages: [],
            formData: {
                productType: PayType.POSTPAY,
                serviceType: 'HPAS',
                logicalZone: '',
            },
            zoneLoading: false,
            logicalZoneList: [],
            subnetLoading: false,
            subnetList: [],
            rules: getRules(this),
        };
    }

    inited() {
        this.initHpasFlavorList();
        const getSubnetRemainingIPHandler = async subnetId => {
            let remainIps = 0;
            if (subnetId) {
                remainIps = await getSubnetRemainingIP(subnetId);
            }
            this.data.set('subnetRemainIps', remainIps);
        };
        this.watch('formData.subnetId', async subnetId => {
            getSubnetRemainingIPHandler(subnetId);
        });
        this.watch('appInfo', val => {
            if (val?.levels?.length) {
                const list = val.levels.map(el => ({label: `每秒 ${el} tokens`, value: el}));
                this.data.set('appPerformanceDatasource', list);
                const defaultLevel = list.find(
                    el => el.value === this.data.get('formData.appPerformanceLevel'),
                );
                this.data.set('formData.appPerformanceLevel', defaultLevel?.value || list[0].value);
            } else {
                this.data.set('formData.appPerformanceLevel', '');
                this.data.set('appPerformanceDatasource', []);
            }
        });
    }

    async attached() {
        this.initFormData();
    }

    async initHpasFlavorList() {
        try {
            this.data.set('zoneLoading', true);
            await this.getOnCNIZones();
            const res = await this.$http.getHpasFlavors({isCoupon: false});
            const eniInZonesKeys = this.data.get('eniInZonesKeys') || {};
            const isEni = !!Object.keys(eniInZonesKeys).length;
            const zoneList = res.result.map(el => {
                return {
                    text: el.zoneName?.replace('zone', '可用区'),
                    value: el.zoneName,
                    disabled: isEni && !eniInZonesKeys[el.zoneName],
                    tip: '该可用区下无容器子网',
                };
            });
            const defaultZone = this.data.get('formData.logicalZone');
            this.data.set('allFlavors', res.result);
            this.data.set('logicalZoneList', zoneList);
            if (!zoneList.some(el => el.value === defaultZone && !el.disabled)) {
                const defaultZone = zoneList.find(el => !el.disabled)?.value || '';
                this.data.set('formData.logicalZone', defaultZone);
            }
            this.getSubnetList();
            this.initAppType();
            this.getEhcClusterList();
            this.data.set('zoneLoading', false);
        } catch (error) {
            this.getSubnetList();
            this.data.set('zoneLoading', false);
        }
    }

    initAppType() {
        const flavorsRes = this.data.get('allFlavors');
        const editData = this.data.get('editData');
        const zoneDs =
            flavorsRes?.find(el => el.zoneName === this.data.get('formData.logicalZone'))
                ?.flavors || [];
        const instanceChargingType =
            this.data.get('formData.productType') === 'prepay' ? 'Prepaid' : 'Postpaid';
        const flavors = zoneDs.filter(el => {
            return el.chargeType === instanceChargingType;
        });

        const appTypeDs = _.groupBy(flavors, 'appType');
        const datasource = Object.keys(appTypeDs).map(key => {
            const items = appTypeDs[key];
            const item = items?.[0];

            const levelGroup = _.groupBy(items, 'appPerformanceLevel');
            const levels = Object.keys(levelGroup) || [];
            return {
                ...item,
                text: item.name,
                value: item.appType,
                desc: item.description,
                disabled: !!editData,
                iconClass: 'apptype',
                levels,
            };
        });
        this.data.set('appTypeDatasource', datasource);
        const defaultAppType = this.data.get('formData.appType');
        const defaultItem = datasource.find(el => el.value === defaultAppType);
        if (datasource[0] && !defaultItem) {
            this.data.set('formData.appType', datasource[0]?.value);
            this.data.set('appInfo', datasource[0]);
        }
        defaultItem && this.data.set('appInfo', defaultItem);
    }

    onAppTypeChange({value}) {
        this.data.set('formData.appType', value);
        const appInfo = this.data.get('appTypeDatasource').find(el => el.value === value);
        this.data.set('appInfo', appInfo);
    }

    async onLogicalZoneChange({value}) {
        this.data.merge('formData', {
            logicalZone: value,
        });
        this.getSubnetList();
        this.initAppType();
        this.getEhcClusterList();
    }

    // 原getSubmitData
    getFormData() {
        const formData = this.data.get('formData');
        const instanceChargingType = formData.productType === 'prepay' ? 'Prepaid' : 'Postpaid';
        return {...formData, instanceChargingType};
    }

    /**
     * 初始化表单数据
     * 主要是需要初始化
     */
    initFormData() {
        const data = this.data.get('editData') || this.data.get('fillData') || {};
        this.data.merge('formData', data);
    }

    // 获取ehc集群列表
    getEhcClusterList() {
        this.data.set('ehcClusterStatus', 'loading');
        this.data.set('ehcClusterList', []);
        this.$http
            .getHpasEhcClusterList()
            .then(({result}) => {
                this.data.set('ehcClusterStatus', 'done');
                const list = result
                    .filter(el => el.zoneName === this.data.get('formData.logicalZone'))
                    .map(item => {
                        return {
                            value: item.ehcClusterId,
                            label: item.name,
                            ...item,
                        };
                    });
                this.data.set('ehcClusterList', list);
                const defaultVal =
                    list.find(i => i.ehcClusterId === this.data.get('formData.ehcClusterId')) ||
                    list.find(i => i.isDefault) ||
                    list[0] ||
                    {};
                this.data.set('formData.ehcClusterId', defaultVal.ehcClusterId);
            })
            .catch(() => {
                this.data.set('ehcClusterStatus', 'error');
            });
    }

    async validateForm() {
        let tasks = [this.ref('form')?.validateFields()];
        return Promise.all(tasks);
    }

    onSubnetChange(e) {
        this.data.merge('formData', {
            subnetId: e ? e.value : '',
        });
        const item = _.find(this.data.get('subnetList'), d => d.value === e.value);
        this.data.set('formData.selectedSubnetItem', item || {});
    }

    getSubnetList() {
        this.data.set('subnetLoading', true);
        return this.$http
            .getSubnetList({
                // 这俩类型复制于hpas代码，bcc/nat
                subnetTypes: [SubnetType.BCC, SubnetType.NAT],
                vpcId: this.data.get('vpcId'),
                zone: this.data.get('formData.logicalZone'),
            })
            .then(res => {
                let list = _.map(res.result || [], item => ({
                    text: getSubnetName(item.name) + (item.cidr ? '（' + item.cidr + '）' : ''),
                    value: item.shortId,
                }));
                if (list.length) {
                    const {vpcSubnetId, subnetId} =
                        this.data.get('editData') || this.data.get('fillData') || {}; // 编辑回显
                    const target = list.find(e => e.value === vpcSubnetId || e.value === subnetId);
                    if (target) {
                        this.data.set('formData.subnetId', target.value);
                        this.data.set('formData.selectedSubnetItem', target);
                    } else {
                        this.data.set('formData.subnetId', list[0].value);
                        this.data.set('formData.selectedSubnetItem', list[0]);
                    }
                } else {
                    this.data.set('formData.subnetId', null);
                    this.data.set('formData.selectedSubnetItem', null);
                }
                this.data.set('subnetList', list);
                this.data.set('subnetLoading', false);
            })
            .catch(() => {
                this.data.set('subnetLoading', false);
                this.data.set('subnetList', []);
            });
    }

    onCreateSubnet() {
        const selectedVpcItem = this.data.get('selectedVpcItem');
        if (selectedVpcItem) {
            const payload = {
                availableService: ['BBC'],
                subnetTypes: [SubnetType.BCC],
                vpcInfo: {
                    name: selectedVpcItem.text,
                    ipv6Cidr: selectedVpcItem.ipv6Cidr,
                    cidr: selectedVpcItem.cidr,
                    auxiliaryCidr: selectedVpcItem.auxiliaryCidr,
                    vpcId: selectedVpcItem.value,
                },
            };
            const dialog = new SubnetCreateDialog({
                sdk: new VpcSDK({
                    client: new HttpClient(),
                    context: window.$context,
                }),
                ...payload,
            });
            dialog.attach(document.body);
            dialog.on('create', () => {
                notification.success('创建子网成功');
                this.getSubnetList();
            });
            dialog.on('cancel', () => dialog && dialog.dispose());
            this.$childs.push(dialog);
        }
    }

    async getOnCNIZones() {
        const onExtendClusterUuid = this.data.get('onExtendClusterUuid');
        if (this.data.get('onWorkVPCNIMode')) {
            this.data.set('eniInZonesKeys', {});
        }
        if (onExtendClusterUuid) {
            return this.getEniSubnets(onExtendClusterUuid).then(eniSubnets => {
                const eniVPCSubnet = eniSubnets.reduce((result, eniSubnet) => {
                    if (result[eniSubnet.availableZone]) {
                        result[eniSubnet.availableZone].push(eniSubnet.subnetID);
                    } else {
                        result[eniSubnet.availableZone] = [eniSubnet.subnetID];
                    }
                    return result;
                }, {});

                this.data.set('eniInZonesKeys', eniVPCSubnet);
            });
        } else {
            const eniVPCSubnet = this.data.get('eniVPCSubnetIDs') || {};

            this.data.set('eniInZonesKeys', eniVPCSubnet);
        }
    }

    getEniSubnets(clusterUuid) {
        return this.$http.getClusterExtraInfoV2(clusterUuid).then(({result}) => {
            return result.eniSubnets || [];
        });
    }

    disposed() {
        _.each(this.$childs, comp => {
            comp.dispose();
        });
        this.$childs = [];
    }
}

export default class AddHpasCustomStore extends connect.san(StoreMap)(AddHpasCustom) {}
