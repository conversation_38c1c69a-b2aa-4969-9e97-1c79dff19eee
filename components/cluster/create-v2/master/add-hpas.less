.cce-cluster-add-hpas {
    .s-form-item-label > label {
        padding-left: 7px;
    }
    .s-form-item-label {
        width: 107px;
    }
    .s-icon-button {
        padding: 0 7px;
        margin-left: 5px;
        svg {
            position: relative;
            top: -1px;
        }
    }
    .subnet-tip {
        display: flex;
        color: #84868c;
        margin-top: 4px;
    }
    .s-select-option-list {
        .s-trigger-container {
            width: 260px;
            padding-right: 16px;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
    .color-orange {
        color: #f38900;
    }
}
