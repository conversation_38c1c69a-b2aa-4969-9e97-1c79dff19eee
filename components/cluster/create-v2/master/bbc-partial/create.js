/**
 * @file 创建部署集
 *
 * <AUTHOR>
 * @created: 2019/08/15
 */

import {Component} from 'san';
import {html, decorators, Enum} from '@baiducloud/runtime';
import {Form, FormItem, TextBox, RadioSelect} from '@baiducloud/bce-ui/san';
import {Button, Dialog, Notification, Tip} from '@baidu/sui';

const DeploysetStrategy = new Enum(
    {alias: 'HOST_HA', text: '宿主机', value: 'HOST_HA'},
    {alias: 'RACK_HA', text: '机架', value: 'RACK_HA'},
    {alias: 'TOR_HA', text: '交换机', value: 'TOR_HA'}
);

const {asComponent, invokeBceSanUI} = decorators;

const template = html`
<template>
    <s-dialog
        open
        title="创建部署集"
        width="500"
        okText="{{confirmButton.label}}"
        confirmButtonDisable="{{confirmButton.disable}}"
        loadingAfterConfirm="{{false}}"
        class="create-deployset-dialog"
        on-confirm="onConfirmDialog"
        on-close="onClose"
    >
        <ui-form s-ref="form" descriptor="{=rules=}" formData="{=formData=}" errors="{=errors=}"
            class="bbc-deployset-create">
            <ui-form-item name="name" label="名称：" label-width="{{labelWidth}}" inline>
                <ui-textbox value="{=formData.name=}"
                    width="{{270}}"
                    on-input="onNameInput"
                    placeholder="请输入名称" />
            </ui-form-item>
            <ui-form-item label="描述：" label-width="{{labelWidth}}" inline>
                <ui-textbox value="{=formData.desc=}"
                    multiline
                    width="{{250}}"
                    height="{{100}}"
                    limit-length="{{140}}"
                    placeholder="请输入描述"
                    class="bui-multi-line-textarea"
                />
            </ui-form-item>
            <ui-form-item class="bbc-deployset-create-item" label="策略：" label-width="{{labelWidth}}">
                <ui-radio-select
                    datasource="{{strategyList}}"
                    value="{=formData.strategy=}"
                />
            </ui-form-item>
            <ui-form-item name="concurrency" s-if="isBcc" class="bbc-deployset-create-item" label="并发上限：" label-width="{{labelWidth}}">
                <ui-textbox
                    width="{{60}}"
                    value="{=formData.concurrency=}"
                />
                <s-tip
                    message="加入该部署集的实例允许在单宿主机/机架/交换机中并发数量的上限，编辑时只能调大"
                    layer-width="200"
                />
            </ui-form-item>
        </ui-form>
    </s-dialog>
</template>
`;

@asComponent('@deployset-create')
@invokeBceSanUI
export default class Create extends Component {
    static template = template

    static components = {
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-textbox': TextBox,
        's-dialog': Dialog,
        'ui-radio-select': RadioSelect,
        's-button': Button,
        's-tip': Tip
    }

    static computed = {
        isBcc() {
            const type = this.data.get('type');
            return type === 'bcc';
        }
    }

    initData() {
        return {
            labelWidth: 80,
            rules: {
                name: [
                    {
                        validator(rule, value, callback) {
                            if (!/^[a-zA-Z][\w\-\/\.]{0,64}$/.test(value)) {
                                return callback('只支持大小写字母、数字和-_ /.特殊字符，必须以字母开头，不超过65个字');
                            }

                            return callback();
                        }
                    }
                ]
            },
            formData: {
                name: '',
                desc: '',
                strategy: DeploysetStrategy.HOST_HA,
                concurrency: ''
            },
            errors: null,
            confirmButton: {
                label: '确定',
                disable: false
            },
            strategyList: DeploysetStrategy.toArray()
        };
    }

    inited() {
        const isBcc = this.data.get('isBcc');
        if (isBcc) {
            this.data.set('rules.concurrency', [{
                validator(rule, value, callback) {
                    if (!/^[1-9][0-9]*$/.test(value)) {
                        return callback('请填写正整数');
                    }

                    return callback();
                }
            }]);
        }
    }

    onClose() {
        this.dispose && this.dispose();
    }

    async onConfirmDialog() {
        await this.ref('form').validateForm();

        const {name, desc, strategy, concurrency} = this.data.get('formData');
        const isBcc = this.data.get('isBcc');
        const payload = {
            name,
            desc,
            strategy
        };

        if (isBcc) {
            payload.concurrency = concurrency;
        }

        this.data.set('confirmButton', {label: '提交中...', disable: true});
        try {
            await isBcc ? this.$http.createBccDeployset(payload) : this.$http.createBbcDeployset(payload);
            Notification.success('部署集创建成功');
            this.fire('success', null);
            this.onClose();
        }
        catch (e) {
            this.data.set('confirmButton', {label: '确定', disable: false});
        }
    }
}
