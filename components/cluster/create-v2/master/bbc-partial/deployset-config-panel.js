/**
 * @file deployset-config.js 部署集
 *
 * <AUTHOR>
 * @created: 2020/04/15
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Select, Form, FormItem, Tip, Button} from '@baiducloud/bce-ui/san';

import operations from './operations';

import './deployset-config-panel.less';

const {asComponent, invokeBceSanUI, invokeAppComp} = decorators;

const tpl = html`
    <template>
        <ui-form class="deployset-config-wrapper">
            <ui-form-item label="部署集：" label-width="{{labelWidth}}" inline>
                <ui-select
                    width="200"
                    id="deployset"
                    datasource="{{deploysetList}}"
                    value="{=formData.deployId=}"
                    on-change="onChangeDeployset"
                />
                <span s-if="{{deploysetQuota === 0}}" class="text-red"
                    >当前部署集可创建实例超过上限</span
                >
                <ui-tip message="{{deploysetTip}}" layer-width="{{320}}" />
                <p class="color-gray-light form-item-bottom-tip ln30">
                    <span>{{deploysetCreateTip}}</span>
                    <ui-button
                        skin="stringfy"
                        class="create-button"
                        on-click="onCreateDeployest"
                        disabled="{{deploysetOverQuota}}"
                        tip="您目前所在地域可创建部署集数已超出限制"
                        >点击创建>></ui-button
                    >
                </p>
            </ui-form-item>
        </ui-form>
    </template>
`;

@asComponent('@deployset-config-panel')
@invokeBceSanUI
@invokeAppComp
export default class BbcInstanceCreateDeploysetConfig extends Component {
    static template = tpl;

    static components = {
        'ui-select': Select,
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-tip': Tip,
        'ui-button': Button,
    };

    initData() {
        return {
            labelWidth: 100,
            deploysetList: [],
            deploysetLength: 0,
            formData: {
                deployId: '',
            },
            deploysetTip:
                '部署集在指定部署集中创建服务器时，会和处于同一部署集中的其他服务器严格按照弹性裸金属服务器打散，' +
                '保障在硬件故障等异常情况下的服务高可用性。',
            deploysetCreateTip: '如需要创建新的部署集，您可以，',
        };
    }

    static computed = {
        deploysetOverQuota() {
            const length = this.data.get('deploysetLength');
            const quota = this.data.get('quota');
            if (quota && length !== 0 && quota.deploySetQuota && quota.deploySetQuota <= length) {
                return true;
            }
            return false;
        },
    };

    loadDeploysetListData(defaultSelect = false) {
        const logicalZone = this.data.get('logicalZone');
        const type = this.data.get('type');
        if (!logicalZone) {
            return;
        }
        const payload = {
            logicalZone,
        };

        return (
            ['bcc', 'BCC', 'EBC', 'ebc'].includes(type)
                ? this.$http.getBccDeploysetAvailableList(payload)
                : this.$http.getBbcDeploysetAvailableList(payload)
        )
            .then(({result}) => {
                let list = _.map(result, item => {
                    const selectItem = {
                        text: item.name,
                        value: item.uuid,
                        deploysetQuota: +item.instanceTotal - +item.instanceCount,
                    };
                    return selectItem;
                });
                this.data.set('deploysetLength', result.length);
                list.unshift({text: '暂不选择', value: ''});
                return list;
            })
            .then(list => {
                this.data.set('deploysetList', list);
                const deploySetID = this.data.get('deploySetID');
                const target = list.find(e => e.value == deploySetID);
                // 创建部署集后默认选择刚创建的部署集
                if (defaultSelect) {
                    list.length > 1 && this.data.set('formData.deployId', list[1].value);
                } else if (target) {
                    // 编辑时传入的deploySetID
                    this.data.set('formData.deployId', target.value);
                }
                // 从部署集列表点击创建实例默认选择该部署集
                else {
                    const deployId = location.hash.split('deployId=');
                    deployId.length > 1 && this.data.set('formData.deployId', deployId[1]);
                }
            });
    }

    resetDeploysetSelected() {
        this.data.set('deploysetQuota', undefined);
        this.data.set('formData.deployId', '');
        this.data.set('deploysetList', []);
    }

    getComponentData() {
        const deploySetID = this.data.get('formData.deployId');
        const deploysetList = this.data.get('deploysetList');
        const selectedItem = _.find(deploysetList, item => item.value === deploySetID);
        return {
            deploySetID,
            deploySetName: selectedItem?.text,
        };
    }

    valid() {
        if (this.data.get('deploysetQuota') === 0) {
            return Promise.reject();
        }
        return Promise.resolve();
    }

    onCreateDeployest() {
        const dialog = operations.createDeployset({self: this, type: this.data.get('type')});
        dialog && this.addChild(dialog);
    }
    onChangeDeployset(e) {
        const deploysetList = this.data.get('deploysetList');
        const item = _.find(deploysetList, item => item.value === e.value);
        this.data.set('deploysetQuota', item ? item.deploysetQuota : item);
    }
    // 创建部署集后调用
    onRefresh() {
        this.resetDeploysetSelected();
        this.loadDeploysetListData(true);
    }

    addChild(com) {
        com.dispose && this.$childs.push(com);
    }

    inited() {
        this.$childs = [];
        this.resetDeploysetSelected();
        this.loadDeploysetListData();
        this.watch('logicalZone', logicalZone => {
            this.resetDeploysetSelected();
            this.loadDeploysetListData();
        });
    }

    disposed() {
        _.each(this.$childs, comp => {
            comp && comp.dispose();
        });
        this.$childs = [];
    }
}
