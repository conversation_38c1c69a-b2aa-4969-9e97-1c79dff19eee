/**
 * @file operations.js
 *
 * <AUTHOR>
 * @created: 2019/08/15
 */

import _ from 'lodash';

import Create from './create';
import './create.less';

export default {
    createDeployset(params) {
        const dialog = new Create({
            data: {
                type: params.type
            }
        });
        dialog.on('success', () => {
            params.self.onRefresh();
        });
        dialog.attach(document.body);
        return dialog;
    },
};
