/**
 * @file components/cluster/create-v2/master/eip.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {EipConfigPanel} from '@baidu/bce-eip-sdk-san';

import {PayType} from '../../../../utils/enums';

const {asComponent, invokeBceSanUI} = decorators;
const $flag = ServiceFactory.resolve('$flag');

const template = html`<div>
    <x-eip
        s-ref="eip"
        service-type="{{serviceType}}"
        type="{{type}}"
        product-type="${PayType.POSTPAY}"
        if-buy-eip="{{ifBuyEip}}"
        formData="{{formData}}"
        subProductType="{{subProductType}}"
        bandwidthInMbps="{{bandwidthInMbps}}"
        allNetwork="{{allNetwork}}"
        on-ifBuyEipChange="onIfBuyEipChange"
        labelWidth="{{labelWidth}}"
    />
</div> `;

@asComponent('@cce-cluster-create-eip')
@invokeBceSanUI
export default class CustomEip extends Component {
    static components = {
        'x-eip': EipConfigPanel,
    };

    static template = template;

    static computed = {
        type() {
            const inBosWhite = this.data.get('inBosWhite');
            return inBosWhite ? 'bos' : PayType.POSTPAY;
        },
    };

    initData() {
        return {
            ifBuyEip: '0',
            serviceType: 'CCE',
            labelWidth: 100,
            formData: {
                name: '',
            },
        };
    }

    attached() {
        this.getNetwork();
        this.getBosWhiteList();
    }

    getNetwork() {
        return this.$http
            .eipMaxBandQuota([
                'eipBandwidthQuotaByBand',
                'eipBandwidthQuotaByTraffic',
                'staticEipBandwidthQuotaByBand',
                'staticEipBandwidthQuotaByTraffic',
                'eipInBandwidthQuota',
            ])
            .then(({result}) => result.quotaType2quota)
            .then(quotaType2quota => {
                let networkQuota = quotaType2quota.eipBandwidthQuotaByBand
                    ? quotaType2quota.eipBandwidthQuotaByBand
                    : 200;
                let networkTraffic = quotaType2quota.eipBandwidthQuotaByTraffic
                    ? quotaType2quota.eipBandwidthQuotaByTraffic
                    : 1000;
                let allNetwork = {
                    networkQuotaDynamic: Number(networkQuota),
                    networkTrafficDynamic: Number(networkTraffic),
                };
                this.data.set('allNetwork', allNetwork);
            });
    }

    getBosWhiteList() {
        if (!$flag.CceClusterCreateEipBos) {
            this.data.set('inBosWhite', false);
            return;
        }

        return this.$http
            .combine95WhiteList()
            .then(({result}) => this.data.set('inBosWhite', result));
    }

    validateForm() {
        const eip = this.ref('eip');
        return eip.valid();
    }

    getFormData() {
        let eipData = this.ref('eip').getConfig();
        const serviceType = this.data.get('serviceType');
        if (!+eipData.ifBuyEip && serviceType !== 'EIP') {
            eipData = {};
        }
        return eipData;
    }

    onIfBuyEipChange(e) {
        this.fire('ifBuyEipChange', e);
    }
}
