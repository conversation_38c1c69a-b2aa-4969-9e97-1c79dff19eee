/**
 * @file components/cluster/create-v2/master/add-hpas.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Tooltip, Form, Select, Button, Radio} from '@baidu/sui';
import './add-hpas.less';

const imageRadioText = {
    Custom: '自定义镜像',
    System: '公共镜像',
};

const template = html`<div class="cce-cluster-hpas-os">
    <!-- 镜像类型 -->
    <s-form-item label="镜像类型：" prop="imageType" s-if="imageTypeList.length">
        <s-radio-group
            value="{=formData.imageType=}"
            datasource="{{imageTypeList}}"
            radioType="button"
            on-change="onImageTypeChange"
        />
    </s-form-item>

    <!-- 操作系统 -->
    <s-form-item label="操作系统：" prop="osType" s-if="imageTypeList.length">
        <s-select
            width="300"
            placeholder="请选择操作系统："
            on-change="onHpasOsTypeChange"
            value="{=formData.osType=}"
        >
            <s-option
                s-for="item in osTypeList"
                label="{{item.label}}"
                value="{{item.value}}"
                disabled="{{item.disabled}}"
            >
                <s-tooltip placement="right">
                    <div slot="content">{{item.label}}</div>
                    {{item.label}}
                </s-tooltip>
            </s-option>
        </s-select>
        <div class="os-tip" s-if="{{formData.imageType === 'System'}}">
            若你需要挂载极速型L2并行文件存储（PFS），建议选择CentOS或者Ubuntu操作系统，<a
                href="https://cloud.baidu.com/doc/PFS/s/olzz6s5bi"
                target="_blank"
                >详细说明</a
            >。
        </div>
    </s-form-item>
</div> `;

export default class AddHpasImageOs extends Component {
    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-select': Select,
        's-option': Select.Option,
        's-button': Button,
        's-tooltip': Tooltip,
    };

    static template = template;

    static computed = {
        osTypeList() {
            const osTypeData = this.data.get('osTypeData') || {};
            const imageType = this.data.get('formData.imageType');
            return osTypeData?.[imageType] || [];
        },
    };

    static filters = {};

    initData() {
        return {
            appInfo: {},
            imageTypeList: [],
            osTypeData: {},
            imageList: [],
            customImages: [],
            formData: {
                imageType: '',
                osType: '',
                osName: '',
                osVersion: '',
            },
        };
    }

    inited() {
        this.getImageAndOs();
        this.watch('appInfo', val => {
            this.initImages();
        });
    }

    async attached() {
        // this.initFormData();
    }

    onImageTypeChange({value}) {
        this.data.set('formData.imageType', value);
        this.data.set('formData.osType', '');
        this.data.set('formData.osName', '');
    }

    onHpasOsTypeChange({value}) {
        this.data.set('formData.osType', value);
        const osTypeList = this.data.get('osTypeList') || [];
        const label = osTypeList.find(item => item.value === value)?.label;
        this.data.set('formData.osName', label);
    }
    getFormData() {
        const formData = this.data.get('formData');
        const {imageType, osType, osName, osVersion} = formData;
        return {imageType, osType, osName, osVersion};
    }

    /**
     * 初始化表单数据
     * 主要是需要初始化
     */
    initFormData() {
        const data = this.data.get('editData') || {};
        this.data.merge('formData', data);
    }

    getImageAndOs() {
        return Promise.all([
            this.$http.getHpasImageList(),
            this.$http.getHpasCustomImages({appType: ''}),
        ]).then(res => {
            const [imageList, customImages] = res;
            this.data.set('imageList', imageList.result);
            let images = customImages?.page?.result || [];
            images = _.filter(images, image => image.imageStatus === 'Available');
            this.data.set('customImages', images);
            this.initImages();
        });
    }

    // 获取appType对应的自定义镜像
    getAppTypeCustomImages(appType) {
        const {customImages} = this.data.get('');
        let images = [];
        appType &&
            _.forEach(customImages, image => {
                if (image.supportedAppType.includes(appType)) {
                    images.push(image);
                }
            });
        return images;
    }

    initImages() {
        const {imageList, appInfo} = this.data.get('');
        if (!imageList?.length || !appInfo?.appImageFamily) {
            return;
        }
        let imagesDs =
            imageList?.find(el => el.appImageFamily === appInfo?.appImageFamily)?.imageList || [];
        const ds = {
            System: [],
            Custom: [],
        };
        // 自定义镜像从image/custom/list获取，从image/list中过滤掉
        imagesDs = imagesDs.filter(el => el.imageType !== 'Custom');
        const coustomImages = this.getAppTypeCustomImages(appInfo?.appType);
        imagesDs = imagesDs.concat(coustomImages);
        imagesDs.forEach(el => {
            ds[el.imageType].push({value: el.imageId, label: `${el.name}/${el.imageId}`});
        });
        const imageRadioArray = [];
        Object.keys(ds).forEach(el => {
            if (ds[el].length > 0) {
                imageRadioArray.push({
                    value: el,
                    text: imageRadioText[el],
                });
            }
        });
        this.data.set('imageTypeList', imageRadioArray);
        const defaultImageType = imageRadioArray.find(
            el => el.value === this.data.get('formData.imageType'),
        );

        if (imageRadioArray.length > 0) {
            let imageType = defaultImageType?.value || imageRadioArray[0].value;
            this.data.set('formData.imageType', imageType);
            const image = this.data.get('formData.osType');
            if (!ds[imageType].find(el => el.value === image)) {
                this.data.set('formData.osType', ds[imageType]?.[0]?.value);
                this.data.set('formData.osName', ds[imageType]?.[0]?.label);
            }
        } else {
            this.data.set('formData.osType', '');
            this.data.set('formData.imageType', '');
        }
        this.data.set('osTypeData', ds);
    }

    async validateForm() {
        let tasks = [this.ref('form')?.validateFields()];
        return Promise.all(tasks);
    }

    disposed() {
        _.each(this.$childs, comp => {
            comp.dispose();
        });
        this.$childs = [];
    }
}
