/**
 * @file components/cluster/create-v2/master/index.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';

import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {ClusterType} from '../../../../utils/enums';
import {MasterType, ClusterHA} from '../enums';
import tips from '../../../../utils/tips';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;
const $flag = ServiceFactory.resolve('$flag');

const Schema = AsyncValidator.default;

const rules = new Schema({});

const template = html`<template>
    <ui-biz-legend
        label="{{title}}"
        class="cce-create-cluster-master-config"
        style="display: {{show ? 'block' : 'none'}}"
    >
        <ui-form
            s-ref="form"
            rules="{{rules}}"
            form-data="{=formData=}"
            form-errors="{{formErrors}}"
        >
            <ui-form-item
                inline
                label="Master类型："
                name="masterType"
                help="{{masterTypeMessage}}"
            >
                <ui-radio-select
                    box-type="checkbox"
                    datasource="{{types}}"
                    value="{=formData.masterType=}"
                    on-change="onMasterTypeChange"
                />
            </ui-form-item>

            <ui-form-item
                inline
                label="Master副本："
                name="clusterHA"
                s-if="!isManaged"
                help="{{clusterHAMessage}}"
            >
                <ui-radio-select
                    box-type="checkbox"
                    datasource="{{ha}}"
                    value="{=formData.clusterHA=}"
                    on-change="onClusterHAChange"
                />
            </ui-form-item>
            <ui-form-item inline label="VPC网络：" s-if="!isManaged">
                <span class="cluster-create-text-line"> {{selectedVpcItem.text}} </span>
            </ui-form-item>

            <ui-form-item
                inline
                label="Master安全组："
                s-if="!isManaged"
                help="${tips.cluster.securityGroup}"
            >
                <cee-cluster-security-group
                    s-ref="security-group"
                    type="Master"
                    exposedPublic="{{exposedPublic}}"
                    vpcId="{{selectedVpcItem.shortId}}"
                    vpcOriginId="{{selectedVpcItem.vpcId}}"
                />
            </ui-form-item>

            <ui-form-item inline label="节点配置：" s-if="!isManaged" required>
                <cce-cluster-create-node
                    s-ref="master"
                    node-type="Master"
                    on-add="onAdd"
                    on-delete="onDelete"
                />
                <div class="cluster-create-node-invalid-label" s-if="error">
                    节点个数请与副本数保持一致
                </div>
            </ui-form-item>

            <advanced-config-container s-if="!isManaged" open="{=advancedOpen=}">
                <ui-form-item
                    inline
                    label="etcd数据目录："
                    s-if="!isManaged"
                    help="etcd数据存储目录；若已挂载数据盘，建议存储的数据盘"
                >
                    <ui-text-box value="{=formData.etcdDataPath=}" />
                </ui-form-item>

                <cce-data-root s-ref="dataRoot" runtimeType="{{runtimeType}}" />

                <cce-cluster-create-script s-ref="script" hideAutoCordon />

                <cce-flex-tag
                    s-if="!$flag.CceClusterCreateDefaultTags"
                    class="tags-hidden-border"
                    hiddenK8s="{{true}}"
                    s-ref="flexTag"
                />
            </advanced-config-container>
        </ui-form>
    </ui-biz-legend>
</template>`;

@invokeBceSanUI
@invokeComp(
    '@cce-cluster-create-node',
    '@cce-cluster-create-script',
    '@cce-flex-tag',
    '@cee-cluster-security-group',
    '@cce-data-root',
)
class Master extends Component {
    static template = template;

    static components = {
        'advanced-config-container': AdvancedConfigContainer,
    };

    static computed = {
        isManaged() {
            const type = this.data.get('formData.masterType');
            return type === MasterType.MANAGEDPRO;
        },
        clusterHAMessage() {
            const clusterHA = this.data.get('formData.clusterHA');
            const config = ClusterHA.fromValue(clusterHA);
            return config ? config.message : '';
        },
        masterTypeMessage() {
            const type = this.data.get('formData.masterType');
            const config = MasterType.fromValue(type);
            return config ? config.message : '';
        },
    };

    initData() {
        return {
            title: 'Master配置',
            formData: {
                clusterHA: ClusterHA.HA3,
                masterType: MasterType.MANAGEDPRO,
                etcdDataPath: '/home/<USER>/etcd',
            },
            formErrors: null,
            rules,
            ha: ClusterHA.toArray(),
            types: [],
            advancedOpen: false,
            $flag,
        };
    }

    inited() {
        const masterType = this.data.get('masterType');
        if (!masterType) {
            store.dispatch('setMasterType', MasterType.MANAGEDPRO);
        } else {
            this.data.set('formData.masterType', masterType);
        }

        const clusterHA = this.data.get('clusterHA');
        // 只有入门测试集群设置了clusterHA是1
        if (!clusterHA) {
            store.dispatch('setClusterHA', ClusterHA.HA3);
            this.data.set('ha', ClusterHA.toArray('HA3', 'HA5'));
        } else {
            this.data.set('formData.clusterHA', clusterHA);
        }

        const clusterType = this.data.get('clusterType');
        if (
            !$flag.CceCreateManagedMasterCluster ||
            (clusterType && clusterType === ClusterType.CLOUD_EDGE)
        ) {
            this.data.set('formData.masterType', MasterType.CUSTOM);
            this.data.set('types', MasterType.toArray('CUSTOM'));

            store.dispatch('setMasterType', MasterType.CUSTOM);
        } else {
            this.data.set('types', MasterType.toArray());
        }

        if (clusterType === ClusterType.ARM) {
            this.data.set('formData.masterType', MasterType.CUSTOM);
            this.data.set(
                'types',
                MasterType.toArray().map(e => ({
                    ...e,
                    disabled: e.value === MasterType.MANAGEDPRO,
                    tip: e.value === MasterType.MANAGEDPRO ? '暂不支持' : '',
                })),
            );
            store.dispatch('setMasterType', MasterType.CUSTOM);
        }

        this.watch('show', show => {
            if (show) {
                store.dispatch('queryBlbPrice', this.data.get('formData.masterType'));
            }
        });
    }

    getFormData() {
        const formData = this.data.get('formData');
        const runtimeType = this.data.get('runtimeType');
        const runtimeVersion = this.data.get('runtimeType');
        const master = this.ref('master');
        const script = this.ref('script');
        const flexTag = this.ref('flexTag');
        const bccTags = flexTag ? flexTag.getFormData().bccTags : [];
        const relationTag = flexTag ? flexTag.getFormData().relationTag : false;
        const securityGroup = this.ref('security-group');
        const dataRoot = this.ref('dataRoot') && this.ref('dataRoot').getFormData();
        return _.extend({}, formData, {
            masters: master ? master.getFormData() : {},
            masterAdvanced: _.extend(
                {},
                script ? script.getFormData() : {},
                {tags: bccTags},
                {relationTag},
                {securityGroup: securityGroup && securityGroup.getFormData()},
                dataRoot,
                {runtimeType},
                {runtimeVersion},
            ),
        });
    }

    onAdd(data) {
        this.fire('node-change', {
            type: 'master',
            data,
        });
        this.data.set('error', false);
    }

    onDelete(data) {
        this.fire('node-change', {
            type: 'master',
            data,
        });
        this.data.set('error', false);
    }

    validateForm() {
        this.data.set('error', false);

        const master = this.ref('master');
        const masterPromise = master
            ? master.validateForm().then(() => {
                  const formData = this.data.get('formData');
                  if (formData.clusterHA !== master.getInstanceCount()) {
                      this.data.set('error', true);
                      return Promise.reject();
                  }

                  return Promise.resolve();
              })
            : Promise.resolve();

        const flexTag = this.ref('flexTag');
        const flexPromise = flexTag
            ? flexTag.validateForm().catch(() => {
                  const advancedOpen = this.data.get('advancedOpen');
                  if (!advancedOpen) {
                      this.data.set('advancedOpen', true);
                  }

                  return Promise.reject();
              })
            : Promise.resolve();

        const securityGroup = this.ref('security-group');
        const securityGroupPromise = securityGroup && securityGroup.validateForm();
        const dataRootPromise = this.ref('dataRoot')?.validateForm() || Promise.resolve();

        return Promise.all([masterPromise, flexPromise, securityGroupPromise, dataRootPromise]);
    }

    onClusterHAChange(e) {
        store.dispatch('setClusterHA', e.value);
    }

    onMasterTypeChange(e) {
        store.dispatch('setMasterType', e.value);
        store.dispatch('queryBlbPrice', e.value);
        store.dispatch('removeMasterCustomInstances');
        store.dispatch('clearMasterSelectedCustomInstances');
        store.dispatch('queryBccPrice');
        if (e.value === MasterType.MANAGEDPRO) {
            store.dispatch('setClusterHA', null);
        } else {
            store.dispatch('setClusterHA', this.data.get('formData.clusterHA'));
        }
    }
}

@asComponent('@cce-cluster-create-master')
export default class MasterStore extends connect.san(StoreMap)(Master) {}
