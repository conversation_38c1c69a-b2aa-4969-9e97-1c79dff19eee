/**
 * @file components/cluster/create-v2/master/node.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Arch, Classify, DiskType} from '@baidu/bce-bcc-sdk-enum';
import {connect, store} from 'san-store';
import {Alert} from '@baidu/sui';
import {PayType, ClusterType, BccImageType} from '../../../../utils/enums';
import {getSpecGroupText} from '../../../../utils/helper';
import {getResourceConfig, getRegionInfo} from '../../../../utils/bec-flavor';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {
    NodeType,
    UserPassType,
    EipType,
    CreateType,
    BecNodeType,
    BbcFlavorType,
    BbcImageType,
    NetworkModeCni,
    ExistNodeType,
    ExistHPASNodeType,
    BmType,
    BecExistNodeType,
} from '../enums';
import AddDialog from './add-dialog';

const {asComponent, invokeBceSanUI} = decorators;

/* eslint-disable */
const template = html`<div>
    <div class="cluster-create-node">
        <div s-if="showAddNodeBtn" class="create-button-wrap">
            <ui-button label="添加配置" skin="primary" icon="plus" on-click="addNode()" />
            <span
                class="node-config"
                s-if="nodeType === 'Master' && (existCount + customCount) < (clusterHA || 0)"
            >
                <span
                    >推荐Master配置：实例规格<span class="cce-tip-warn">4核8GB</span> 系统盘<span
                        class="cce-tip-warn"
                        >SSD100GB</span
                    >。</span
                >
                <span
                    >如果集群规模较大，建议选择更高配置，也可<a
                        href="{{ticketUrl}}/?_=1615877384362#/ticket/create~productId=110"
                        target="_blank"
                        >工单咨询</a
                    ></span
                >
            </span>
            <s-alert s-if="!isARM && nodeType === 'Worker'" skin="info" showIcon="{{false}}">
                <div slot="description">
                    温馨提示：<br />
                    1、请确保加入集群的GPU实例安装相应的GPU驱动，否则GPU资源无法正常使用。<br />
                    2、创建节点系统盘默认值为100GB。对于AI大模型等数据密集型应用，可能需要更大的存储空间。请考虑容器镜像和运行时数据对磁盘空间的需求，根据实际情况选择增加系统盘大小或使用额外的数据盘。
                </div>
            </s-alert>
        </div>
        <div s-for="item, index in existData">
            <div
                class="cluster-create-node-item{{item.invalid ? ' cluster-create-node-item-invalid' : ''}}"
            >
                <div class="cluster-create-node-item-title">
                    <span>已有节点配置{{index + 1}}</span>
                    <span class="operations">
                        <!-- <a href="javascript:void(0)" on-click="onExistEdit(index)">编辑</a>-->
                        &nbsp;&nbsp;
                        <a href="javascript:void(0)" on-click="onExistDelete(index)">删除</a>
                    </span>
                </div>
                <div class="cluster-create-node-item-list">
                    <span class="item-col">
                        <label class="item-label">节点类型：</label>
                        <span class="item-content">{{item.nodeType | nodeTypeText}}</span>
                    </span>
                    <span
                        class="item-col"
                        s-if="item.nodeType === NodeType.BBC || item.nodeType === NodeType.EBC"
                    >
                        <label class="item-label">重装操作系统：</label>
                        <span class="item-content">{{item.rebuild | rebuildText}}</span>
                    </span>
                    <span
                        class="item-col"
                        s-if="{{(item.nodeType === NodeType.BCC || item.nodeType === NodeType.EBC)  && item.rebuild}}"
                    >
                        <label class="item-label">操作系统：</label>
                        <span
                            class="item-content"
                            title="{{item.osType}}&nbsp;{{item.osVersionText}}"
                        >
                            {{item.osType}}&nbsp;{{item.osVersionText}}
                        </span>
                    </span>
                    <span class="item-col col-full" s-if="item.rebuild">
                        <label class="item-label">登录方式：</label>
                        <span class="item-content">{{item.adminPassType | adminPassTypeText}}</span>
                    </span>
                    <span class="item-col col-full">
                        <label class="item-label">节点列表：</label>
                        <div class="item-content full-item-content">
                            <ui-table
                                s-if="item.nodeType === 'BEC' || item.nodeType === 'BEC_EBC'"
                                schema="{{becTable.schema}}"
                                datasource="{{item.instanceList}}"
                                cell-builder="{{becTable.cellRenderer}}"
                            >
                            </ui-table>

                            <ui-table
                                s-else
                                schema="{{table.schema}}"
                                datasource="{{item.instanceList}}"
                                cell-builder="{{table.cellRenderer}}"
                            >
                            </ui-table>
                        </div>
                    </span>
                </div>
            </div>
            <div s-if="item.invalid" class="cluster-create-node-invalid-label">
                {{item.invalid}}
            </div>
        </div>
        <div s-for="item, index in customData">
            <div
                class="cluster-create-node-item{{item.invalid ? ' cluster-create-node-item-invalid' : ''}}"
            >
                <div class="cluster-create-custom-item-title">
                    <span>新建节点配置{{index + 1}}</span>
                    <span class="operations">
                        <a href="javascript:void(0)" on-click="onCustomDelete(index)">删除</a>
                    </span>
                </div>
                <div class="cluster-create-custom-item-list">
                    <span class="item-col">
                        <label class="item-label">节点创建方式：</label>
                        <span class="item-content">新建节点</span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">付费方式：</label>
                        <span class="item-content">{{item.productType | productTypeText}}</span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">可用区：</label>
                        <span class="item-content">{{item.logicalZone | logicalZoneText}}</span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">节点子网：</label>
                        <span class="item-content">{{item.selectedSubnetItem.text}}</span>
                    </span>
                    <span class="item-col" s-if="item.framework">
                        <label class="item-label">架构：</label>
                        <span class="item-content">{{item.framework | frameworkText}}</span>
                    </span>
                    <span class="item-col" s-if="item.classify">
                        <label class="item-label">分类：</label>
                        <span class="item-content">{{item.classify | classifyText}}</span>
                    </span>
                    <div class="item-col col-full" s-if="item.bcc && item.bcc.selectedFlavor">
                        <label class="item-label">规格：</label>
                        <div class="item-content full-item-content">
                            <ui-table
                                schema="{{customTable.schema}}"
                                datasource="{{[item.bcc.selectedFlavor]}}"
                                cell-builder="{{customTable.cellRenderer}}"
                            >
                            </ui-table>
                        </div>
                    </div>
                    <div class="item-col col-full" s-elif="item.bbc && item.bbc.flavor">
                        <label class="item-label">类型：</label>
                        <span class="item-content">{{item.bbc.type | typeText}}</span>
                        <div class="item-content full-item-content">
                            <ui-table
                                schema="{{bbcCustomTable.schema}}"
                                datasource="{{[item.bbc.flavor]}}"
                                cell-builder="{{bbcCustomTable.cellRenderer}}"
                            >
                                <div slot="c-disk">
                                    <div s-if="{{row.ssdInfo}}">SSD盘 {{row.ssdInfo}}</div>
                                    <div s-if="{{row.sataInfo}}">SATA盘 {{row.sataInfo}}</div>
                                    <div s-if="{{row.sasInfo}}">SAS盘 {{row.sasInfo}}</div>
                                    <div s-if="{{row.hddInfo}}">HDD盘 {{row.hddInfo}}</div>
                                </div>

                                <div slot="c-cpuGhz">
                                    <div>{{row.cpuGhz || '-'}} GHz</div>
                                </div>
                            </ui-table>
                        </div>
                    </div>
                    <span class="item-col">
                        <label class="item-label">镜像类型：</label>
                        <span class="item-content">{{item | imageTypeText}}</span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">操作系统：</label>
                        <span class="item-content">{{item | osText}}</span>
                    </span>
                    <span class="item-col" s-if="item.bbc && item.bbc.raid">
                        <label class="item-label">Raid参数：</label>
                        <span class="item-content">{{item.bbc.raid}}</span>
                    </span>
                    <span class="item-col" s-if="item.serviceType === 'BCC' &&  item.cds">
                        <label class="item-label">系统盘：</label>
                        <span class="item-content" s-if="item.cds.rootDiskStorageType">
                            {{item.cds.rootDiskStorageType | rootDiskStorageTypeText}}&nbsp;
                            {{item.cds.rootDiskSizeInGb}}GB
                        </span>
                        <span class="item-content" s-elif="item.cds.sysDiskSize">
                            {{item.cds.sysDiskSize}}GB
                        </span>
                    </span>
                    <span
                        class="item-col"
                        s-if="item.serviceType === 'BCC' && item.cds.ebsSize && item.cds.ebsSize.length"
                    >
                        <label class="item-label">CDS云磁盘：</label>
                        <span class="item-content"> {{item.cds.ebsSize | ebsSizeText}} </span>
                    </span>
                    <span class="item-col" s-if="item.eip && item.eip.ifBuyEip === '1'">
                        <label class="item-label">公网IP：</label>
                        <span class="item-content"> {{item.eip | eipText}} </span>
                    </span>
                    <span class="item-col" s-if="item.selectedSecurityItem">
                        <label class="item-label">安全组：</label>
                        <span class="item-content">
                            {{item.selectedSecurityItem ? item.selectedSecurityItem.text : '-'}}
                        </span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">登录方式：</label>
                        <span class="item-content">
                            {{item.adminPassType | adminPassTypeText}}
                        </span>
                    </span>
                    <span class="item-col">
                        <label class="item-label">节点数量：</label>
                        <span class="item-content"> {{item.purchaseNum}} </span>
                    </span>
                    <span class="item-col" s-if="item.deploySetID">
                        <label class="item-label">部署集：</label>
                        <span class="item-content"> {{item.deploySetName}} </span>
                    </span>
                </div>
            </div>
            <div s-if="item.invalid" class="cluster-create-node-invalid-label">
                {{item.invalid}}
            </div>
        </div>
        <div class="cluster-create-node-invalid-label" s-if="error">请选择节点</div>
    </div>
</div> `;
/* eslint-enable */

@invokeBceSanUI
class Node extends Component {
    static template = template;

    static components = {
        's-alert': Alert,
    };

    static computed = {
        existCount() {
            const existData = this.data.get('existData');
            let count = 0;
            _.each(_.pluck(existData, 'instanceList'), item => {
                count += item.length;
            });

            return count;
        },
        customCount() {
            const customData = this.data.get('customData');
            let count = 0;
            _.each(customData, item => {
                count += item.purchaseNum;
            });

            return count;
        },
        isARM() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.ARM;
        },
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },
        showAddNodeBtn() {
            const nodeType = this.data.get('nodeType') || 0;
            const existCount = this.data.get('existCount') || 0;
            const customCount = this.data.get('customCount') || 0;
            const clusterHA = this.data.get('clusterHA') || 0;
            if (nodeType === 'Master') {
                return !!(existCount + customCount < clusterHA);
            } else if (nodeType === 'Worker') {
                if (this.data.get('isExpand')) {
                    return !!(existCount + customCount <= 0);
                } else {
                    return true;
                }
            } else {
                return true;
            }
        },
    };

    static filters = {
        nodeTypeText(value) {
            return (
                NodeType.getTextFromValue(value) ||
                BecNodeType.getTextFromValue(value) ||
                ExistHPASNodeType.getTextFromValue(value) ||
                BecExistNodeType.getTextFromValue(value) ||
                value
            );
        },
        rebuildText(value) {
            return value ? '是' : '否';
        },
        productTypeText(value) {
            return PayType.getTextFromValue(value);
        },
        logicalZoneText(value) {
            return window.$zone.getLabel(value);
        },
        frameworkText(value) {
            return Arch.getTextFromValue(value);
        },
        classifyText(value) {
            return Classify.getTextFromValue(value);
        },
        imageTypeText(item) {
            if (item.bcc) {
                return BccImageType.getTextFromValue(item.bcc.imageType);
            }
            if (item.bbc) {
                return BbcImageType.getTextFromValue(item.bbc.imageType);
            }
            return BccImageType.getTextFromValue(item.imageType);
        },
        rootDiskStorageTypeText(value) {
            return DiskType.getTextFromValue(value);
        },
        ebsSizeText(value) {
            let text = '';
            _.each(value, item => {
                text += DiskType.getTextFromValue(item.storageType) + ' ' + item.size + 'GB';
            });
            return text;
        },
        eipText(value) {
            if (_.isEmpty(value)) {
                return '';
            }
            let type = null;
            const payConfig = this.data.get('payConfig');
            if (payConfig?.payType === 'prepay') {
                type = EipType.getTextFromValue(EipType.PREPAY);
            } else {
                if (value?.ifBuyEip === '1') {
                    if (value.subProductType) {
                        type = EipType.getTextFromValue(value.subProductType);
                    }
                } else {
                    return '-';
                }
            }
            return type + '（' + (value.bandwidthInMbps || '-') + 'Mbps）';
        },
        adminPassTypeText(value) {
            return UserPassType.getTextFromValue(value);
        },
        typeText(value) {
            return BbcFlavorType.getTextFromValue(value);
        },
        osText(item) {
            const {osName, osVersion} = item.bcc || item.bbc;
            return `${osName} ${osVersion}`;
        },
    };

    initData() {
        return {
            table: {
                schema: [
                    {
                        name: 'instanceId',
                        label: '实例ID',
                        width: 100,
                    },
                    {
                        name: 'name',
                        label: '实例名称',
                        width: 90,
                    },
                    {
                        name: 'configuration',
                        label: '实例配置',
                        width: 110,
                    },
                    {
                        name: 'payment',
                        label: '支付方式',
                        width: 60,
                    },
                    {
                        name: 'logicalZone',
                        label: '可用区',
                        width: 60,
                    },
                    {
                        name: 'internalIp',
                        label: '内网IP',
                        width: 60,
                    },
                    {
                        name: 'cds',
                        label: '数据磁盘',
                        width: 60,
                    },
                ],
                cellRenderer: (item, key, col, row) => {
                    const nodeType = this.data.get('existData')?.[0]?.nodeType;
                    switch (key) {
                        case 'logicalZone':
                            return window.$zone.getLabel(item.logicalZone);
                        case 'configuration':
                            if (nodeType === ExistHPASNodeType.HPAS) {
                                return `${item.appType || '-'}/${item.appPerformanceLevel || '-'}`;
                            }
                            return `${item.cpu}核/${item.memory}GB/${item.sysDisk}GB`;
                        case 'cds': {
                            if (!item.volumeId) {
                                return '-';
                            }
                            const cdsItem = _.find(
                                item.cdsList,
                                data => data.volumeId === item.volumeId,
                            );
                            if (cdsItem) {
                                return `${cdsItem.volumeId}(${cdsItem.size}GB)`;
                            }
                            return '-';
                        }
                        default:
                            break;
                    }
                    return _.escape(item[key]) || '-';
                },
            },
            becTable: {
                schema: [
                    {
                        name: 'vmId',
                        label: '实例ID',
                    },
                    {
                        name: 'vmName',
                        label: '实例名称',
                    },
                    {
                        name: 'configuration',
                        label: '配置(CPU/内存/系统盘/数据盘/GPU)',
                    },
                    {
                        name: 'region',
                        label: '边缘站点',
                    },
                    {
                        name: 'serviceId',
                        label: '实例组',
                    },
                    {
                        name: 'internalIp',
                        label: '内网IP',
                    },
                ],
                cellRenderer(item, key) {
                    switch (key) {
                        case 'vmId':
                            return `${item.vmId || item.bmId || '-'}`;
                        case 'vmName':
                            return `${item.vmName || item.name || '-'}`;
                        case 'configuration':
                            if (item.bmId) {
                                const diskList = item.diskList || [];
                                let total = 0;
                                diskList.forEach(item => (total += item.sizeInGb));
                                return `${item.cpu || '-'}核/${item.memory || '-'}GB/${
                                    item.systemDisk.sizeInGb
                                }GB/${total}GB/${item.gpu || '-'}`;
                            }
                            return getResourceConfig(
                                item,
                                'cpu',
                                'mem',
                                'rootDiskSize',
                                'dataStorage',
                                'gpu',
                            );
                        case 'region':
                            if (!item) {
                                return;
                            }
                            const provider = [
                                {
                                    city: item.city,
                                    region: item.region,
                                    replicas: 1,
                                    serviceProvider: item.serviceProvider,
                                },
                            ];
                            return item && getRegionInfo(provider);
                        case 'serviceId':
                            return `${item.serviceId || '-'}`;
                        case 'internalIp':
                            if (!item) {
                                return '-';
                            }
                            if (item.internalIp) {
                                return item.internalIp;
                            }
                            if (item.bmType === BmType.BBC1) {
                                return item.internalIp || '-';
                            }
                            const iplist = item.iplist || [];
                            const intranetip = iplist.filter(
                                item => item.isp === 'intranet' || item.isp === 'intra',
                            );
                            return intranetip.length > 0 ? intranetip[0].ip : '-';
                        default:
                            break;
                    }

                    return _.escape(item[key]);
                },
            },
            customTable: {
                schema: [
                    {
                        name: 'specName',
                        label: '规格族',
                    },
                    {
                        name: 'flavorId',
                        label: '实例规格',
                    },
                    {
                        name: 'cpu',
                        label: 'CPU',
                    },
                    {
                        name: 'memory',
                        label: '内存',
                    },
                    {
                        name: 'cpuModel',
                        label: '处理器型号',
                    },
                ],
                cellRenderer(item, key, col, row) {
                    const value = item[key];
                    switch (key) {
                        case 'specName':
                            return `<span>${getSpecGroupText(item) || '-'}<span>`;

                        case 'flavorId':
                            return `<span class="break-all-text">${item.spec || '-'}</span>`;

                        case 'cpu':
                            return html`<span
                                >${item.cpuCount || '-'}
                                <span class="grey-text m-l-5">核</span></span
                            >`;

                        case 'memory':
                            return `<span>${item.memoryCapacityInGB || '-'}
                                <span class="grey-text m-l-5">GB</span></span>`;

                        case 'cpuModel': {
                            return `<span class="break-all-text" title="${item.cpuModel}">${
                                item.cpuModel || '-'
                            }</span>`;
                        }
                        default:
                            return _.escape(value);
                    }
                },
            },
            bbcCustomTable: {
                schema: [
                    {name: 'id', label: '套餐'},
                    {name: 'cpu', label: 'CPU'},
                    {name: 'memory', label: '内存'},
                    {name: 'disk', label: '磁盘'},
                    {name: 'cpuGhz', label: '处理器主频'},
                ],
                cellBuilder: (row, key) => row[key] || '-',
            },
            existData: [],
            customData: [],
            error: false,
            NodeType,
            ExistNodeType,
            ticketUrl: window.$context.getDomains().ticket,
        };
    }

    inited() {
        this.$childs = [];
    }

    attached() {
        this.watch('vpcId', value => {
            // 切换vpcId 清空之前选择的所有节点
            this.data.set('existData', []);
            this.data.set('customData', []);

            store.dispatch('clearCustomInstances');
            store.dispatch('clearMasterSelectedCustomInstances');
            store.dispatch('clearWorkerSelectedCustomInstances');

            store.dispatch('queryBccPrice');
        });
    }

    getKey(opt) {
        const nodeType = this.data.get('nodeType');
        let key = opt + 'MasterSelectedInstanceIds';
        if (nodeType === 'Worker') {
            key = opt + 'WorkerSelectedInstanceIds';
        }

        return key;
    }

    addNode(formData = null) {
        const nodeType = this.data.get('nodeType');
        const clusterUuid = this.data.get('clusterUuid');
        const data = {
            open: true,
            nodeType,
            clusterType: this.data.get('clusterType'),
            existedNodeNum: this.data.get('existedNodeNum'),
            masterFlavor: this.data.get('masterFlavor'),
            clusterUuid,
        };
        if (this.data.get('isExpand')) {
            data.isExpand = true;
        }
        if (this.data.get('showPayType')) {
            data.showPayType = true;
        }
        if (formData && !_.isEmpty(formData)) {
            data.formData = formData;
        }

        const dialog = new AddDialog({data});

        dialog.attach(document.body);

        dialog.on('confirm', e => {
            const type = e.type;
            if (type === CreateType.CUSTOM) {
                this.data.push('customData', e.value);
                store.dispatch('addCustomInstances', {instance: _.cloneDeep(e), nodeType});
                store.dispatch('queryBccPrice');
            } else {
                if (_.isUndefined(e.index)) {
                    this.data.push('existData', e.value);
                } else {
                    this.data.set(`existData[${e.index}]`, e.value);
                }
                const selectedInstanceIds = _.pluck(e.value.instanceList, 'instanceId');
                store.dispatch(this.getKey('set'), selectedInstanceIds);
                if (formData && !_.isEmpty(formData)) {
                    const preInstanceIds = _.pluck(formData.instanceList, 'instanceId');
                    const deletedInstanceIds = _.difference(preInstanceIds, selectedInstanceIds);
                    store.dispatch(this.getKey('delete'), deletedInstanceIds);
                }
            }

            this.fire('add', {
                customData: this.data.get('customData'),
                existData: this.data.get('existData'),
            });
            this.data.set('error', false);

            dialog.dispose();
        });

        this.$childs.push(dialog);
    }

    onExistEdit(index) {
        const formData = _.cloneDeep(this.data.get('existData')[index]);
        this.addNode(_.extend({createType: CreateType.EXIST, index}, formData));
    }

    onExistDelete(index) {
        const item = _.cloneDeep(this.data.get(`existData[${index}]`));
        store.dispatch(this.getKey('delete'), _.pluck(item.instanceList, 'instanceId'));
        this.data.removeAt('existData', index);

        this.fire('delete', {
            customData: this.data.get('customData'),
            existData: this.data.get('existData'),
        });
        this.data.set('error', false);
    }

    onCustomDelete(index) {
        const nodeType = this.data.get('nodeType');
        this.data.removeAt('customData', index);
        store.dispatch('removeCustomInstances', {index, nodeType});
        store.dispatch('queryBccPrice');

        this.fire('delete', {
            customData: this.data.get('customData'),
            existData: this.data.get('existData'),
        });
        this.data.set('error', false);
    }

    disposeInternalChilds() {
        _.each(this.$childs, component => {
            const dialog = component.ref('dialog');
            try {
                if (dialog) {
                    dialog.dispose();
                }
                component.dispose();
            } catch (ex) {
                /* eslint-disable */
            }
            /* eslint-enable */
        });
        this.$childs = [];
    }

    disposed() {
        this.disposeInternalChilds();
    }

    validateForm() {
        this.data.set('error', false);
        if (this.getInstanceCount() === 0) {
            this.data.set('error', true);
            return Promise.reject();
        }

        return Promise.resolve();
    }

    getInstanceCount() {
        return this.data.get('existCount') + this.data.get('customCount');
    }

    getFormData() {
        const existData = this.data.get('existData');
        const customData = this.data.get('customData');

        return {existData, customData};
    }
}

@asComponent('@cce-cluster-create-node')
export default class NodeStore extends connect.san(StoreMap)(Node) {}
