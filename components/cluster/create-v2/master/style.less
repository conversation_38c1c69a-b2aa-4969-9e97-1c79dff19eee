.cluster-create-node {
    width: 800px;

    .create-button-wrap {
        > .s-alert {
            margin-top: 8px;
            padding: 8px;
        }

        .s-form-item-help {
            padding-bottom: 0;
        }
    }

    .item-col {
        width: 33%;
        padding: 10px 0;
        display: inline-block;
        zoom: 1;
        font-size: 0;

        .item-label {
            color: #999;
            display: inline-block;
            vertical-align: middle;
            width: 100px;
            font-size: 12px;
        }

        .item-content {
            color: #000;
            max-width: 58%;
            display: inline-block;
            vertical-align: middle;
            zoom: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 12px;
        }
    }

    .operations {
        float: right;
        text-align: right;
    }

    .col-full {
        width: 100%;

        .item-label {
            float: left;
        }

        .item-content.full-item-content {
            max-width: none;
            display: block;
        }
    }

    .full-item-content {
        margin-left: 100px;
    }

    .user-name {
        line-height: 30px;
    }

    .cluster-create-node-item {
        border: 1px solid #ccc;
        margin-top: 10px;
        border-radius: 10px;
    }

    .cluster-create-node-item-invalid {
        border-color: #f33e3e;
    }

    .cluster-create-node-item-title,
    .cluster-create-custom-item-title {
        border-bottom: 1px solid #ccc;
        background: #f5f5f5;
        padding: 5px 20px;
        border-radius: 10px 10px 0 0;
    }

    .cluster-create-node-item-list,
    .cluster-create-custom-item-list {
        padding: 10px 20px;
    }

    .cluster-create-custom-item-list {
        .item-col {
            width: 45%;
        }

        .col-full {
            width: 100%;
        }
    }
}

.remain-ips {
    b {
        font-weight: 400;
        color: #151b26;
    }
}

.cce-cluster-master-add-exist {
    .error-by-network-plugin {
        margin-top: 8px;
        color: #f33e3e;
    }
    .os-tip {
        display: flex;
        color: #84868c;
        margin-top: 8px;
    }
    .error-select {
        border-color: rgb(237, 13, 13);
    }
    .bui-form {
        .bui-form-item {
            margin: 20px 0;

            &:first-child {
                margin-top: 0;
            }
        }

        .bui-form-item.bui-form-item-rebuild {
            margin-bottom: 0;
        }
        .s-form-item-label {
            width: 107px;
            text-align: left;
        }
    }

    .tip-confirm-wrap {
        margin-top: 16px;
        padding-left: 10px;
        .s-checkbox {
            padding-left: 10px;
        }
        .desc {
            margin-left: 18px;
            margin-top: 8px;
        }
    }

    .image-version-select {
        min-width: 200px;
        max-width: 400px;
    }

    .bui-form-item-needFormat {
        .bui-form-item-label {
            color: #151b26;
        }
        .bui-form-item-content {
            .s-checkbox {
                margin-top: 8px;
                .s-radio-text {
                    font-size: 12px;
                    color: #000;
                    user-select: none;
                }
            }
            .disk-info {
                width: 900px;
                margin-bottom: 10px;
                display: flex;
                .bui-form-item-dataDevice,
                .bui-form-item-diskPath {
                    margin-right: 16px;
                    margin-top: 10px;
                    margin-bottom: 0;
                    .bui-form-item-invalid-label {
                        display: block;
                        margin-top: 4px;
                    }
                }
            }
            .tip {
                width: 900px;
                span {
                    color: #f38900;
                }
            }
        }
    }
}

.cce-cluster-master-add-custom {
    margin-bottom: 40px;

    .invalid-image {
        .image-selector {
            .s-input {
                border-color: #f33e3e;
            }
        }
    }

    .error-by-network-plugin {
        margin-left: 112px;
        margin-top: -10px;
        margin-bottom: 10px;
        color: #f33e3e;
    }
    .image-info-tip {
        display: flex;
        color: #84868c;
        margin-top: -10px;
        margin-left: 112px;
    }
    .scale-info {
        color: #84868c;
    }
    .pay-config-new {
        .common-form-item .s-form-item-label {
            flex-basis: 112px;
        }
        .recommend-msg {
            left: 123px;
        }
    }
    .instance-config-panel {
        .s-form-item-label {
            width: 115px;
            padding-left: 6px;
            padding-right: 16px;
        }

        .filter-wrapper + .s-form-item {
            display: none;
        }
        .flavor-form-item .s-form-item-label label {
            margin-top: 0px;
        }
        .wrapper-flavor + .m-t-10 {
            padding-left: 114px;
        }

        .root-instance-type {
            .bui-form-item-content {
                .bui-table {
                    margin: 0;
                    max-height: 280px;
                    overflow: auto;
                }
            }
        }

        dd {
            margin-bottom: 20px;
        }
    }

    .cce-cluster-create-custom-bidding.bui-form {
        .bui-form-item {
            .bui-form-item-help {
                padding-top: 4px;
                padding-bottom: 0;
            }
        }
    }

    .bui-form-item-help {
        width: 760px;
    }

    .bui-form-item-help-bidding {
        max-width: 900px;
        color: #84868c;
        line-height: 18px;
        margin-top: 4px;
    }

    .com-color-gray-light {
        color: #999;
    }

    .security-group-help-link {
        margin-left: 10px;
    }
    .remain-ips {
        display: inline-block;
    }
    .s-icon-button {
        padding: 0 7px;
        margin-left: 5px;
        svg {
            position: relative;
            top: -1px;
        }
    }
    .subnet-tip {
        display: flex;
        color: #84868c;
        margin-top: 4px;
    }

    .cce-cluster-create-subnet {
        display: inline-block;
        zoom: 1;
    }

    .cds-item-extra {
        .bui-textbox {
            margin-left: 10px;
        }

        .cce-custom-cds-bui-form-item-invalid-label {
            color: #f33e3e;
            display: block;
        }
    }
    .purchaseNum-error-msg {
        color: #f33e3e;
        margin-top: 4px;
    }

    .cce-custom-bui-form-item-invalid-label {
        color: #f33e3e;
        margin: -10px 0 10px 100px;
    }

    .bbc-quota-created-tip {
        margin-top: 10px;
    }

    .bui-form-item-logicalZone {
        .bui-radioselect .bui-radio-block {
            overflow: hidden;
        }

        .bui-radioselect .bui-radio-mark:after {
            left: -18px;
            top: 0;
            width: 40px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            transform: rotate(-40deg) scale(0.8);
            border-radius: 0;
            background: #f39001;
        }

        .bui-radioselect .bui-radio-disabled:after {
            background: #ccc;
        }
    }
    .expected-node-tip {
        position: relative;
        top: 3px;
        left: -6px;
    }
    .node-group-create-tip {
        color: #f38900;
    }
    .bui-radio-selected {
        background-color: #e6f0ff !important;
    }
}

.cce-cluster-add-dialog.bui-form {
    margin-top: 12px;
    .bui-form-item {
        margin: 0 0 24px 0;
    }

    .bui-radioselect > ul > li.bui-radio-block {
        min-width: 100px;
    }

    .instance-config-panel {
        .bui-form-item {
            .bui-form-item-content {
                max-width: 1100px;

                .bui-radio-block {
                    min-width: 100px;
                    margin-bottom: 0;
                }
            }
        }

        .multi-line-item {
            .bui-form-item-label {
                padding: 0;
            }
        }
        .image-type-content .bui-radioselect ul {
            max-width: 800px;
        }
    }

    .system-config-panel {
        .bui-form-item-instanceName,
        .bui-form-item-adminPass,
        .bui-form-item-adminPassConfirm {
            .bui-textbox input {
                width: 197px;
            }
        }

        .bui-form-item-adminPass,
        .bui-form-item-adminPassConfirm {
            margin-bottom: 0;
        }

        .adminPass-tip {
            margin: 10px 0 10px 115px;
        }

        #adminPassType {
            margin-bottom: 10px;
        }

        .bui-form-item-name {
            margin-bottom: 10px;
        }

        .bui-form-item-label.require-label.required-label {
            margin-left: 11px;
        }
    }

    .eip-config-panel {
        .bui-textbox input {
            width: 209px;
        }

        .form-item-bottom-tip {
            margin: -10px 0 20px 100px;
        }

        .bui-form-item.sub-item {
            margin-left: 102px;
        }

        .s-form-item-label {
            width: 115px;
            label {
                padding-left: 11px;
            }
        }
    }

    .bui-form-item-securityGroupId,
    .bui-form-item-logicalZone,
    .bui-form-item-subnetId,
    .bui-form-item-createType {
        .bui-form-item-help {
            padding: 0;
            margin-top: 10px;
        }
    }

    .com-storage-conf {
        .kms-wrap {
            margin-top: 8px;
        }
        .span-text {
            line-height: 30px;
        }
        .data-disk-form-item {
            .s-trigger-container {
                margin-bottom: 8px;
            }
            .s-form-item-control-wrapper {
                width: 850px;
            }
            .disk-tip {
                margin-left: 4px;
            }
        }

        .syetem-disk-item,
        .data-disk-size-header {
            .com-normal-tip-gray {
                position: relative;
                top: 6px;
                left: 4px;
            }
        }

        .create-cds-wrapper {
            .s-button {
                margin-bottom: 0;
            }
        }

        .s-form-item-label {
            width: 115px;
            padding-left: 6px;
            padding-right: 16px;
        }

        #sysDisk {
            .bui-form-item-content {
                width: 1030px;
            }
        }

        #cdsDisk {
            // width: 1030px;
        }

        .com-color-red {
            color: #f38900;
        }
        .disk-compare-system-btn {
            padding-left: 8px;
            position: relative;
            top: -1px;
        }
    }

    .com-dialog {
        .bui-table {
            border: 1px solid #f5f5f5;
            max-height: 200px;
            overflow: auto;
        }
    }

    .bui-form-item-productType {
        .bui-form-item-content {
            line-height: 30px;
        }
    }

    .bui-form-item-label {
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .login-config {
        .s-form-item-label-required {
            label:before {
                left: -12px;
            }
        }
    }
    .custom-gpu-driver {
        margin-bottom: 10px;
        .s-switch-checked {
            margin-right: 8px;
            position: relative;
            bottom: 4px;
        }
        .s-cascader-panel {
            background: unset;
            box-shadow: unset;
        }
    }
    .icon-tip {
        margin-left: -8px;
        position: relative;
        top: -1px;
    }
}

.cce-cluster-add-dialog.cce-cluster-add-dialog-edge {
    .bui-form-item-createType {
        .bui-form-item-help {
            color: #f38900;
        }
    }
}

#osVersion.error-msg {
    border: 1px solid #f33e3e;
    .bui-select-text {
        color: #000;
    }
}

.security-group-rule {
    display: inline-block;
    zoom: 1;
    margin: 0 0px 0 10px;
    color: #f33e3e;
    a {
        margin-left: 5px;
        vertical-align: middle;
    }
    .bui-loading {
        margin-left: 5px;
    }
}

.cce-cluster-create-custom-security-item {
    color: #108cee;

    .iconfont {
        font-size: 12px;
        margin-right: 10px;
    }
}

.cce-create-rule-error {
    color: #f33e3e;
}

.break-all-text {
    word-break: break-all;
}

.grey-text {
    color: #999;
}

.m-l-5 {
    margin-left: 5px;
}

.bui-form {
    .bui-form-item.bui-form-item-clusterHA,
    .bui-form-item.bui-form-item-subnetId {
        .bui-form-item-help {
            color: #f38900;
        }
    }

    h4 {
        border-left: none !important;
        padding-left: 0 !important;
    }
}

.node-config {
    color: #999;
    margin-left: 10px;
    font-size: 12px;
}

.s-alert-width {
    max-width: 180px;
}

.s-button-icon {
    border: 1px solid #f38900;
    &:hover {
        background-color: #f38900;
    }
}
.s-icon-exclamation {
    fill: #f38900;
    line-height: 16px;
    &:hover {
        fill: #fff !important;
    }
}

.system-tip {
    .s-tooltip-body {
        background: #2468f2;
        padding: 16px;
        color: #fff;
        .system-title {
            font-size: 16px;
            margin-bottom: 12px;
        }
        .system-des {
            font-size: 12px;
        }
    }
    .s-tooltip-arrow {
        background: #2468f2 !important;
    }
}

.custom-driver-item.hide-label {
    display: block !important;
}

.node-add-dialog {
    .custom-driver-item.hide-label {
        .s-form-item-label {
            width: 100px;
        }
    }
}
