/**
 * @file components/cluster/create-v2/master/zone-subnets.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, HttpClient} from '@baiducloud/runtime';
import {notification, Loading, RadioSelect, Form, FormItem} from '@baiducloud/bce-ui/san';
import {Tooltip, Button, Select} from '@baidu/sui';
import {OutlinedExclamation, OutlinedRefresh} from '@baidu/sui-icon';
import {connect} from 'san-store';
import * as AsyncValidator from 'async-validator';
import {SubnetCreateDialog} from '@baidu/bce-vpc-sdk-san';
import {VpcSDK} from '@baidu/bce-vpc-sdk';
import {getSubnetRemainingIP} from '../../../../utils/util';
import tips from '../../../../utils/tips';
import {getSubnetName} from '../../../../utils/helper';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {NetworkModeCni, NodeType} from '../enums';

const Schema = AsyncValidator.default;

const rules = new Schema({
    logicalZone: [{required: true, message: '请选择可用区'}],
    subnetId: [{required: true, message: '请选择子网'}],
});

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<div>
    <ui-form s-ref="form" form-data="{=formData=}" rules="{{rules}}" errors="{=formErrors=}">
        <ui-form-item inline required label-width="{{100}}" label="可用区：" name="logicalZone">
            <ui-loading size="small" s-if="zone.loading" />
            <ui-radio-select
                s-else
                datasource="{{logicalZone.list}}"
                value="{=formData.logicalZone=}"
                on-change="onZoneChange"
            />
            <s-tooltip placement="right">
                <div class="s-button-icon">
                    <s-icon-exclamation class="s-icon-exclamation" is-button="{{false}}" />
                </div>
                <div slot="content" class="s-alert-width">
                    <span
                        >可用区是指在同一区域下，电力和网络互相独立的区域，故障会被隔离在一个可用区内。如果您的应用程序需要更高的高可用性，建议您将云服务创建在不同的可用区内。</span
                    >
                    <a href="${tips.doc.zone}" target="_blank">了解详情。</a>
                </div>
            </s-tooltip>
        </ui-form-item>

        <ui-form-item inline required label-width="{{100}}" label="子网：" name="subnetId">
            <ui-loading size="small" s-if="subnet.loading" />
            <ui-select
                s-else
                width="{{328}}"
                tip-position="lt"
                datasource="{{subnet.list}}"
                value="{=formData.subnetId=}"
                on-change="onSubnetChange"
            />
            <s-button class="s-icon-button" s-if="!subnet.loading" on-click="getSubnetList">
                <s-icon-refresh class="button-icon" is-button="{{false}}" />
            </s-button>
            <div class="subnet-tip">
                <div class="remain-ips" s-if="{{formData.subnetId}}">
                    当前子网剩余可用 IP 数：<b>{{subnetRemainIps}}</b>个。
                </div>
                <div
                    s-if="onWorkVPCNIMode || subnet.list.length === 0"
                    class="cce-cluster-create-subnet"
                >
                    如需创建子网，可以
                    <a href="javascript:void(0)" on-click="onCreateSubnet">去创建子网</a>
                </div>
            </div>
        </ui-form-item>
    </ui-form>
</div> `;

@invokeBceSanUI
class ZoneSubnets extends Component {
    static components = {
        'ui-form': Form,
        'ui-form-item': FormItem,
        'ui-radio-select': RadioSelect,
        'ui-select': Select,
        'ui-loading': Loading,
        's-tooltip': Tooltip,
        's-button': Button,
        's-icon-refresh': OutlinedRefresh,
        's-icon-exclamation': OutlinedExclamation,
    };

    static template = template;

    static computed = {
        onWorkVPCNIMode() {
            const networkMode = this.data.get('networkMode');
            return !_.isEmpty(NetworkModeCni.fromValue(networkMode));
        },
    };

    initData() {
        return {
            rules,
            formData: {},
            formErrors: null,
            eniInZonesKeys: {},
            onWorkVPCNIMode: false,
            subnet: {
                loading: true,
            },
            zone: {
                loading: true,
            },
        };
    }

    inited() {
        this.$childs = [];
    }

    attached() {
        // 设置剩余子网数
        const subnetId = this.data.get('formData.subnetId');
        const getSubnetRemainingIPHandler = async subnetId => {
            let remainIps = 0;
            if (subnetId) {
                remainIps = await getSubnetRemainingIP(subnetId);
            }
            this.data.set('subnetRemainIps', remainIps);
        };
        getSubnetRemainingIPHandler(subnetId);
        this.watch('formData.subnetId', async subnetId => {
            getSubnetRemainingIPHandler(subnetId);
        });
        this.initZoneList();

        this.watch('instanceNodeType', val => {
            this.initZoneList();
        });
    }

    initZoneList() {
        this.getFlavorList()
            .then(() => {
                if (this.data.get('onWorkVPCNIMode')) {
                    return this.getOnCNIZones();
                }
                return Promise.resolve();
            })
            .then(() => {
                if (this.data.get('onWorkVPCNIMode')) {
                    const zoneList = this.data.get('logicalZone.list');
                    const eniInZonesKeys = this.data.get('eniInZonesKeys');
                    const enableZones = [];
                    _.each(zoneList, (z, i) => {
                        if (!eniInZonesKeys[z.value]) {
                            this.data.merge(`logicalZone.list[${i}]`, {
                                disabled: true,
                                tip: '该可用区下无容器子网',
                            });
                        } else {
                            enableZones.push(z.value);
                        }
                    });
                    if (enableZones.length > 0) {
                        const logicalZone = this.data.get('formData.logicalZone');
                        if (!logicalZone || !enableZones.includes(logicalZone)) {
                            this.data.set('formData.logicalZone', enableZones[0]);
                        }
                    } else {
                        this.data.set('formData.logicalZone', '');
                    }
                }

                this.getSubnetList();
            })
            .catch(e => {});
    }

    async getOnCNIZones() {
        const onExtendClusterUuid = this.data.get('onExtendClusterUuid');

        if (onExtendClusterUuid) {
            return this.getEniSubnets(onExtendClusterUuid).then(eniSubnets => {
                const eniVPCSubnet = eniSubnets.reduce((result, eniSubnet) => {
                    if (result[eniSubnet.availableZone]) {
                        result[eniSubnet.availableZone].push(eniSubnet.subnetID);
                    } else {
                        result[eniSubnet.availableZone] = [eniSubnet.subnetID];
                    }
                    return result;
                }, {});

                this.data.set('eniInZonesKeys', eniVPCSubnet);
            });
        } else {
            const eniVPCSubnet = this.data.get('eniVPCSubnetIDs') || {};

            this.data.set('eniInZonesKeys', eniVPCSubnet);
        }
    }

    getFlavorList() {
        this.data.set('zone.loading', true);
        return this.data
            .get('sdk')
            .getFlavorList({})
            .then(data => {
                if (!data.length) {
                    return;
                }
                let zoneList = [];
                _.each(data, item => {
                    const instanceNodeType = this.data.get('instanceNodeType');
                    const key = instanceNodeType === NodeType.BCC ? 'bccResources' : 'ebcResources';
                    const soldOut = !item?.[key]?.flavorGroups?.length;
                    zoneList.push({
                        text: window.$zone.getLabel(item.logicalZone),
                        value: item.logicalZone,
                        defaultZone: !soldOut && item.defaultZone,
                        mark: soldOut ? '售罄' : '',
                        disabled: soldOut,
                    });
                });

                const selectedZone =
                    _.find(
                        zoneList,
                        item => item.value === this.data.get('formData.logicalZone'),
                    ) ||
                    _.find(zoneList, item => item.defaultZone) ||
                    zoneList[0];
                this.data.set('logicalZone.list', zoneList);
                this.data.set('formData.logicalZone', selectedZone.value);
                this.data.set('zone.loading', false);
                this.data.set('zoneLoading', false);
            })
            .catch(() => {
                this.data.set('zone.loading', false);
                this.data.set('zoneLoading', false);
            });
    }

    getSubnetList() {
        this.data.set('subnet.loading', true);
        return this.data
            .get('sdk')
            .getSubnetList({
                subnetTypes: [1, 3],
                vpcId: this.data.get('vpcId'),
                zone: this.data.get('formData.logicalZone'),
            })
            .then(data => {
                let list = [];
                const zone = this.data.get('formData.logicalZone');
                const onWorkVPCNIMode = this.data.get('onWorkVPCNIMode');
                const IPversion = this.data.get('IPversion');
                if (onWorkVPCNIMode) {
                    list = _.map(data || [], item => {
                        const cidr = item.cidr ? ' (' + item.cidr + ') ' : '';
                        const ipv6Cidr = IPversion
                            ? item.ipv6Cidr
                                ? '(' + item.ipv6Cidr + ')'
                                : ''
                            : '';
                        const text = getSubnetName(item.name) + cidr + ipv6Cidr;
                        const value = item.shortId;
                        const disabled = IPversion && !item.ipv6Cidr;
                        const tip = IPversion && !item.ipv6Cidr ? '该子网未分配IPv6' : '';
                        return {
                            text,
                            value,
                            disabled,
                            tip: tip || text,
                        };
                    });
                    const filterList = _.filter(list, item => !item.disabled);
                    if (filterList.length) {
                        const {subnetId} = this.data.get('formData');
                        const defaultSubnetId =
                            filterList.find(e => e.value === subnetId)?.value ||
                            filterList[0].value;
                        this.data.set('formData.subnetId', defaultSubnetId);
                        this.data.set(
                            'formData.selectedSubnetItem',
                            filterList.find(e => e.value === defaultSubnetId),
                        );
                        this.data.set('formErrors.subnetId', null);
                    } else {
                        this.data.set('formData.subnetId', '');
                        this.data.set('formData.selectedSubnetItem', null);
                    }
                } else {
                    list = _.map(data || [], item => {
                        const ipv6Cidr = IPversion
                            ? item.ipv6Cidr
                                ? '(' + item.ipv6Cidr + ')'
                                : ''
                            : '';
                        return {
                            text:
                                getSubnetName(item.name) +
                                (item.cidr ? '（' + item.cidr + '）' : '') +
                                ipv6Cidr,
                            value: item.shortId,
                            disabled: IPversion && !item.ipv6Cidr,
                            tip:
                                getSubnetName(item.name) +
                                (item.cidr ? '（' + item.cidr + '）' : '') +
                                ipv6Cidr,
                        };
                    });
                    const filterList = _.filter(list, item => !item.disabled);
                    if (filterList.length) {
                        const {subnetId} = this.data.get('formData');
                        const defaultSubnetId =
                            list.find(e => e.value === subnetId)?.value || list[0].value;
                        this.data.set('formData.subnetId', defaultSubnetId);
                        this.data.set(
                            'formData.selectedSubnetItem',
                            list.find(e => e.value === defaultSubnetId),
                        );
                        this.data.set('formErrors.subnetId', null);
                    } else {
                        this.data.set('formData.subnetId', '');
                        this.data.set('formData.selectedSubnetItem', null);
                    }
                }
                this.data.set('subnet.list', list);
                this.data.set('subnet.loading', false);
            })
            .catch(() => {
                this.data.set('subnet.loading', false);
                this.data.set('subnet.list', []);
            });
    }

    getEniSubnets(clusterUuid) {
        return this.$http.getClusterExtraInfoV2(clusterUuid).then(({result}) => {
            return result.eniSubnets || [];
        });
    }

    onZoneChange(e) {
        this.data.set('formData.logicalZone', e.value);
        this.getSubnetList();
        this.fire('zoneChange', e.value);
    }

    onSubnetChange(e) {
        const item = _.find(this.data.get('subnet.list'), d => d.value === e.value);
        this.data.set('formData.selectedSubnetItem', item || {});
    }

    onCreateSubnet() {
        const selectedVpcItem = this.data.get('selectedVpcItem');
        const payload = {
            availableService: ['CCE'],
            subnetTypes: [1, 3],
            zone: this.data.get('formData.logicalZone'),
            vpcInfo: {
                name: selectedVpcItem.text,
                ipv6Cidr: selectedVpcItem.ipv6Cidr,
                cidr: selectedVpcItem.cidr,
                auxiliaryCidr: selectedVpcItem.auxiliaryCidr,
                vpcId: selectedVpcItem.value,
            },
        };
        const dialog = new SubnetCreateDialog({
            sdk: new VpcSDK({
                client: new HttpClient(),
                context: window.$context,
            }),
            ...payload,
        });
        dialog.attach(document.body);
        dialog.on('create', () => {
            notification.success('创建子网成功');
            this.getSubnetList();
        });
        dialog.on('cancel', () => dialog && dialog.dispose());

        this.$childs.push(dialog);
    }

    validateForm() {
        const form = this.ref('form');

        return form.validateForm();
    }

    getFormData() {
        return this.data.get('formData');
    }

    disposed() {
        _.each(this.$childs, comp => {
            comp.dispose();
        });
        this.$childs = [];
    }
}

@asComponent('@cce-cluster-create-zone-subnets')
export default class ZoneSubnetsStore extends connect.san(StoreMap)(ZoneSubnets) {}
