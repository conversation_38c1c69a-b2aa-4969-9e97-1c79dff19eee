/**
 * @file components/cluster/create-v2/network/cidr.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';

import Cluster from '../../../../utils/cluster';
import {getAvaliableByMask, getAvaliableContent, checkAvaliablePart} from '../../../../utils/network';

const {asComponent, invokeBceSanUI} = decorators;

const cidrs = [
    {text: '172', value: '172'},
    {text: '192', value: '192'}
];

const cidr10 = [
    {text: '10', value: '10'}
];

const template = html`<div class="cce-create-network-cidr">
    <ui-select
        value="{=cidr0=}"
        datasource="{{cidr0Datasource}}"
        on-change="onCheckCidr($event, 'cidr0')"
        disabled="{{cidr0Disable}}"
    />
    <span class="cidr-dot">.</span>
    <ui-text-box
        width="60"
        value="{=cidr1=}"
        s-if="cidr0 === '10'"
        disabled="{{cidr1Disable}}"
        on-input="onCheckCidr($event, 'cidr1')"
        on-focus="cidr1Focus"
        on-blur="cidrBlur"
    />
    <ui-select
        value="{=cidr1=}"
        s-else
        datasource="{{cidr1Datasource}}"
        on-change="onCheckCidr($event, 'cidr1')"
    />
    <span class="cidr-dot">.</span>
    <ui-text-box
        width="60"
        value="{=cidr2=}"
        on-blur="cidrBlur"
        on-input="onCheckCidr($event, 'cidr2')"
        disabled="{{cidr2Disable || mask === '16'}}"
        on-focus="cidr2Focus"
    />
    <span class="cidr-dot">.</span>
    <ui-text-box
        width="60"
        value="{=cidr3=}"
        on-blur="cidrBlur"
        on-input="onCheckCidr($event, 'cidr3')"
        disabled="{{cidr3Disable}}"
        on-focus="cidr3Focus"
    />
    <span class="cidr-dot">/</span>
    <ui-select
        on-change="maskChange($event)"
        value="{=mask=}"
        datasource="{{maskDatasource}}" />
    <ui-tip s-if="message" message="{{message}}" layer-width="200"></ui-tip>
    <div class="cce-tip-grey">{{maskTip}}</div>
    <div class="cce-error" s-if="error">CIDR不合法</div>
</div>
`;

@asComponent('@cce-cluster-create-cidr')
@invokeBceSanUI
export default class Cidr extends Component {
    static template = template

    initData() {
        // 吉利容器和clusterIP网段仅支持192开头
        let cidr0Datasource = [
            ...cidrs,
            ...cidr10
        ];
        let cidr0 = '172';
        let cidr1 = '16';
        let mask = '16';
        if ($flag.CceClusterNetworkCIDR) {
            cidr0 = '192';
            cidr1 = '168';
            mask = '17';
            cidr0Datasource = [{text: '192', value: '192'}]
        }
        return {
            cidr0,
            cidr0Datasource,
            cidr1,
            cidr2: '0',
            cidr3: '0',
            mask,
            message: '',
            error: false
        };
    }

    attached() {
        // 云边集群的cluster ip不用展示10网段
        const isCloudEdge = this.data.get('isCloudEdge');
        if (isCloudEdge) {
            this.data.set('cidr0Datasource', cidrs);
        }
        if (!this.data.get('cidr')) {
            const value = this.data.get('value');
            this.data.set('cidr', value);
            this.fire('change', {value});
            this.maskChange({value: this.data.get('mask')});
        }

        this.watch('cidr', value => {
            const cidr = this.data.get('value');
            if (cidr === value) {
                return;
            }
            if (value) {
                const [prefix, mask] = value.split('/');
                const [cidr0, cidr1, cidr2, cidr3] = prefix.split('.');
                this.data.set('cidr0', cidr0);
                this.data.set('cidr1', cidr1);
                this.data.set('cidr2', cidr2);
                this.data.set('cidr3', cidr3);
                this.data.set('mask', mask);

                this.maskChange({value: mask});
            }
        });

        this.watch('cidr1Datasource', datasource => {
            const cidr1 = this.data.get('cidr1');
            if (!_.find(datasource, item => item.value === cidr1)) {
                this.data.set('cidr1', datasource.length ? datasource[0].value : cidr1);
            }
        });

        this.watch('maskDatasource', datasource => {
            const mask = this.data.get('mask');
            if (!_.find(datasource, item => item.value === mask) && datasource.length > 0) {
                this.data.set('mask', datasource[0].value);
            }
        });
    }

    static computed = {
        cidr1Datasource() {
            const cidr0 = this.data.get('cidr0');
            if (cidr0 !== '10') {
                let mask = this.data.get('mask');
                let result = getAvaliableByMask(mask, 1);
                return _.filter(_.find(Cluster.cidr, item => item.value === cidr0).children,
                    item => _.find(result, d => d + '' === item.value));
            }

            return [];
        },
        maskDatasource() {
            const cidr0 = this.data.get('cidr0');
            const maxMask = +(this.data.get('maxMask') || '24');
            let mask = [];
            if (cidr0 === '192') {
                for (let i = 16; i <= maxMask; i++) {
                    const value = i + '';
                    mask.push({text: value, value});
                }
            }
            else if (cidr0 === '172') {
                for (let i = 12; i <= maxMask; i++) {
                    const value = i + '';
                    mask.push({text: value, value});
                }
            }
            else {
                // ClusterIP掩码不能小于12
                const minMask = +(this.data.get('minMask') || '8');
                for (let i = minMask; i <= maxMask; i++) {
                    const value = i + '';
                    mask.push({text: value, value});
                }
            }

            return mask;
        },
        value() {
            const cidr0 = this.data.get('cidr0');
            const cidr1 = this.data.get('cidr1');
            const cidr2 = this.data.get('cidr2');
            const cidr3 = this.data.get('cidr3');
            const mask = this.data.get('mask');
            return cidr0 + '.' + cidr1 + '.' + cidr2 + '.' + cidr3 + '/' + mask;
        }
    }

    cidr1Focus() {
        let mask = this.data.get('mask');
        let result = getAvaliableByMask(mask, 1);
        this.data.set('maskTip', getAvaliableContent(result));
    }

    cidrBlur() {
        const cidrValid = this.data.get('cidrValid'); // 合法为false
        this.data.set('maskTip', '');
        !cidrValid && this.onCheckCidr(); // 合法检查
    }

    cidr2Focus() {
        let mask = this.data.get('mask');
        let result = getAvaliableByMask(mask, 2);
        this.data.set('maskTip', getAvaliableContent(result));
    }

    cidr3Focus() {
        let mask = this.data.get('mask');
        let result = getAvaliableByMask(mask, 3);
        this.data.set('maskTip', getAvaliableContent(result));
    }

    maskChange(e) {
        let value = parseInt(e.value, 10);
        if (value <= 8) {
            this.data.set('cidr1', 0);
            this.data.set('cidr1Disable', true);
        }
        else {
            this.data.set('cidr1Disable', false);
        }

        if (value > 16) {
            this.data.set('cidr2Disable', false);
        }
        else {
            this.data.set('cidr2', 0);
            this.data.set('cidr2Disable', true);
        }

        if (value > 24) {
            this.data.set('cidr3Disable', false);
        }
        else {
            this.data.set('cidr3', 0);
            this.data.set('cidr3Disable', true);
        }

        this.updateCidr();

        this.onCheckCidr(e, 'mask');
    }

    updateCidr() {
        let mask = this.data.get('mask');
        for (let i = 0; i < 3; i++) {
            const value = this.data.get(`cidr${i}`);
            let result = getAvaliableByMask(mask, i);
            if (_.findIndex(result, d => d === +value) === -1 && result.length > 0) {
                this.data.set(`cidr${i}`, result[0]);
            }
        }
    }

    validateCidr() {
        const mask = this.data.get('mask');
        let result = true;
        for (let i = 0; i < 3; i++) {
            const value = _.trim(this.data.get(`cidr${i}`));
            if (!value || (value && !checkAvaliablePart(mask, i, value))) {
                result = false;

                break;
            }
        }

        this.data.set('error', !result);

        return result;
    }

    onCheckCidr = _.debounce((e, id) => {
        if (id) {
            this.data.set(id, e.value);
        }

        if (!this.validateCidr()) {
            return;
        }

        this.fire('change', {value: this.data.get('value')});
    }, 500)
}
