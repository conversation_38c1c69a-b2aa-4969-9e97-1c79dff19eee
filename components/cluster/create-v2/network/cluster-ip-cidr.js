/**
 * @file components/cluster/create-v2/network/cluster-ip-cidr.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {connect} from 'san-store';
import NetworkConflict from './conflict';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {Loading} from '@baidu/sui';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const $flag = ServiceFactory.resolve('$flag');

/* eslint-disable */
const template = html`<div class="cce-cluster-ip-cidr">
    <ui-form-item
        inline
        required
        label="ClusterIP网段："
        name="clusterIPServiceCIDR"
        help="{{clusterIPContent}}"
    >
        <label slot="label" title="ClusterIP网段：">ClusterIP网段：</label>
        <cce-cluster-create-cidr
            s-ref="cidr"
            on-change="onClusterIpChange($event)"
            max-mask="{{maxMask}}"
            min-mask="12"
            cidr="{{clusterIPServiceCIDR}}"
        />
        <ui-tip
            layer-width="200"
            message="ClusterIP指通过集群内部IP暴露服务，服务在集群内部可被访问。ClusterIP网段即服务IP网段，需与VPC网络相互独立。"
        />
        <s-loading loading s-if="checkLoading" />

        <span s-else>
            <span s-if="isConflict" class="cce-error">
                ClusterIP网段与节点网段冲突
                <a
                    href="javascript:void(0)"
                    on-click="onConflictDetail('cluster')"
                    class="vpc-conflict-btn"
                >
                    推荐
                </a>
            </span>
        </span>
    </ui-form-item>
</div>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp('@cce-cluster-create-cidr')
class ClusterIpCidr extends Component {
    static template = template;

    static components = {
        's-loading': Loading,
    };

    static computed = {
        clusterIPContent() {
            const cidr = this.data.get('clusterIPServiceCIDR') || '';
            const mask = +(cidr.split('/')[1] || '32');
            const clusterMaxServiceNum = Math.pow(2, 32 - mask);
            const span = '<span class="num-highlight">';
            return (
                `当前ClusterIP网段配置下，集群最多允许创建${span}${clusterMaxServiceNum}${'</span>'}个service` +
                `${'&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://kubernetes.io/docs/concepts/services-networking/service/" target="_blank">'}K8S Service ClusterIP网段说明${'</a>'}`
            );
        },
    };

    initData() {
        return {
            checkLoading: false,
            isConflict: false,
            maxMask: $flag.CceClusterNetworkCIDR ? 24 : 29,
        };
    }

    attached() {
        this.watch('vpcId', () => {
            // 切换vpc重新检查冲突
            this.onClusterIpChange();
        });
    }

    validateForm() {
        const isConflict = this.data.get('isConflict');
        if (isConflict) {
            return Promise.reject();
        }

        return Promise.resolve();
    }

    getFormData() {
        return {clusterIPServiceCIDR: this.data.get('clusterIPServiceCIDR')};
    }

    onClusterIpChange(e) {
        if (e && e.value) {
            this.data.set('clusterIPServiceCIDR', e.value);
        }
        const cidr = this.ref('cidr');
        if (!cidr?.validateCidr()) {
            return;
        }

        this.checkClusterIpCidr();
    }

    checkClusterIpCidr() {
        const selectedVpc = this.data.get('selectedVpcItem');
        const clusterIPServiceCIDR = this.data.get('clusterIPServiceCIDR');
        if (selectedVpc && selectedVpc.cidr && clusterIPServiceCIDR) {
            let payload = {
                clusterIPCIDR: clusterIPServiceCIDR,
                ipVersion: 'ipv4',
                vpcCIDR: selectedVpc.cidr,
                vpcID: selectedVpc.shortId,
            };

            this.data.set('checkLoading', true);
            return this.$http
                .checkClusterIpCidr(payload)
                .then(({result}) => this.data.set('isConflict', result.isConflict))
                .catch(() => this.data.set('isConflict', false))
                .then(() => this.data.set('checkLoading', false));
        }
    }

    getClusterPayload(ipVersion) {
        const selectedVpc = this.data.get('selectedVpcItem');
        const cidr = this.data.get('clusterIPServiceCIDR') || '';
        const mask = +(cidr.split('/')[1] || '32');
        const clusterMaxServiceNum =
            Math.pow(2, 32 - mask) > 65536 ? 65536 : Math.pow(2, 32 - mask);
        return {
            vpcCIDR: selectedVpc?.cidr,
            containerCIDR: selectedVpc?.cidr,
            clusterMaxServiceNum,
            privateNetCIDRs: ['10.0.0.0/8', '**********/12', '***********/16'],
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: ipVersion,
        };
    }

    onConflictDetail(type) {
        const isCluster = type === 'cluster';
        const ipVersion = 'ipv4';

        const networkConflict = new NetworkConflict({
            data: {
                isCluster,
                containerNetError: {},
                payload: this.getClusterPayload(ipVersion),
            },
        });

        networkConflict.attach(document.body);
        networkConflict.on('ok', cidr => {
            if (cidr) {
                this.data.set('clusterIPServiceCIDR', cidr);
            }
        });
    }
}

@asComponent('@cce-cluster-create-cluster-ip-cidr')
export default class ClusterIpCidrStore extends connect.san(StoreMap)(ClusterIpCidr) {}
