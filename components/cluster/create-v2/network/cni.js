import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';
import cidrRegex from 'cidr-regex';
import {getSecurityName} from '../../../../utils/helper';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import tips from '../../../../utils/tips';
import {ClusterTemplateType} from '../template/config';
import NetworkConflict from './conflict';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';
import SubnetTable from '@pages/cluster/create-v2/components/network/subnet-table';
import {Loading} from '@baidu/sui';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const Schema = AsyncValidator.default;

/* eslint-disable */
const template = html`<div class="cce-create-network-cni">
    <ui-form s-ref="form" form-data="{=formData=}" errors="{{formErrors}}" rules="{{rules}}">
        <ui-form-item inline label="容器子网：" s-if="!isServerless" name="subnets" required>
            <subnet-table
                s-ref="subnetTable"
                vpcId="{{vpcId}}"
                selectedVpcItem="{{selectedVpcItem}}"
                isServerless="{{isServerless}}"
                isEBPF="{{eBPFEnabled}}"
                IPversion="{{IPversion}}"
                on-selected-change="onSubnetsSelected"
            />
            <div class="bui-form-item-help">
                <span class="cce-tip-warn"
                    >集群创建的Pod将从容器子网中分配IP地址，请选择与节点子网同可用区的不同子网作为容器子网。</span
                >
                <span
                    >若无合适子网，请<a href="javascript:void(0)" on-click="onCreateSubnet"
                        >创建子网</a
                    ></span
                >
            </div>
        </ui-form-item>
        <ui-form-item
            inline
            required
            label="{{isServerless ? '安全组：' : 'ENI 安全组'}}"
            name="eniSecurityGroupId"
            help="{{securityHelp}}"
        >
            <s-loading loading s-if="securityGroups.loading" />
            <ui-select
                s-else
                width="250"
                value="{=formData.eniSecurityGroupId=}"
                datasource="{{securityGroups.list}}"
            />
        </ui-form-item>
        <cce-cluster-create-cluster-ip-cidr
            IPversion="{{IPversion}}"
            selectedVpcItem="{{selectedVpcItem}}"
            s-ref="clusterIpCidr"
            s-if="!enableServiceController"
        />
        <ui-form-item
            s-if="!enableServiceController && IPversion"
            inline
            required
            label="IPv6 ClusterIP网段："
            name="clusterIPServiceCIDRIPv6"
            class="cce-cluster-ip-cidr"
            help="{{'当前ClusterIP网段配置下，集群最多允许创建'+ serviceNum +'个Service'}}"
        >
            <ui-text-box
                width="240"
                value="{=formData.clusterIPServiceCIDRIPv6=}"
                placeholder="请输入"
            />
            <ui-tip
                layer-width="200"
                message="ClusterIP指通过集群内部IP暴露服务，服务在集群内部可被访问。ClusterIP网段即服务IP网段，需与VPC网络相互独立。"
            />
            <span s-if="isIpv6CidrConflict">
                <span class="cce-error"> ClusterIP网段与节点网段冲突 </span>
            </span>
            <a
                s-if="isIpv6CidrConflict || !formData.clusterIPServiceCIDRIPv6"
                href="javascript:void(0)"
                on-click="onConflictDetail('cluster')"
                class="vpc-conflict-btn"
                >推荐
            </a>
        </ui-form-item>
        <advanced-config-container s-if="!isServerless" open="{=advancedOpen=}">
            <cce-cluster-create-port-mode
                s-ref="portMode"
                mode="{{mode}}"
                isExclusiveEni="{{isExclusiveEni}}"
                eBPFEnabled="{{eBPFEnabled}}"
                eBPFSettingVisible="{{eBPFSettingVisible}}"
            />
        </advanced-config-container>
    </ui-form>
</div>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp('@cce-cluster-create-cluster-ip-cidr', '@cce-cluster-create-port-mode')
class Cni extends Component {
    static template = template;

    static components = {
        'advanced-config-container': AdvancedConfigContainer,
        'subnet-table': SubnetTable,
        's-loading': Loading,
    };

    static computed = {
        securityHelp() {
            const isServerless = this.data.get('isServerless');
            return isServerless
                ? '该安全组将应用于Serverless集群中启动的BCI实例'
                : '新建的弹性网卡将绑定该安全组，所有的外部流量都将通过该安全组进入容器。' +
                      '<a href="' +
                      tips.doc.securityGroup +
                      '" target="_blank">CCE默认/附加安全组说明</a>';
        },
        isARMCluster() {
            const clusterTemplateType = store.getState('clusterTemplateType');
            return clusterTemplateType === ClusterTemplateType.ARM;
        },
        serviceNum() {
            const clusterIPServiceCIDRIPv6 =
                this.data.get('formData.clusterIPServiceCIDRIPv6') || '';
            const mask = clusterIPServiceCIDRIPv6.split('/')[1];
            if (mask) {
                const maskNum = +(mask || '128');
                const clusterMaxServiceNum = Math.pow(2, 128 - maskNum);
                return `<span class="num-highlight">${clusterMaxServiceNum}</span>`;
            } else {
                return '--';
            }
        },
        ipv6CidrErr() {
            const clusterIPServiceCIDRIPv6 = this.data.get('formData.clusterIPServiceCIDRIPv6');
            if (clusterIPServiceCIDRIPv6) {
                return !cidrRegex.v6({exact: true}).test(clusterIPServiceCIDRIPv6);
            } else {
                return false;
            }
        },
        rules() {
            const rules = {
                eniSecurityGroupId: {
                    validator(rule, value, callback, source) {
                        if (_.isUndefined(source.eniSecurityGroupId)) {
                            return callback();
                        }
                        if (!value) {
                            return callback('至少选择一个安全组');
                        }
                        callback();
                    },
                },
            };
            if (this.data.get('IPversion')) {
                rules.clusterIPServiceCIDRIPv6 = {
                    validator: (rule, value, callback, source) => {
                        if (!value) {
                            return callback('请输入cidr');
                        } else {
                            if (!cidrRegex.v6({exact: true}).test(value)) {
                                return callback('无效cidr');
                            } else {
                                return callback();
                            }
                        }
                    },
                };
            }
            return new Schema(rules);
        },
    };

    initData() {
        return {
            formData: {},
            formErrors: null,
            securityGroups: {
                loading: false,
                list: [],
            },
            advancedOpen: false,
            isIpv6CidrConflict: false,
        };
    }

    attached() {
        const vpcId = this.data.get('vpcId');
        if (vpcId) {
            this.getSecurityGroups();
            this.getInitIpv6Cidr();
        }

        this.watch('vpcId', value => {
            if (value) {
                this.getSecurityGroups();
                this.getInitIpv6Cidr();
            }
        });

        const debounceCheck = _.debounce(() => this.checkIpv6Cidr(), 1000);
        this.watch('formData.clusterIPServiceCIDRIPv6', () => debounceCheck());
    }

    async getInitIpv6Cidr() {
        const payload = this.getClusterPayload('ipv6');
        if (!payload.vpcCIDRIPv6) {
            return;
        }
        const data = await this.$http.recommendClusterIpCidr(payload);
        if (data?.recommendedClusterIPCIDRIPv6s?.[0]) {
            this.data.set(
                'formData.clusterIPServiceCIDRIPv6',
                data.recommendedClusterIPCIDRIPv6s[0],
            );
        }
    }

    checkIpv6Cidr() {
        const clusterIPServiceCIDRIPv6 = this.data.get('formData.clusterIPServiceCIDRIPv6');
        if (!clusterIPServiceCIDRIPv6) {
            this.data.set('isIpv6CidrConflict', false);
        } else {
            if (!cidrRegex.v6({exact: true}).test(clusterIPServiceCIDRIPv6)) {
                this.data.set('isIpv6CidrConflict', false);
            } else {
                this.checkClusterIpv6Cidr();
            }
        }
    }

    async checkClusterIpv6Cidr() {
        const selectedVpc = this.data.get('selectedVpcItem');
        const clusterIPServiceCIDRIPv6 = this.data.get('formData.clusterIPServiceCIDRIPv6');
        if (selectedVpc?.ipv6Cidr && clusterIPServiceCIDRIPv6) {
            let payload = {
                clusterIPCIDRIPv6: clusterIPServiceCIDRIPv6,
                ipVersion: 'ipv6',
                vpcCIDRIPv6: selectedVpc.ipv6Cidr,
                vpcID: selectedVpc.shortId,
            };
            try {
                const res = await this.$http.checkClusterIpCidr(payload, {'x-silent': true});
                if (res?.result?.isConflict === false) {
                    this.data.set('isIpv6CidrConflict', false);
                } else {
                    this.data.set('isIpv6CidrConflict', true);
                }
            } catch (error) {
                this.data.set('isIpv6CidrConflict', true);
            }
        }
    }

    onSubnetsSelected(selectedItems) {
        if (selectedItems.length === 0) {
            this.data.set('formErrors.subnets', '至少选择一个子网');
        } else {
            this.data.set('formErrors.subnets', null);
        }
    }

    onCreateSubnet() {
        const selectedVpcItem = this.data.get('selectedVpcItem');
        if (!selectedVpcItem) {
            return;
        }

        const subnetTable = this.ref('subnetTable');
        if (!subnetTable) {
            return;
        }

        subnetTable.onCreateSubnet();
    }

    async validateForm() {
        const form = this.ref('form');
        await form.validateForm();
        this.data.set('formErrors.subnets', null);
        const subnetTable = this.ref('subnetTable');
        if (subnetTable) {
            const subnets = subnetTable.getFormData();
            if (subnets.vkSubnets.length === 0) {
                this.data.set('formErrors.subnets', '至少选择一个子网');
                return Promise.reject();
            }
        }
        const clusterIpCidr = this.ref('clusterIpCidr');
        if (clusterIpCidr) {
            await clusterIpCidr.validateForm();
        }
        if (this.data.get('IPversion') && this.data.get('isIpv6CidrConflict')) {
            return Promise.reject();
        }
        return Promise.resolve();
    }

    getFormData() {
        const formData = this.data.get('formData');
        const subnetTable = this.ref('subnetTable');
        const subnets = subnetTable ? subnetTable.getFormData() : {};
        const portMode = this.ref('portMode');
        const clusterIpCidr = this.ref('clusterIpCidr');
        // 设置 eBPF 是否启用
        formData.eBPFEnabled = this.data.get('eBPFEnabled');

        return _.extend(
            {
                eniVPCSubnetIDs: subnets.subnets,
                eniVPCSubnetUuids: subnets.subnetIds,
            },
            clusterIpCidr ? clusterIpCidr.getFormData() : {},
            formData,
            portMode ? portMode.getFormData() : {},
        );
    }

    getSecurityGroups() {
        const vpcId = this.data.get('vpcId');
        if (!vpcId) {
            return;
        }
        this.data.set('securityGroups.loading', true);
        return this.$http
            .cceSecurityListSelect({vpcId})
            .then(({page}) => {
                let result = _.map(page.result || [], item => {
                    return {
                        text: getSecurityName(item.name),
                        value: item.securityGroupId,
                        desc: item.desc,
                        name: item.name,
                    };
                });
                const eniSecurityGroupId = this.data.get('formData.eniSecurityGroupId');
                if (!eniSecurityGroupId || !_.find(result, {value: eniSecurityGroupId})) {
                    // 当有多个安全组时默认选中默认安全组
                    const defaultSecurityGroup =
                        _.find(result, item => item.name === 'CCE默认安全组') || result[0];
                    this.data.set(
                        'formData.eniSecurityGroupId',
                        defaultSecurityGroup ? defaultSecurityGroup.value : '',
                    );
                }
                this.data.set('securityGroups.list', result);
            })
            .finally(() => this.data.set('securityGroups.loading', false));
    }

    onConflictDetail(type) {
        const isCluster = type === 'cluster';
        const ipVersion = 'ipv6';

        const networkConflict = new NetworkConflict({
            data: {
                isCluster,
                containerNetError: {},
                payload: this.getClusterPayload(ipVersion),
            },
        });

        networkConflict.attach(document.body);
        networkConflict.on('ok', cidr => {
            if (cidr) {
                this.data.set('formData.clusterIPServiceCIDRIPv6', cidr);
                this.nextTick(() => {
                    this.ref('form')?.validateForm();
                });
            }
        });
    }

    getClusterPayload(ipVersion) {
        const selectedVpc = this.data.get('selectedVpcItem');
        return {
            vpcCIDRIPv6: selectedVpc?.ipv6Cidr,
            containerCIDRIPv6: selectedVpc?.ipv6Cidr,
            clusterMaxServiceNum: 65536,
            privateNetCIDRIPv6s: ['fd00::/8'],
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: ipVersion,
        };
    }
}

@asComponent('@cce-cluster-create-cni')
export default class CniStore extends connect.san(StoreMap)(Cni) {}
