/**
 * @file components/cluster/create-v2/network/conflict.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Dialog, Table, Loading, Select, Button} from '@baiducloud/bce-ui/san';

import {RouteType, ConflictType} from '../../../../utils/enums';

const {asComponent, invokeBceSanUI} = decorators;

/* eslint-disable */
const template = html`
    <template>
        <ui-dialog s-if="open" open="{=open=}" title="容器网络冲突详情" skin="conflict-net">
            <div s-if="networkTable.datasource.length">
                <span class="conflict-title">冲突的容器网络/节点网络：</span>
                <ui-table
                    schema="{{networkTable.schema}}"
                    cell-builder="{{networkTable.cellRenderer}}"
                    datasource="{{networkTable.datasource}}"
                />
            </div>
            <div s-if="containerNetError.conflictVPCRoute">
                <span class="conflict-title">冲突的路由表规则：</span>
                <ui-table
                    schema="{{routeTable.schema}}"
                    cell-builder="{{routeTable.cellRenderer}}"
                    datasource="{{routeTable.datasource}}"
                />
            </div>
            <br />
            <span class="conflict-title">
                建议使用的{{title}}：
                <ui-loading s-if="loading" size="small" />
                <span s-elif="containerNetDatasource.length === 0"
                    >暂无符合当前掩码位数的可用网段，请重选掩码位数</span
                >
                <ui-select
                    s-else
                    datasource="{{containerNetDatasource}}"
                    value="{=containerNet=}"
                />
            </span>
            <div slot="foot">
                <ui-button
                    on-click="onConfirmDialog"
                    disabled="{{containerNetDatasource.length === 0}}"
                    >使用建议的{{title}}</ui-button
                >
                <ui-button on-click="onCloseDialog">取消</ui-button>
            </div>
        </ui-dialog>
    </template>
`;
/* eslint-enable */

@asComponent('@cce-cluster-create-conflict')
@invokeBceSanUI
export default class Conflict extends Component {
    static template = template;
    static components = {
        'ui-dialog': Dialog,
        'ui-table': Table,
        'ui-button': Button,
        'ui-select': Select,
        'ui-loading': Loading,
    };
    initData() {
        return {
            open: true,
            networkTable: {
                schema: [
                    {name: 'netType', label: '网络类型'},
                    {name: 'clusterId', label: '所属CCE集群'},
                    {name: 'netCidr', label: '网段'},
                ],
                cellRenderer(item, key, col, rowIndex) {
                    const value = item[key];
                    if (key === 'netType') {
                        return ConflictType.getTextFromValue(value) || '-';
                    }
                    return item[key] || '-';
                },
            },
            routeTable: {
                schema: [
                    {name: 'sourceAddress', label: '源网段'},
                    {name: 'destinationAddress', label: '目标网段'},
                    {name: 'nexthopId', label: '下一跳'},
                    {name: 'nexthopType', label: '路由类型'},
                    {name: 'description', label: '描述'},
                ],
                cellRenderer(item, key, col, rowIndex) {
                    const value = item[key];
                    if (key === 'nexthopType') {
                        return RouteType.getTextFromValue(value) || '-';
                    }
                    return item[key] || '-';
                },
            },
            loading: false,
            title: '容器网络',
        };
    }

    inited() {
        const {
            containerNetError: {conflictType, conflictNodeCIDR, conflictCluster, conflictVPCRoute},
        } = this.data.get();
        let items = [];
        if (conflictNodeCIDR) {
            items.push({
                netType: conflictType,
                clusterId: '本集群',
                netCidr: conflictNodeCIDR.nodeCIDR,
            });
        }
        if (conflictCluster) {
            items.push({
                netType: conflictType,
                clusterId:
                    conflictCluster.clusterID === 'THIS_CLUSTER'
                        ? '本集群'
                        : conflictCluster.clusterID,
                netCidr: conflictCluster.containerCIDR,
            });
        }
        this.data.set('networkTable.datasource', items);
        this.data.set('routeTable.datasource', [_.get(conflictVPCRoute || {}, 'routeRule', {})]);
        this.getAvailableContainerNet().catch(e => {
            this.data.set('loading', false);
            this.data.set('containerNetDatasource', []);
        });

        const isCluster = this.data.get('isCluster');
        if (isCluster) {
            this.data.set('title', 'ClusterIP网段');
        }
    }

    getAvailableContainerNet() {
        const {isCluster, payload} = this.data.get();
        this.data.set('loading', true);
        if (isCluster) {
            return this.$http.recommendClusterIpCidr(payload).then(res => {
                const datasource = [];
                _.each(res.recommendedClusterIPCIDRs || res.recommendedClusterIPCIDRIPv6s, d => {
                    datasource.push({
                        text: d,
                        value: d,
                    });
                });

                this.data.set('containerNetDatasource', datasource);
                if (datasource && datasource.length > 0) {
                    this.data.set('containerNet', datasource[0].value);
                }

                this.data.set('loading', false);
            });
        }
        return this.$http.recommendContainerCidr(payload).then(res => {
            const datasource = [];
            _.each(res.recommendedContainerCIDRs, d => {
                datasource.push({
                    text: d,
                    value: d,
                });
            });

            this.data.set('containerNetDatasource', datasource);
            if (datasource && datasource.length > 0) {
                this.data.set('containerNet', datasource[0].value);
            }

            this.data.set('loading', false);
        });
    }

    onConfirmDialog() {
        this.fire('ok', this.data.get('containerNet'));
        this.onCloseDialog();
    }

    onCloseDialog() {
        this.data.set('open', false);
    }
}
