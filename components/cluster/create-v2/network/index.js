/**
 * @file components/cluster/create-v2/network/index.js
 * <AUTHOR>
 */

import _, {cloneDeep} from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';

import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import tips from '../../../../utils/tips';
import {ClusterType, PermissionType} from '../../../../utils/enums';
import {compareVersion} from '../../../../utils/util';
import {NetworkMode, NetworkType, EniNetworkMode, CloudNetworkMode} from '../enums';
import {checkWhiteByName} from '../../../../common/white';
import {ClusterTemplateType} from '../template/config';
import {Checkbox} from '@baidu/sui';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';
import './index.less';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;
const $flag = ServiceFactory.resolve('$flag');

const Schema = AsyncValidator.default;

const rules = new Schema({});

/* eslint-disable */
const template = html`<template>
    <ui-biz-legend label="{{title}}">
        <ui-form s-ref="form" rules="{{rules}}" form-data="{=formData=}" errors="{{formErrors}}">
            <ui-form-item inline required label="容器网络模式：" name="mode">
                <ui-radio-select
                    box-type="checkbox"
                    datasource="{{modeDatasource}}"
                    value="{=formData.mode=}"
                    on-change="onModeChange"
                    disabled="{{IPversion}}"
                />
                <a class="network-mode-tip" href="{{networkModeUrl}}" target="_blank">
                    {{networkModeTip}}
                </a>
            </ui-form-item>
            <ui-form-item s-if="isCni && !isARM" inline label="弹性网卡模式：" name="eniMode">
                <ui-radio-select
                    box-type="checkbox"
                    datasource="{{eniModeDatasource}}"
                    value="{=formData.eniMode=}"
                    on-change="onEniModeChange"
                />
                <div class="bui-form-item-help">
                    <template s-if="isExclusiveEni">
                        <span> 每个Pod独占一个弹性网卡，基于eBPF技术提供更高网络性能， </span>
                        <span class="cce-tip-warn"> 操作系统仅支持公共镜像Ubuntu 20.04 </span>
                    </template>
                    <span s-else>
                        多个Pod共享一个弹性网卡，系统为弹性网卡申请多个辅助IP分配给不同的Pod
                    </span>
                </div>
            </ui-form-item>
            <ui-form-item s-if="eBPFSettingVisible && !(isCni && isExclusiveEni)" inline label=" ">
                <div class="eBPF-config-container">
                    <s-checkbox
                        label="开启eBPF增强"
                        checked="{{ formData.eBPFEnabled }}"
                        on-change="on_eBPF_EnabledChange"
                    ></s-checkbox>
                    <span class="eBPF-msg">
                        ，开启后原有Kube-proxy将被替代，
                        <span class="enhanced-tip"
                            >操作系统仅支持公共镜像BaiduLinux 3.0。创建后不支持修改</span
                        >
                    </span>
                    <!-- <div>
                        开启后原有Kube-proxy将被替代，<span class="enhanced-tip"
                            >操作系统仅支持公共镜像Ubuntu 20.04</span
                        >， 更多 <a href="">查看详情</a>
                    </div> -->
                </div>
                <div class="network-policy-container">
                    <s-checkbox
                        label="支持Network Policy"
                        checked="{{formData.networkPolicyEnabled}}"
                        on-change="onNetworkPolicyChange"
                    ></s-checkbox>
                    <span>
                        ，勾选后集群将默认支持基于策略的网络控制，<a
                            href="{{networkPolicyUrl}}"
                            target="_blank"
                            >查看详情</a
                        >
                    </span>
                </div>
                <div s-if="!(isCloudEdge || isARM)" class="enable-rdma">
                    <s-checkbox
                        label="开启RDMA"
                        checked="{{formData.enableRDMA}}"
                        on-change="onRDMAChange"
                    ></s-checkbox>
                </div>
            </ui-form-item>
            <div style="display: {{isVpc || isCloudEdge ? 'block' : 'none'}}">
                <div style="display: {{isAuto ? 'block' : 'none'}}">
                    <cce-cluster-create-recommend s-ref="recommend" on-edit="onEdit" />
                </div>
                <div style="display: {{!isAuto ? 'block' : 'none'}}">
                    <cce-cluster-create-vpc s-ref="vpc" form-data="{{recommendData}}" />
                </div>
                <advanced-config-container open="{=advancedOpen=}">
                    <cce-cluster-create-port-mode
                        s-ref="portMode"
                        mode="{{formData.mode}}"
                        eBPFEnabled="{{formData.eBPFEnabled}}"
                        eBPFSettingVisible="{{eBPFSettingVisible}}"
                    />
                </advanced-config-container>
            </div>
            <div s-if="init[1]" style="display: {{isCni ? 'block' : 'none'}}">
                <cce-cluster-create-cni
                    s-ref="cni"
                    mode="{{formData.mode}}"
                    isExclusiveEni="{{isExclusiveEni}}"
                    eBPFEnabled="{{formData.eBPFEnabled}}"
                    eBPFSettingVisible="{{eBPFSettingVisible}}"
                    isCloudEdge="{{isCloudEdge}}"
                    IPversion="{{IPversion}}"
                />
            </div>
            <div s-if="init[2]" style="display: {{isVpcHybrid ? 'block' : 'none'}}">
                <cce-cluster-create-cluster-ip-cidr s-ref="clusterIpCidr" />
                <cce-cluster-create-port-mode
                    s-ref="hybridPortMode"
                    mode="{{formData.mode}}"
                    eBPFEnabled="{{formData.eBPFEnabled}}"
                    eBPFSettingVisible="{{eBPFSettingVisible}}"
                />
            </div>
        </ui-form>
    </ui-biz-legend>
</template>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp(
    '@cce-cluster-create-recommend',
    '@cce-cluster-create-vpc',
    '@cce-cluster-create-cni',
    '@cce-cluster-create-port-mode',
    '@cce-cluster-create-cluster-ip-cidr',
)
class Network extends Component {
    static template = template;

    static components = {
        's-checkbox': Checkbox,
        'advanced-config-container': AdvancedConfigContainer,
    };

    static computed = {
        isAuto() {
            const type = this.data.get('formData.type');
            return type === NetworkType.AUTO;
        },
        isVpc() {
            const mode = this.data.get('formData.mode');
            return mode === NetworkMode.AUTO_DETECT;
        },
        isCni() {
            const mode = this.data.get('formData.mode');
            return mode === NetworkMode.CNI;
        },
        isVpcHybrid() {
            const mode = this.data.get('formData.mode');
            return mode === NetworkMode.VPC_HYBRID;
        },
        modeDatasource() {
            const isCloudEdge = this.data.get('isCloudEdge');
            if (isCloudEdge) {
                return CloudNetworkMode.toArray();
            }

            let datasource = NetworkMode.toArray();

            const EnableVPCRouter = checkWhiteByName(
                PermissionType.getTextFromValue(PermissionType.EnableVPCRouter),
            );

            if (!EnableVPCRouter) {
                datasource = _.filter(datasource, item => item.alias !== 'AUTO_DETECT');
            }
            return datasource;
        },
        networkModeTip() {
            const isCloudEdge = this.data.get('isCloudEdge');
            return isCloudEdge ? '边缘容器网络模式说明' : '如何选择Kubernetes集群的容器网络模式';
        },
        networkModeUrl() {
            return tips.doc.networkMode;
        },
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },
        isARM() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.ARM;
        },
        eBPFSettingVisible() {
            // 前六种集群模板支持开发 eBPF
            const clusterTemplateType = store.getState('clusterTemplateType');
            return (
                [
                    ClusterTemplateType.CUSTOM,
                    ClusterTemplateType.MANAGEDPRO,
                    ClusterTemplateType.START,
                    ClusterTemplateType.HPC,
                    ClusterTemplateType.BBC,
                    ClusterTemplateType.EXIST,
                ].indexOf(clusterTemplateType) > -1
            );
        },
        doNotNeedKubenet() {
            const rutimeType = this.data.get('runtimeType');
            const k8sVersion = this.data.get('k8sVersion');
            return (
                rutimeType === 'containerd' || (k8sVersion && compareVersion(k8sVersion, '1.22'))
            );
        },
        networkPolicyUrl() {
            return tips.doc.networkPolicy;
        },
        isExclusiveEni() {
            return this.data.get('formData.eniMode') === EniNetworkMode.EXCLUSIVE;
        },
    };

    initData() {
        return {
            title: '容器网络',
            formData: {
                mode: NetworkMode.CNI,
                // type: NetworkType.AUTO,
                type: NetworkType.CUSTOM,
                // 是否开启 eBPF 增强
                eBPFEnabled: false,
                // 是否开启支持 Network Policy
                networkPolicyEnabled: false,
                eniMode: EniNetworkMode.SHARE,
                enableRDMA: false, //rdma
                netDeviceDriver: 'veth-pair', //rdma
            },
            formErrors: null,
            rules,
            typeDatasource: NetworkType.toArray(),
            advancedOpen: false,
            init: [true, false, false],
            eniModeDatasource: EniNetworkMode.toArray(),
        };
    }

    inited() {
        const networkMode = this.data.get('networkMode');
        const EnableVPCHybirdWhite = checkWhiteByName(
            PermissionType.getTextFromValue(PermissionType.ENABLEVPCHYBIRD),
        );

        if (!networkMode || !EnableVPCHybirdWhite) {
            const modeDatasource = this.data.get('modeDatasource');
            if (
                modeDatasource?.length === 1 &&
                modeDatasource[0].value === CloudNetworkMode.AUTO_DETECT
            ) {
                store.dispatch('setNetworkMode', CloudNetworkMode.AUTO_DETECT);
                this.data.set('formData.mode', CloudNetworkMode.AUTO_DETECT);
            } else {
                store.dispatch('setNetworkMode', NetworkMode.AUTO_DETECT);
            }
        } else {
            this.data.set('formData.mode', networkMode);
            this.onModeChange({value: networkMode});
            if (networkMode === NetworkMode.VPC_HYBRID) {
                this.data.set('init', [false, false, true]);
            }
        }

        this.checkEnableExclusiveENI();
    }

    attached() {
        this.data.set('formData.mode', NetworkMode.CNI);
        this.onModeChange({value: NetworkMode.CNI});
        this.watch('IPversion', value => {
            if (value) {
                this.data.set('formData.mode', NetworkMode.CNI);
                this.onModeChange({value: NetworkMode.CNI});
            }
        });
    }

    onEdit(e) {
        this.data.set('formData.type', NetworkType.CUSTOM);

        this.data.set('recommendData', e.value);
    }

    validateForm() {
        const {mode, type} = this.data.get('formData');
        if (mode === NetworkMode.AUTO_DETECT) {
            if (type === NetworkType.AUTO) {
                const recommend = this.ref('recommend');
                return recommend.validateForm();
            }

            const vpc = this.ref('vpc');
            return vpc.validateForm();
        } else if (mode === NetworkMode.CNI) {
            const cni = this.ref('cni');
            return cni.validateForm();
        }

        const clusterIpCidr = this.ref('clusterIpCidr');
        return clusterIpCidr?.validateForm() || Promise.resolve();
    }

    getFormData() {
        const formData = this.data.get('formData');
        const {
            mode,
            type,
            eniMode,
            eBPFEnabled,
            networkPolicyEnabled,
            enableRDMA,
            netDeviceDriver,
        } = formData;
        let data = {};

        if (mode === NetworkMode.AUTO_DETECT || mode === CloudNetworkMode.AUTO_DETECT) {
            if (type === NetworkType.AUTO) {
                const recommend = this.ref('recommend');
                data = recommend.getFormData();
            } else {
                const vpc = this.ref('vpc');
                data = vpc.getFormData();
            }

            const portMode = this.ref('portMode');

            return _.extend({}, portMode ? portMode.getFormData() : {}, formData, data);
        } else if (mode === NetworkMode.CNI) {
            const cni = this.ref('cni');
            data = cni.getFormData();
        } else {
            const hybridPortMode = this.ref('hybridPortMode');
            const clusterIpCidr = this.ref('clusterIpCidr');
            data = _.extend(
                {},
                hybridPortMode ? hybridPortMode.getFormData() : {},
                clusterIpCidr ? clusterIpCidr.getFormData() : {},
            );
        }

        return _.extend(
            {
                mode,
                eniMode,
                eBPFEnabled,
                networkPolicyEnabled,
                enableRDMA,
                netDeviceDriver,
            },
            data,
        );
    }

    onModeChange(e) {
        store.dispatch('setNetworkMode', e.value);

        const init = this.data.get('init');
        if (e.value === NetworkMode.AUTO_DETECT && !init[0]) {
            this.data.set('init[0]', true);
        } else if (e.value === NetworkMode.CNI && !init[1]) {
            this.data.set('init[1]', true);
        } else if (e.value === NetworkMode.VPC_HYBRID && !init[2]) {
            this.data.set('init[2]', true);
        }
    }

    onEniModeChange(e) {
        store.dispatch('setIsExclusiveEni', e.value === EniNetworkMode.EXCLUSIVE);
    }

    on_eBPF_EnabledChange(e) {
        const targetValue = e.value;
        this.data.set('formData.eBPFEnabled', targetValue);
        store.dispatch('setEBPFEnabled', targetValue);
    }

    onNetworkPolicyChange(e) {
        const targetValue = e.value;
        this.data.set('formData.networkPolicyEnabled', targetValue);
    }

    onRDMAChange(e) {
        this.data.set('formData.enableRDMA', e.value);
    }

    async checkEnableExclusiveENI() {
        // VPC-ENI支持Pod独占弹性网卡白名单检查
        const res = await this.$http.checkWhiteList('enableExclusiveENI');

        if (!res?.isExist) {
            // 不在白名单则只展示共享弹性网卡
            this.data.set('eniModeDatasource', [EniNetworkMode.fromValue(EniNetworkMode.SHARE)]);
        }
    }
}
@asComponent('@cce-cluster-create-network')
export default class NetworkStore extends connect.san(StoreMap)(Network) {}
