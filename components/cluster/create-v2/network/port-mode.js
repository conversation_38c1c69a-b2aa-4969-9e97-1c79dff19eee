/**
 * @file components/cluster/create-v2/network/port-mode.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {connect, store} from 'san-store';
import * as AsyncValidator from 'async-validator';

import {KubeProxyType} from '../../../../utils/enums';
import {checkIpInCidr} from '../../../../utils/network';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {NetworkMode} from '../enums';
import {ClusterTemplateType} from '../template/config';

const {asComponent, invokeBceSanUI} = decorators;

const DefaultKubeProxyMode = KubeProxyType.getValueFromAlias('Ipvs');

const toNumberValue = v => (v ? parseInt(v, 10) : 0);
const compareVersion = (source, dest) => {
    const srcArr = source.split('.');
    const destArr = dest.split('.');
    const index = Math.max(srcArr.length, destArr.length);
    let result = false;
    for (let i = 0; i < index; i++) {
        const src = toNumberValue(srcArr[i]);
        const dst = toNumberValue(destArr[i]);
        if (src > dst) {
            result = true;
            break;
        }
        if (src === dst) {
            continue;
        }
        break;
    }
    return result;
};

const DefaultFormData = {
    nodePortRangeMax: 32767,
    nodePortRangeMin: 30000,
    kubeProxyMode: DefaultKubeProxyMode,
    enableNodeLocalDNS: false,
    nodeLocalDNSAddr: '*************',
};

const Schema = AsyncValidator.default;

const rules = new Schema({
    nodeLocalDNSAddr: {
        validator(rule, value, callback, source) {
            if (!source.enableNodeLocalDNS) {
                return callback();
            }
            if (!value) {
                return callback('请填写本地IP');
            }

            if (
                !/^(([01]?\d?\d|2[0-4]\d|25[0-5])\.){3}([01]?\d?\d|2[0-4]\d|25[0-5])$/.test(value)
            ) {
                return callback('IP地址格式不正确');
            }

            if (!checkIpInCidr('***********/16', value)) {
                return callback('需要在***********/16网段');
            }

            callback();
        },
    },
});

/* eslint-disable */
const template = html`<div class="cce-cluster-create-port-mode">
    <ui-form
        form-data="{=formData=}"
        rules="{{rules}}"
        errors="{{formErrors}}">
        <ui-form-item
            inline
            label="Nodeport范围："
            name="nodePortRange">
            <ui-number-textline
                value="{=formData.nodePortRangeMin=}"
                min="{{portRangeMin}}"
                max="{{formData.nodePortRangeMax - 1}}" />
            <span>-</span>
            <ui-number-textline
                value="{=formData.nodePortRangeMax=}"
                min="{{formData.nodePortRangeMin + 1}}"
                max="{{portRangeMax}}" />
            <p class="cce-tip-grey" s-if="internalUser">
                由于您的账户属于百度内部用户，根据公司安全规定，我们默认将您的Nodeport范围指定<br>
                为8000-9000，建议您不要修改该范围，以防因此导致网络访问问题。
            </p>
        </ui-form-item>
        <ui-form-item
            inline
            label="Kube-proxy模式："
            name="kubeProxyMode"
            s-if="showKubeProxyMode">
            <ui-radio-select
                disabled="{{!hasKubeProxyMode || IPversion}}"
                value="{=formData.kubeProxyMode=}"
                datasource="{{kubeProxyModes.datasource}}" />
            <ui-tip
                message="{{kubeProxyModeTip}}"
                layer-width="300">
            </ui-tip>
        </ui-form-item>
        <ui-form-item
            inline
            label="NodeLocalDNS："
            name="enableNodeLocalDNS"
            s-if="showNodeLocalDNS">
            <ui-switch
                checked="{=formData.enableNodeLocalDNS=}" />
            <ui-tip
                message="通过在集群节点上以Daemonset形式运行NodeLocal DNS Cache，以提高集群DNS性能"
                layer-width="300">
            </ui-tip>
            <a class="port-mode-dns-link" href="https://kubernetes.io/docs/tasks/administer-cluster/nodelocaldns/" target="_blank">集群使用NodeLocal DNSCache说明</a>

            <ui-form-item
                inline
                name="nodeLocalDNSAddr"
                s-if="formData.enableNodeLocalDNS">
                <span class="port-mode-local-ip">本地IP</span>
                <ui-text-box value="{=formData.nodeLocalDNSAddr=}" />
                </ui-tip>
            </ui-form-item>
        </ui-form-item>
    </ui-form-item>
    </ui-form>
</div>`;
/* eslint-enable */

@invokeBceSanUI
class PortMode extends Component {
    static template = template;

    static computed = {
        hasKubeProxyMode() {
            const k8sVersion = this.data.get('k8sVersion');
            return k8sVersion && compareVersion(k8sVersion, '1.11');
        },
        kubeProxyModeTip() {
            let tips = [
                'ipvs将会提高集群的网络性能，但是会增加一定的资源损耗。',
                '目前kube-proxy模式只对Linux节点生效，Windows节点默认使用kernel。',
            ];
            const hasKubeProxyMode = this.data.get('hasKubeProxyMode');
            if (!hasKubeProxyMode) {
                tips.unshift('仅支持1.11以上版本。');
            }

            return tips.join('');
        },
        showNodeLocalDNS() {
            const enableNodeLocalDNS = this.data.get('enableNodeLocalDNS');
            const mode = this.data.get('mode');
            const kubeProxyMode = this.data.get('formData.kubeProxyMode');
            const eBPFSettingVisible = this.data.get('eBPFSettingVisible');
            const clusterTemplateType = store.getState('clusterTemplateType');
            return (
                enableNodeLocalDNS &&
                (mode === NetworkMode.AUTO_DETECT ||
                    (mode === NetworkMode.CNI &&
                        (eBPFSettingVisible || clusterTemplateType === ClusterTemplateType.ARM))) &&
                kubeProxyMode === KubeProxyType.Ipvs
            );
        },
        showKubeProxyMode() {
            const mode = this.data.get('mode');
            const isExclusiveEni = this.data.get('isExclusiveEni');
            const eBPFEnabled = this.data.get('eBPFEnabled');
            return mode !== NetworkMode.VPC_HYBRID && !isExclusiveEni && !eBPFEnabled;
        },
    };

    initData() {
        return {
            rules,
            formErrors: null,
            formData: DefaultFormData,
            kubeProxyModes: {
                datasource: KubeProxyType.toArray('Ipvs', 'Iptables'),
            },
            portRangeMax: 65535,
            portRangeMin: 0,
            enableNodeLocalDNS: true,
        };
    }

    attached() {
        store.dispatch('checkInternalUser');

        const internalUser = this.data.get('internalUser');
        if (!_.isUndefined(internalUser)) {
            this.updatePortRange(internalUser);
        }

        this.watch('hasKubeProxyMode', value => {
            if (!value) {
                this.data.set('formData.kubeProxyMode', KubeProxyType.Iptables);
            }
        });

        this.watch('internalUser', value => this.updatePortRange(value));

        this.watch('IPversion', value => {
            if (value) {
                this.data.set('formData.kubeProxyMode', KubeProxyType.Ipvs);
            }
        });

        this.watch('isExclusiveEni', value => {
            if (value) {
                // 独占弹性网卡模式下Kube-proxy默认选择“IPVS”
                this.data.set('formData.kubeProxyMode', KubeProxyType.Ipvs);
            }
        });
    }

    updatePortRange(internalUser) {
        // 内部用户的范围是8000-9000

        if (internalUser) {
            this.data.set('formData.nodePortRangeMin', 8000);
            this.data.set('formData.nodePortRangeMax', 9000);
        }
    }

    validateForm() {
        const form = this.ref('form');
        return form.validateForm();
    }

    getFormData() {
        const formData = this.data.get('formData');

        return formData;
    }
}

@asComponent('@cce-cluster-create-port-mode')
export default class PortModeStore extends connect.san(StoreMap)(PortMode) {}
