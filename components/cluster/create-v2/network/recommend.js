/**
 * @file components/cluster/create-v2/network/recommend.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect} from 'san-store';

import StoreMap from '../../../../pages/cluster/create-v2/store/map';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const Schema = AsyncValidator.default;

const rules = new Schema({
    containerPrivateNetCIDRs: {
        validator(rule, value, callback, source) {
            if (
                (source.step === 0 || source.step === 1) &&
                (!value || (value && value.length === 0))
            ) {
                return callback('至少选择一个');
            }

            callback();
        },
    },
    clusterPrivateNetCIDRs: {
        validator(rule, value, callback, source) {
            if (source.step === 2 && (!value || (value && value.length === 0))) {
                return callback('至少选择一个');
            }

            callback();
        },
    },
});

const ClusterMaxServiceNumData = [64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536];
const PrivateNetCIDRsData = ['10.0.0.0/8', '172.16.0.0/12', '192.168.0.0/16'];
const MaxPodsPerNodeData = [32, 64, 128, 256, 512];
const ClusterMaxNodeNumData = [50, 200, 1000];

const DefaultFormData = {
    maxPodsPerNode: '128',
    clusterMaxNodeNum: '200',
    clusterMaxServiceNum: '8192',
    containerPrivateNetCIDRs: PrivateNetCIDRsData,
    clusterPrivateNetCIDRs: PrivateNetCIDRsData,
};

/* eslint-disable */
const template = html`<template>
    <div class="cluster-create-network-recommend">
        <div class="recommend-content-wrap">
            <div class="recommend-content">
                <div class="content-item content-left">
                    <ui-form s-ref="form"
                        rules="{{rules}}"
                        formData="{=formData=}"
                        form-errors="{{recommendErrors}}">
                        <ui-form-item
                            inline
                            label="单节点最大容器组数："
                            name="maxPodsPerNode">
                            <ui-select
                                width="240"
                                value="{=formData.maxPodsPerNode=}"
                                datasource="{{maxPodsPerNode.datasource}}"
                                on-change="onResetClusterPodCidr" />
                        </ui-form-item>
                        <ui-form-item
                            inline
                            label="集群最大节点数："
                            name="clusterMaxNodeNum">
                            <ui-select
                                width="240"
                                value="{=formData.clusterMaxNodeNum=}"
                                datasource="{{nums.datasource}}"
                                on-change="onClusterMaxNodeNumChange" />
                        </ui-form-item>
                        <ui-form-item
                            inline
                            label="容器组网段区间："
                            name="containerPrivateNetCIDRs">
                            <ui-select
                                multi
                                width="240"
                                checked-all="{{true}}"
                                value="{=formData.containerPrivateNetCIDRs=}"
                                datasource="{{privateNetCIDRs.datasource}}"
                                on-change="onResetClusterPodCidr" />
                        </ui-form-item>
                        <ui-form-item
                            inline
                            label="集群最大服务数："
                            name="clusterMaxServiceNum">
                            <ui-select
                                width="240"
                                disabled="{{!formData.clusterPodCIDR}}"
                                value="{=formData.clusterMaxServiceNum=}"
                                datasource="{{clusterMaxServiceNum.datasource}}"
                                on-change="onResetClusterIpServiceCidr" />
                        </ui-form-item>
                        <ui-form-item
                            inline
                            label="Cluster IP 网段区间："
                            name="clusterPrivateNetCIDRs">
                            <ui-select
                                multi
                                width="240"
                                disabled="{{!formData.clusterPodCIDR}}"
                                checked-all="{{true}}"
                                value="{=formData.clusterPrivateNetCIDRs=}"
                                datasource="{{privateNetCIDRs.datasource}}"
                                on-change="onResetClusterIpServiceCidr" />
                        </ui-form-item>
                    </ui-form>
                </div>
                <div class="content-item content-right">
                    <ui-biz-legend label="推荐配置">
                        <div slot="extra">
                            <div
                                class="recommend-legend-extra"
                                s-if="canRecommend">
                                <ui-button
                                    on-click="onRefreshRecommend">
                                    刷新
                                </ui-button>
                            </div>
                            <a s-else
                                class="recommend-legend-edit"
                                href="javascript:void(0)"
                                on-click="onClickEdit">应用到自定义配置
                            </a>
                        </div>
                        <p class="recommend-item">
                            <label>容器网段：</label>
                            <ui-loading s-if="containerLoading" size="small" />
                            <span s-elif="clusterPodCIDRError" class="cluster-create-recommend-error">推荐失败</span>
                            <span s-else>
                                <ui-select
                                    s-if="formData.clusterPodCIDR && recommendedContainerCIDRs && recommendedContainerCIDRs.length > 0"
                                    value="{=formData.clusterPodCIDR=}"
                                    datasource="{{recommendedContainerCIDRs}}"
                                    width="175">
                                </ui-select>
                                <span s-else>请完成选择后点击刷新按钮</span>
                            </span>
                            <div s-if="isIPv6" class="pod-create-recommend-ipv6">
                                <ui-select
                                    s-if="formData.clusterPodCIDRIPv6 && recommendedContainerCIDRIPv6s && recommendedContainerCIDRIPv6s.length > 0"
                                    value="{=formData.clusterPodCIDRIPv6=}"
                                    datasource="{{recommendedContainerCIDRIPv6s}}"
                                    width="240">
                                </ui-select>
                            </div>
                        </p>
                        <p class="recommend-item">
                            <label>节点Pod数：</label>
                            <span>{{formData.maxPodsPerNode || '-'}}</span>
                        </p>
                        <p class="recommend-item">
                            <label>ClusterIP网段：</label>
                            <ui-loading s-if="clusterLoading" size="small" />
                            <span s-elif="clusterIPServiceCIDRError" class="cluster-create-recommend-error">推荐失败</span>
                            <span s-else>
                                <ui-select
                                    s-if="formData.clusterIPServiceCIDR && recommendedClusterIPCIDRs && recommendedClusterIPCIDRs.length > 0"
                                    value="{=formData.clusterIPServiceCIDR=}"
                                    datasource="{{recommendedClusterIPCIDRs}}"
                                    width="175">
                                </ui-select>
                                <span s-elif="formData.clusterPodCIDR">请完成选择后点击刷新按钮</span>
                                <span s-else>-</span>
                            </span>
                            <div s-if="isIPv6" class="cluster-create-recommend-ipv6">
                                <ui-select
                                    s-if="formData.clusterIPServiceCIDRIPv6 && recommendedClusterIPCIDRIPv6s && recommendedClusterIPCIDRIPv6s.length > 0"
                                    value="{=formData.clusterIPServiceCIDRIPv6=}"
                                    datasource="{{recommendedClusterIPCIDRIPv6s}}"
                                    width="175">
                                </ui-select>
                            </div>
                        </p>
                        <p class="recommend-item">
                            <cce-cluster-create-port-mode
                                s-ref="portMode" />
                        </p>
                    </ui-biz-legend>
                </div>
            </div>
        </div>
        <div class="cce-error" s-if="recommendError">{{recommendError}}</div>
    </div>
</template>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp('@cce-cluster-create-port-mode')
class Recommend extends Component {
    static template = template;

    static computed = {
        canRecommend() {
            const data = this.data.get('formData');
            if (!data.clusterPodCIDR && !data.clusterIPServiceCIDR) {
                return true;
            }

            if (data.clusterPodCIDR && !data.clusterIPServiceCIDR) {
                return true;
            }

            return false;
        },
    };

    initData() {
        return {
            rules,
            formData: {step: 0, ...DefaultFormData},
            maxPodsPerNode: {
                datasource: _.map(MaxPodsPerNodeData, item => {
                    return {text: item + '', value: item + ''};
                }),
            },
            nums: {
                datasource: _.map(ClusterMaxNodeNumData, item => {
                    return {text: item + '', value: item + ''};
                }),
            },
            privateNetCIDRs: {
                datasource: _.map(PrivateNetCIDRsData, item => {
                    return {text: item, value: item};
                }),
            },
            clusterMaxServiceNum: {
                datasource: _.map(ClusterMaxServiceNumData, item => {
                    return {text: item + '', value: item + ''};
                }),
            },
        };
    }

    inited() {
        const IPversion = this.data.get('IPversion');
        this.data.set('isIPv6', IPversion);
        this.data.set('IPversionValue', IPversion ? 'dualStack' : 'ipv4');
    }

    onCheckContainerNet() {
        this.checkContainerNet();
    }

    onRefreshRecommend() {
        const form = this.ref('form');
        return form.validateForm().then(() => this.refreshRecommend());
    }

    onClusterMaxNodeNumChange(e) {
        this.onResetClusterPodCidr();
    }

    onResetClusterPodCidr() {
        this.data.set('formData.clusterPodCIDR', '');

        this.onResetClusterIpServiceCidr();
    }

    onResetClusterIpServiceCidr() {
        this.data.set('formData.clusterIPServiceCIDR', '');
    }

    refreshClusterPodCidr() {
        this.data.set('clusterPodCIDRError', false);
        this.data.set('containerLoading', true);
        const selectedVpc = this.data.get('selectedVpcItem');
        let privateNetCIDRs = this.data.get('formData.containerPrivateNetCIDRs');
        let payload = {
            vpcID: selectedVpc.value,
            vpcCIDR: selectedVpc.cidr,
            maxPodsPerNode: +this.data.get('formData.maxPodsPerNode'),
            clusterMaxNodeNum: +this.data.get('formData.clusterMaxNodeNum'),
            privateNetCIDRs,
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: this.data.get('IPversionValue'),
        };
        const isIPv6 = this.data.get('isIPv6');
        if (isIPv6) {
            payload.vpcCIDRIPv6 = selectedVpc.ipv6Cidr;
            payload.privateNetCIDRIPv6s = ['fc00::/7'];
        }
        return this.$http
            .recommendContainerCidr(payload)
            .then(data => {
                if (data.isSuccess) {
                    this.data.set(
                        'recommendedContainerCIDRs',
                        _.map(data.recommendedContainerCIDRs, value => {
                            return {value, text: value};
                        }),
                    );
                    this.data.set(
                        'formData.clusterPodCIDR',
                        data.recommendedContainerCIDRs ? data.recommendedContainerCIDRs[0] : '',
                    );

                    this.data.set(
                        'recommendedContainerCIDRIPv6s',
                        _.map(data.recommendedContainerCIDRIPv6s, value => {
                            return {value, text: value};
                        }),
                    );
                    this.data.set(
                        'formData.clusterPodCIDRIPv6',
                        data.recommendedContainerCIDRIPv6s
                            ? data.recommendedContainerCIDRIPv6s[0]
                            : '',
                    );
                } else {
                    this.data.set('recommendedContainerCIDRs', []);
                    this.data.set('formData.clusterPodCIDR', '');
                    this.data.set('recommendedContainerCIDRIPv6s', []);
                    this.data.set('formData.clusterPodCIDRIPv6', '');
                    this.data.set('clusterPodCIDRError', true);
                }
            })
            .catch(() => {
                this.data.set('recommendedContainerCIDRs', []);
                this.data.set('formData.clusterPodCIDR', '');
                this.data.set('recommendedContainerCIDRIPv6s', []);
                this.data.set('formData.clusterPodCIDRIPv6', '');
                this.data.set('clusterPodCIDRError', true);
            })
            .finally(() => this.data.set('containerLoading', false));
    }

    refreshClusterIpServiceCidr() {
        this.data.set('clusterIPServiceCIDRError', false);
        this.data.set('clusterLoading', true);
        const selectedVpc = this.data.get('selectedVpcItem');
        let privateNetCIDRs = this.data.get('formData.clusterPrivateNetCIDRs');
        let payload = {
            vpcCIDR: selectedVpc.cidr,
            containerCIDR: this.data.get('formData.clusterPodCIDR'),
            clusterMaxServiceNum: +this.data.get('formData.clusterMaxServiceNum'),
            privateNetCIDRs,
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: this.data.get('IPversionValue'),
        };
        const isIPv6 = this.data.get('isIPv6');
        if (isIPv6) {
            payload.vpcCIDRIPv6 = selectedVpc.ipv6Cidr;
            payload.privateNetCIDRIPv6s = ['fc00::/7'];
            payload.containerCIDRIPv6 = this.data.get('formData.clusterPodCIDRIPv6');
        }
        return this.$http
            .recommendClusterIpCidr(payload)
            .then(data => {
                if (data.isSuccess) {
                    this.data.set(
                        'recommendedClusterIPCIDRs',
                        _.map(data.recommendedClusterIPCIDRs, value => {
                            return {value, text: value};
                        }),
                    );
                    this.data.set(
                        'formData.clusterIPServiceCIDR',
                        data.recommendedClusterIPCIDRs ? data.recommendedClusterIPCIDRs[0] : '',
                    );

                    this.data.set(
                        'recommendedClusterIPCIDRIPv6s',
                        _.map(data.recommendedClusterIPCIDRIPv6s, value => {
                            return {value, text: value};
                        }),
                    );
                    this.data.set(
                        'formData.clusterIPServiceCIDRIPv6',
                        data.recommendedClusterIPCIDRIPv6s
                            ? data.recommendedClusterIPCIDRIPv6s[0]
                            : '',
                    );
                } else {
                    this.data.set('recommendedClusterIPCIDRs', []);
                    this.data.set('formData.clusterIPServiceCIDR', '');
                    this.data.set('recommendedClusterIPCIDRIPv6s', []);
                    this.data.set('formData.clusterIPServiceCIDRIPv6', '');
                    this.data.set('clusterIPServiceCIDRError', true);
                }
            })
            .catch(() => {
                this.data.set('recommendedClusterIPCIDRs', []);
                this.data.set('formData.clusterIPServiceCIDR', '');
                this.data.set('recommendedClusterIPCIDRIPv6s', []);
                this.data.set('formData.clusterIPServiceCIDRIPv6', '');
                this.data.set('clusterIPServiceCIDRError', true);
            })
            .finally(() => this.data.set('clusterLoading', false));
    }

    refreshRecommend() {
        const form = this.ref('form');
        return form.validateForm().then(() => {
            const formData = this.data.get('formData');
            if (!formData.clusterPodCIDR) {
                return this.refreshClusterPodCidr();
            }

            return this.refreshClusterIpServiceCidr();
        });
    }

    onClickEdit() {
        const formData = this.data.get('formData');
        const portMode = this.ref('portMode');
        this.fire('edit', {value: _.extend({}, formData, portMode.getFormData())});
    }

    validateForm() {
        const formData = this.data.get('formData');
        const isIPv6 = this.data.get('isIPv6');

        this.data.set('recommendError', null);

        if (!formData.clusterPodCIDR || (isIPv6 && !formData.clusterPodCIDRIPv6)) {
            this.data.set('recommendError', '请完成容器网段的推荐配置');
            return Promise.reject();
        }

        if (!formData.clusterIPServiceCIDR || (isIPv6 && !formData.clusterIPServiceCIDRIPv6)) {
            this.data.set('recommendError', '请完成ClusterIP网段的推荐配置');
            return Promise.reject();
        }

        return Promise.resolve();
    }

    getFormData() {
        const portMode = this.ref('portMode');
        return _.extend({}, this.data.get('formData'), portMode.getFormData());
    }
}

@asComponent('@cce-cluster-create-recommend')
export default class RecommendStore extends connect.san(StoreMap)(Recommend) {}
