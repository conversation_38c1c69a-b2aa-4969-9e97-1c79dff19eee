.cce-create-network-cidr {
    display: inline-block;
    zoom: 1;

    .cidr-dot {
        vertical-align: middle;
        margin: 0 3px;
    }

    .bui-select {
        min-width: 80px;
    }

    .bui-textbox {
        background-color: #fff;
    }

    .bui-textbox.state-disabled {
        background-color: #f5f5f5;
    }
}

.bui-form-item-nodePortRange {
    .bui-numbertextline {
        display: inline-block;
        zoom: 1;
        vertical-align: middle;
        width: 114px;

        .bui-textbox input {
            width: 94px !important;
        }

        .bui-numbertextline-arrow-increase,
        .bui-numbertextline-arrow-decrease {
            left: 94px;
        }
    }
}

.cluster-create-network-recommend {
    .bui-legend {
        margin-top: 0;
        padding: 10px 20px;

        .highlight {
            position: relative;
        }
    }

    .recommend-legend-extra {
        position: absolute;
        right: 0;
    }

    .recommend-legend-edit {
        font-size: 12px;
        margin-left: 10px;
    }

    .recommend-content-wrap {
        display: flex;
        align-items: center;
    }

    .recommend-content {
        flex-grow: 3;
    }

    .recommend-content {
        display: flex;
    }

    .content-left {
        width: 40%;

        .bui-form {
            .bui-form-item {
                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }

    .content-right {
        width: 450px;
    }

    .recommend-item {
        padding: 10px;

        a {
            margin: 0 5px;
        }

        label {
            display: inline-block;
            zoom: 1;
            width: 120px;
        }

        .bui-form {
            .bui-form-item {
                margin: 20px 0 0 0;

                &:first-child {
                    margin: 0;
                }
            }
        }
    }

    .bui-form-item-invalid-label {
        display: block;
        margin-top: 5px;
    }
}

.cluster-create-recommend-error {
    color: #f33e3e;
}

.pod-create-recommend-ipv6 {
    margin-left: 63px;
    margin-top: 10px;
}

.cluster-create-recommend-ipv6 {
    margin-left: 91px;
    margin-top: 10px;
}

.bui-form {
    .bui-form-item.bui-form-item-advancedMode {
        .bui-form-item-help {
            color: #ff9326;
        }
    }
}

.cce-create-network-vpc {
    margin-top: -10px;

    .cce-tip-warn {
        margin-top: 5px;
        max-width: 810px;
    }

    .network-custom-wrap {
        margin-top: 10px;
        padding: 16px;
        background: #f7f7f9;

        .bui-form-item {
            &:first-child {
                margin-top: 0;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        .vpc-conflict-btn {
            margin: 0 5px;
        }
    }

    .cce-error {
        margin-top: 5px;
    }

    .num-highlight {
        color: #f38900 !important;
        border-bottom: 1px dashed #84868c;
    }
}

.num-highlight {
    b {
        color: #f38900 !important;
    }
    color: #f38900;
    border-bottom: 1px dashed #84868c;
}

.cce-cluster-ip-cidr {
    .num-highlight {
        color: #f38900;
        border-bottom: 1px dashed #84868c;
    }
}

.cce-cluster-create-port-mode {
    .port-mode-dns-link {
        margin-left: 20px;
    }

    .bui-form {
        .bui-form-item.bui-form-item-nodeLocalDNSAddr {
            margin: 10px 0 0 0;
        }
    }
}
