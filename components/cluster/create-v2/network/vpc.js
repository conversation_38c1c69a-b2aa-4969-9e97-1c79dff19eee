/**
 * @file components/cluster/create-v2/network/vpc.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect, store} from 'san-store';

import {ConflictType, ClusterType} from '../../../../utils/enums';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import tips from '../../../../utils/tips';
import NetworkConflict from './conflict';
import {checkCidrConflict, CloudClusterCIDR, BecVmCIDR, BecLbCIDR} from '../../../../utils/network';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;

const Schema = AsyncValidator.default;

const rules = new Schema({});

const PRIVATE_NET_CIDRS_DATA = ['10.0.0.0/8', '**********/12', '***********/16'];
const PRIVATE_NET_CIDRS_DATA_JILI = ['***********/16'];
const MAX_PODS_PER_NODE = [512, 256, 128, 64];

const $flag = ServiceFactory.resolve('$flag');

/* eslint-disable */
const template = html`<div class="cce-create-network-vpc">
    <ui-form s-ref="form" form-data="{=formData=}" form-errors="{{formErrors}}" rules="{{rules}}">
        <ui-form-item inline required label="容器网络配置：">
            <label slot="label" title="容器网络配置：">容器网络配置：</label>
            <div s-if="!isCloudEdge" class="cce-tip-warn">
                请您务必在选择网段前，合理有效地规划集群的网络，避免出现网络冲突，否则可能存在风险。
                <a href="${tips.doc.clusterNetwork}" target="_blank">CCE集群网络说明及规划</a>
            </div>
            <div class="network-custom-wrap">
                <ui-form-item inline label="容器网段：" name="containerCidr">
                    <label slot="label" title="容器网段：">容器网段：</label>
                    <div s-if="!isCloudEdge">
                        <cce-cluster-create-cidr
                            s-ref="containerCidr"
                            cidr="{{formData.clusterPodCIDR}}"
                            message="{{containerCidrMessage}}"
                            on-change="onContainerCidrChange($event)"
                        />
                        <ui-loading s-if="checkLoading" size="small" />
                        <div s-else>
                            <div
                                s-if="containerNetError && containerNetError.containerCIDRConflict"
                                class="cce-error"
                            >
                                <span s-if="containerNetError.containerCIDRConflict.message">
                                    {{containerNetError.containerCIDRConflict.message}}
                                    <a
                                        href="javascript:void(0)"
                                        on-click="onConflictDetail('container')"
                                        class="vpc-conflict-btn"
                                        >推荐</a
                                    >
                                    <a
                                        s-if="ignoreNetworkConflict"
                                        href="javascript:void(0)"
                                        on-click="onIgnoreConflict()"
                                        class="vpc-conflict-btn"
                                        >忽略</a
                                    >
                                </span>
                                <span s-else>容器网段冲突</span>
                            </div>
                        </div>
                        <div s-if="{{formData.clusterPodCIDRIPv6}}">
                            {{formData.clusterPodCIDRIPv6}}
                        </div>
                    </div>
                    <div s-if="isCloudEdge">
                        <span>自动分配</span>
                        <ui-tip
                            message="容器网段为Pod所在网段，边缘集群为每个边缘站点自动分配容器网段，无需进行设置"
                        />
                        <div class="cce-tip-grey">
                            每个边缘站点都有固定的容器网段
                            <br />
                            集群在单个边缘站点中，最多允许部署<span class="num-highlight">256</span
                            >个工作节点
                        </div>
                    </div>
                </ui-form-item>
                <ui-form-item
                    s-if="!isCloudEdge"
                    inline
                    label="预留容器IP数："
                    name="maxPodsPerNode"
                    help="{{maxPodsContent}}"
                >
                    <label slot="label" title="预留容器IP数：">预留容器IP数：</label>
                    <ui-select
                        width="176"
                        value="{=formData.maxPodsPerNode=}"
                        datasource="{{maxPodsPerNodeDatasource}}"
                        on-change="onMaxPodsPerNodeChange($event)"
                    />
                </ui-form-item>
                <ui-form-item
                    inline
                    label="ClusterIP网段："
                    name="clusterCidr"
                    help="{{clusterIPContent}}"
                >
                    <label slot="label" title="ClusterIP网段：">ClusterIP网段：</label>
                    <cce-cluster-create-cidr
                        s-ref="clusterCidr"
                        cidr="{{formData.clusterIPServiceCIDR}}"
                        message="{{clusterCidrMessage}}"
                        on-change="onClusterCidrChange($event)"
                        is-cloud-edge="{{isCloudEdge}}"
                        min-mask="12"
                        max-mask="{{maxMask}}"
                    />
                    <ui-loading s-if="checkLoading" size="small" />
                    <div s-else>
                        <div
                            s-if="containerNetError && containerNetError.clusterIPCIDRConflict"
                            class="cce-error"
                        >
                            <span s-if="containerNetError.clusterIPCIDRConflict.message">
                                {{containerNetError.clusterIPCIDRConflict.message}}
                                <a href="javascript:void(0)" on-click="onConflictDetail('cluster')"
                                    >推荐</a
                                >
                            </span>
                            <span s-else>ClusterIP网段冲突</span>
                        </div>
                    </div>
                    <div s-if="{{formData.clusterIPServiceCIDRIPv6}}">
                        {{formData.clusterIPServiceCIDRIPv6}}
                    </div>
                </ui-form-item>
            </div>
        </ui-form-item>
    </ui-form>
</div>`;
/* eslint-enable */

@invokeBceSanUI
@invokeComp('@cce-cluster-create-cidr')
class Vpc extends Component {
    static template = template;

    static computed = {
        maxPodsContent() {
            const maxPods = this.data.get('maxPods');
            const span = '<span class="num-highlight">';
            const maxPodsPerNode = this.data.get('formData.maxPodsPerNode');
            return (
                `每个节点预留的容器IP个数。当前容器网段配置下，每个节点分配${span}${maxPodsPerNode}</span>` +
                `个容器IP时，集群最多可容纳${span}${maxPods}</span>个节点。${span}创建后不支持修改</span>`
            );
        },
        clusterIPContent() {
            const cidr = this.data.get('formData.clusterIPServiceCIDR') || '';
            const mask = +(cidr.split('/')[1] || '32');
            const clusterMaxServiceNum = Math.pow(2, 32 - mask);
            const span = '<span class="num-highlight">';
            return `当前ClusterIP网段配置下，集群最多允许创建${span}${clusterMaxServiceNum}${'</span>'}个service`;
        },
        isCloudEdge() {
            const clusterType = this.data.get('clusterType');
            return clusterType === ClusterType.CLOUD_EDGE;
        },
    };

    initData() {
        return {
            formData: {
                maxPodsPerNode: '256',
                skipNetworkCheck: false,
            },
            formErrors: null,
            rules,
            maxPods: 0,
            maxPodsPerNodeDatasource: _.map(MAX_PODS_PER_NODE, item => {
                return {text: item + '', value: item + ''};
            }),
            containerCidrMessage:
                '容器网络为独立的地址空间，需与节点网络、节点子网、其他集群的容器网络相互独立',
            clusterCidrMessage:
                'ClusterIP指通过集群内部IP暴露服务，服务在集群内部可被访问。ClusterIP网段即服务IP网段，需与节点网络、节点子网、容器网段相互独立',
            maxMask: $flag.CceClusterNetworkCIDR ? 24 : 29,
        };
    }

    attached() {
        store.dispatch('checkInternalUser');

        this.checkIgnoreNetworkConflict();
        // 边缘集群固定容器网段
        const isCloudEdge = this.data.get('isCloudEdge');
        isCloudEdge && this.data.set('formData.clusterPodCIDR', CloudClusterCIDR);

        this.watch('vpcId', value => {
            if (value) {
                this.onContainerCidrChange();
                this.onClusterCidrChange();
            }
        });

        this.watch('IPversion', value => {
            this.onContainerCidrChange();
            this.onClusterCidrChange();
        });
    }

    getContainerPayload(ipVersion) {
        const selectedVpc = this.data.get('selectedVpcItem');
        return {
            vpcID: selectedVpc.value,
            vpcCIDR: selectedVpc.cidr,
            maxPodsPerNode: +this.data.get('formData.maxPodsPerNode'),
            clusterMaxNodeNum: +this.data.get('maxPods'),
            privateNetCIDRs: $flag.CceClusterNetworkCIDR
                ? PRIVATE_NET_CIDRS_DATA_JILI
                : PRIVATE_NET_CIDRS_DATA,
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: ipVersion,
        };
    }

    recommendContainerCidr() {
        let promise = Promise.resolve();
        if (this.data.get('IPversion')) {
            let payload = this.getContainerPayload('ipv6');
            const selectedVpc = this.data.get('selectedVpcItem');
            /* eslint-disable */
            payload.vpcCIDRIPv6 = selectedVpc.ipv6Cidr;
            payload.privateNetCIDRIPv6s = ['fc00::/7'];
            /* eslint-enable */
            promise = this.$http.recommendContainerCidr(payload);
        }

        return promise.then(data => {
            if (
                data &&
                data.recommendedContainerCIDRIPv6s &&
                data.recommendedContainerCIDRIPv6s.length > 0
            ) {
                this.data.set('formData.clusterPodCIDRIPv6', data.recommendedContainerCIDRIPv6s[0]);
            } else {
                this.data.set('formData.clusterPodCIDRIPv6', '');
            }
        });
    }

    onContainerCidrChange(e) {
        if (e && e.value) {
            this.data.set('formData.clusterPodCIDR', e.value);
            this.data.set('formData.clusterPodCIDRIPv6', '');
            this.data.set('formData.skipNetworkCheck', false);
        }
        const containerCidr = this.ref('containerCidr');
        if (containerCidr && !containerCidr.validateCidr()) {
            return;
        }
        this.checkContainerNet()
            .then(() => this.recommendContainerCidr())
            .then(() => this.recommendClusterIpCidr());
    }

    getClusterPayload(ipVersion) {
        const selectedVpc = this.data.get('selectedVpcItem');
        const cidr = this.data.get('formData.clusterIPServiceCIDR') || '';
        const mask = +(cidr.split('/')[1] || '32');
        const clusterMaxServiceNum =
            Math.pow(2, 32 - mask) > 65536 ? 65536 : Math.pow(2, 32 - mask);
        return {
            vpcCIDR: selectedVpc.cidr,
            containerCIDR: this.data.get('formData.clusterPodCIDR'),
            clusterMaxServiceNum: clusterMaxServiceNum,
            privateNetCIDRs: $flag.CceClusterNetworkCIDR
                ? PRIVATE_NET_CIDRS_DATA_JILI
                : PRIVATE_NET_CIDRS_DATA,
            k8sVersion: this.data.get('k8sVersion'),
            IPversion: ipVersion,
        };
    }

    recommendClusterIpCidr() {
        if (this.data.get('IPversion')) {
            if (this.data.get('formData.clusterPodCIDRIPv6')) {
                let payload = this.getClusterPayload('ipv6');
                const selectedVpc = this.data.get('selectedVpcItem');

                /* eslint-disable */
                payload.vpcCIDRIPv6 = selectedVpc.ipv6Cidr;
                payload.privateNetCIDRIPv6s = ['fc00::/7'];
                payload.containerCIDRIPv6 = this.data.get('formData.clusterPodCIDRIPv6');
                /* eslint-enable */

                return this.$http.recommendClusterIpCidr(payload).then(data => {
                    if (
                        data &&
                        data.recommendedClusterIPCIDRIPv6s &&
                        data.recommendedClusterIPCIDRIPv6s.length > 0
                    ) {
                        this.data.set(
                            'formData.clusterIPServiceCIDRIPv6',
                            data.recommendedClusterIPCIDRIPv6s[0],
                        );
                    } else {
                        this.data.set('formData.clusterIPServiceCIDRIPv6', '');
                    }
                });
            }
        } else {
            this.data.set('formData.clusterIPServiceCIDRIPv6', '');
        }
    }

    onClusterCidrChange(e) {
        if (e && e.value) {
            this.data.set('formData.clusterIPServiceCIDR', e.value);
            this.data.set('formData.clusterIPServiceCIDRIPv6', '');
        }
        const clusterCidr = this.ref('clusterCidr');
        if (!clusterCidr.validateCidr()) {
            return;
        }
        this.checkContainerNet()
            .then(() => this.recommendContainerCidr())
            .then(() => this.recommendClusterIpCidr());
    }

    onMaxPodsPerNodeChange(e) {
        this.data.set('formData.maxPodsPerNode', e.value);
        this.checkContainerNet();
    }

    checkContainerNet() {
        if (!this.$context.isVerifyUser()) {
            return Promise.reject();
        }
        const selectedVpc = this.data.get('selectedVpcItem');
        const {maxPodsPerNode, clusterPodCIDR, clusterIPServiceCIDR} = this.data.get('formData');
        const isCloudEdge = this.data.get('isCloudEdge');

        if (isCloudEdge) {
            const containerNetError = {};
            if (checkCidrConflict(BecVmCIDR, clusterIPServiceCIDR)) {
                const clusterIPCIDRConflict = {
                    message: (ConflictType.fromValue('ClusterIPCIDRAndBecVmCIDR') || {}).message,
                };
                containerNetError.clusterIPCIDRConflict = clusterIPCIDRConflict;
            } else if (checkCidrConflict(BecLbCIDR, clusterIPServiceCIDR)) {
                const clusterIPCIDRConflict = {
                    message: (ConflictType.fromValue('ClusterIPCIDRAndBecLbCIDR') || {}).message,
                };
                containerNetError.clusterIPCIDRConflict = clusterIPCIDRConflict;
            } else if (checkCidrConflict(CloudClusterCIDR, clusterIPServiceCIDR)) {
                const clusterIPCIDRConflict = {
                    message: (ConflictType.fromValue('ClusterIPCIDRAndContainerCIDR') || {})
                        .message,
                };
                containerNetError.clusterIPCIDRConflict = clusterIPCIDRConflict;
            }
            this.data.set('containerNetError', containerNetError);
            return Promise.reject();
        }

        if (selectedVpc && selectedVpc.cidr && clusterPodCIDR && clusterIPServiceCIDR) {
            this.data.set('checkLoading', true);
            const skipNetworkCheck = this.data.get('formData.skipNetworkCheck');
            return this.$http
                .checkContainerNetworkCidr({
                    vpcID: selectedVpc.shortId,
                    vpcCIDR: selectedVpc.cidr,
                    containerCIDR: clusterPodCIDR,
                    clusterIPCIDR: clusterIPServiceCIDR,
                    maxPodsPerNode: +maxPodsPerNode,
                    skipContainerCIDRCheck: skipNetworkCheck,
                })
                .then(response => {
                    let {isConflict, containerCIDRConflict, clusterIPCIDRConflict, maxNodeNum} =
                        response;
                    let containerNetError = !!isConflict;
                    if (containerCIDRConflict) {
                        containerCIDRConflict = {
                            ...containerCIDRConflict,
                            message: (
                                ConflictType.fromValue(containerCIDRConflict.conflictType) || {}
                            ).message,
                        };

                        containerNetError = {
                            containerCIDRConflict,
                        };

                        this.data.set('formData.clusterPodCIDRIPv6', '');
                    }

                    if (clusterIPCIDRConflict) {
                        clusterIPCIDRConflict = {
                            ...clusterIPCIDRConflict,
                            message: (
                                ConflictType.fromValue(clusterIPCIDRConflict.conflictType) || {}
                            ).message,
                        };

                        containerNetError = {
                            ...(_.isObject(containerNetError) ? containerNetError : {}),
                            clusterIPCIDRConflict,
                        };

                        this.data.set('formData.clusterIPServiceCIDRIPv6', '');
                    }

                    this.data.set('maxPods', maxNodeNum);
                    this.data.set('containerNetError', containerNetError);

                    if (!isConflict) {
                        return Promise.resolve(maxNodeNum);
                    }

                    return Promise.reject(containerNetError);
                })
                .catch(error => {
                    this.data.set('containerNetError', error);
                    return Promise.reject(error);
                })
                .finally(() => this.data.set('checkLoading', false));
        }

        return Promise.reject();
    }

    // 只有容器网段
    onConflictDetail(type) {
        const {containerCIDRConflict, clusterIPCIDRConflict} = this.data.get('containerNetError');
        const isCluster = type === 'cluster';
        const ipVersion = 'ipv4';

        const networkConflict = new NetworkConflict({
            data: {
                containerNetError: isCluster ? clusterIPCIDRConflict : containerCIDRConflict,
                isCluster,
                payload: isCluster
                    ? this.getClusterPayload(ipVersion)
                    : this.getContainerPayload(ipVersion),
            },
        });

        networkConflict.attach(document.body);
        networkConflict.on('ok', cidr => {
            if (cidr) {
                if (isCluster) {
                    this.data.set('formData.clusterIPServiceCIDR', cidr);
                } else {
                    this.data.set('formData.clusterPodCIDR', cidr);
                }
                this.data.set('containerNetError', false);
            }
        });
    }

    validateForm() {
        const form = this.ref('form');
        return form.validateForm().then(() => {
            const containerCidr = this.ref('containerCidr');
            const isCloudEdge = this.data.get('isCloudEdge');
            if (containerCidr && !containerCidr.validateCidr()) {
                return Promise.reject();
            }

            const clusterCidr = this.ref('clusterCidr');
            if (!clusterCidr.validateCidr()) {
                return Promise.reject();
            }

            const containerNetError = this.data.get('containerNetError');
            if (!isCloudEdge && containerNetError && containerNetError.containerCIDRConflict) {
                return Promise.reject();
            }

            if (containerNetError && containerNetError.clusterIPCIDRConflict) {
                return Promise.reject();
            }

            return Promise.resolve();
        });
    }

    getFormData() {
        const formData = this.data.get('formData');

        const isCloudEdge = this.data.get('isCloudEdge');
        if (isCloudEdge) {
            formData.skipNetworkCheck = true;
        }

        return formData;
    }

    checkIgnoreNetworkConflict() {
        return this.$http
            .checkWhiteList('IgnoreNetworkConflict')
            .then(result => _.get(result, '.isExist', false))
            .catch(e => false)
            .then(res => this.data.set('ignoreNetworkConflict', res));
    }

    onIgnoreConflict() {
        this.data.set('containerNetError.containerCIDRConflict', null);
        this.data.set('formData.skipNetworkCheck', true);
        this.checkContainerNet();
    }
}

@asComponent('@cce-cluster-create-vpc')
export default class VpcStore extends connect.san(StoreMap)(Vpc) {}
