/**
 * @file components/cluster/create-v2/script.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<template>
    <ui-form s-ref="form" form-data="{=formData=}" class="cce-cluster-create-script">
        <ui-form-item inline label="部署前执行脚本：" name="preUserScript">
            <label slot="label" title="部署前执行脚本：">部署前执行脚本：</label>
            <ui-text-box
                multiline
                value="{=formData.preUserScript=}"
                width="600"
                height="60"
                limit-length="{{maxLength}}"
                placeholder="请输入需要在部署前执行的脚本命令，支持Shell格式，大小不能超过16KB"
                class="bui-textbox-multi-line"
            />
            <ui-tip layer-width="300" message="{{preMessage}}" />
        </ui-form-item>

        <ui-form-item inline label="部署后执行脚本：" name="postUserScript">
            <label slot="label" title="部署后执行脚本：">部署后执行脚本：</label>
            <ui-text-box
                multiline
                value="{=formData.postUserScript=}"
                width="600"
                height="60"
                limit-length="{{maxLength}}"
                placeholder="请输入需要在部署后执行的脚本命令，支持Shell格式，大小不能超过16KB"
                class="bui-textbox-multi-line"
            />
            <ui-tip layer-width="300" message="{{postMessage}}" />
        </ui-form-item>

        <ui-form-item s-if="!hideAutoCordon" inline label=" " name="postUserScriptFailedAutoCordon">
            <ui-check-box
                checked="{=formData.postUserScriptFailedAutoCordon=}"
                title="部署后执行脚本失败自动封锁节点"
            />
        </ui-form-item>
    </ui-form>
</template>`;

@asComponent('@cce-cluster-create-script')
@invokeBceSanUI
export default class Script extends Component {
    static template = template;

    initData() {
        return {
            formData: {
                preUserScript: '',
                postUserScript: '',
                postUserScriptFailedAutoCordon: false,
            },
            preMessage:
                '节点部署前将自动执行该脚本，您需要自行保证脚本的可重入及重试逻辑。' +
                '脚本内容和产生日志将写入节点的/usr/local/cce/scripts/目录下。',
            postMessage:
                '节点部署后将自动执行该脚本，您需要自行确定脚本的执行情况。' +
                '脚本内容和产生日志将写入节点的/usr/local/cce/scripts/目录下。',
            maxLength: 16384, // 16KB
        };
    }

    getFormData() {
        const formData = this.data.get('formData');

        return _.extend(formData);
    }
}
