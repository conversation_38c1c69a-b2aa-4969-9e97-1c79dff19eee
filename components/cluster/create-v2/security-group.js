/**
 * @file components/cluster/create-v2/security-group.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {Select} from '@baidu/sui';
import {CheckBox, RadioSelect, Button, Icon} from '@baiducloud/bce-ui/san';
import {connect} from 'san-store';
import StoreMap from '../../../pages/cluster/create-v2/store/map';
import {getSdk} from '../../../utils/sdk';
import {PermissionType} from '../../../utils/enums';
import {checkWhiteByName} from '../../../common/white';

const {asComponent} = decorators;
const $flag = ServiceFactory.resolve('$flag');
const quotaMaxNumber = 10;

const template = html`<template>
    <ui-radio-select
        s-if="showSecurityGroupType"
        box-type="checkbox"
        datasource="{{securityGroupTypes}}"
        value="{=securityGroupType=}"
        class="mb20"
    />
    <div
        s-for="item, index in securityGroupIdList"
        class="security-select-container mb10"
        s-if="securityGroupType === 'normal'"
    >
        <s-select datasource="{{select.datasource}}" value="{=item.value=}" width="{{203}}">
        </s-select>
        <ui-icon name="close" class="ml10 mr10" on-click="deleteSecurityItem(index)" />
        <span s-if="!item.value" class="select-warning">请选择安全组</span>
    </div>
    <div
        class="security-control {{securityGroupIdList.length > 0 ? '' : 'mt4'}}"
        s-if="securityGroupType === 'normal'"
    >
        <ui-button
            label="添加安全组"
            skin="stringfy"
            icon="plus"
            disabled="{{addSecurityGroupDisabled}}"
            on-click="addSecurityGroup"
        />
        <br s-if="!hideDefaultOption" />
        <div s-if="!hideDefaultOption">
            <ui-checkbox
                title="添加CCE {{type}}默认安全组"
                checked="{=checkbox.defaultChecked=}"
                disabled="{{defaultDisabled || checkboxDisabled}}"
            />
            <span class="tips-warning" s-if="!checkbox.defaultChecked">
                注意：建议添加CCE默认安全组规则，否则会造成节点访问异常
            </span>
        </div>
        <div s-if="!hideDefaultOption">
            <ui-checkbox
                title="添加CCE {{type}}附加安全组"
                checked="{=checkbox.attachChecked=}"
                disabled="{{attachDisabled || checkboxDisabled}}"
            />
            <span class="tips-warning" s-if="!checkbox.attachChecked && isMaster">
                注意：若选择开启集群公网访问，建议添加CCE Master附加安全组
            </span>
        </div>
    </div>
    <div class="security-select-container" s-if="securityGroupType === 'enterprise'">
        <s-select
            datasource="{{enterpriseSecurityGroupList}}"
            value="{=enterpriseSecurityGroupId=}"
            width="{{237}}"
            default-label="请选择"
        />
        <span s-if="!enterpriseSecurityGroupId" class="select-warning">请选择企业安全组</span>
    </div>
</template>`;

class SecurityGroup extends Component {
    static template = template;

    static computed = {
        addSecurityGroupDisabled() {
            // 一个实例最多添加10个安全组，包括默认和附加
            const securityGroupIdList = this.data.get('securityGroupIdList');
            const dataSource = this.data.get('select.datasource');
            const defaultSecurity = this.data.get('checkbox.defaultChecked') ? 1 : 0;
            const attachSecurity = this.data.get('checkbox.attachChecked') ? 1 : 0;
            const maxNumber = quotaMaxNumber - defaultSecurity - attachSecurity;
            return (
                securityGroupIdList.length >= dataSource.length ||
                securityGroupIdList.length >= maxNumber
            );
        },
        checkboxDisabled() {
            const defaultSecurity = this.data.get('checkbox.defaultChecked') ? 1 : 0;
            const attachSecurity = this.data.get('checkbox.attachChecked') ? 1 : 0;
            const securityGroupIdList = this.data.get('securityGroupIdList');
            const maxNumber = quotaMaxNumber - defaultSecurity - attachSecurity;
            return (
                securityGroupIdList.length >= maxNumber &&
                securityGroupIdList.length > quotaMaxNumber - 2
            );
        },
        isMaster() {
            const type = this.data.get('type');
            return type === 'Master';
        },
        masterPublic() {
            const exposedPublic = this.data.get('exposedPublic');
            const isMaster = this.data.get('isMaster');
            return exposedPublic && isMaster;
        },
        showSecurityGroupType() {
            const isNewBec = this.data.get('isNewBec');
            const enableEnterpriseSecurityGroup = this.data.get('enableEnterpriseSecurityGroup');
            const enterpriseSecurityGroupWhite = this.data.get('enterpriseSecurityGroupWhite');
            return enableEnterpriseSecurityGroup && enterpriseSecurityGroupWhite && !isNewBec;
        },
    };

    initData() {
        return {
            securityGroupIdList: [],
            select: {
                datasource: [],
            },
            sdk: getSdk('BCC'),
            checkbox: {
                defaultChecked: true,
                attachChecked: false,
            },
            defaultDisabled: true,
            attachDisabled: false,
            exposedPublic: false,
            securityGroupTypes: [
                {text: '普通安全组', value: 'normal'},
                {text: '企业安全组', value: 'enterprise'},
            ],
            securityGroupType: 'normal',
            enterpriseSecurityGroupList: [],
            enterpriseSecurityGroupId: null,
            enterpriseSecurityGroupWhite: true,
            enableEnterpriseSecurityGroup: true,
        };
    }

    components = {
        'ui-checkbox': CheckBox,
        'ui-button': Button,
        'ui-icon': Icon,
        'ui-radio-select': RadioSelect,
        's-select': Select,
    };

    inited() {
        const isNewBec = this.data.get('isNewBec');

        if (!isNewBec) {
            this.getSecurityList();
            this.getEnterpriseSecurityGroupList();
        } else {
            this.getBecSecurityList();
        }

        this.watch('vpcId', () => {
            if (!isNewBec) {
                this.getSecurityList();
            }
        });
    }

    attached() {
        const func = value => {
            this.data.set('checkbox.attachChecked', value);
            this.data.set('attachDisabled', value);
        };
        const masterPublic = this.data.get('masterPublic');
        masterPublic && func(masterPublic);
        this.watch('masterPublic', func);

        this.watch('securityGroupIdList', list => {
            // 删除所有安全组时，添加默认安全组默认勾选且置灰
            // 添加了安全组时，置灰取消
            const hasSecurity = _.filter(list, i => i.value);
            const masterPublic = this.data.get('masterPublic');
            if (list.length === 0 || !hasSecurity.length) {
                this.data.set('defaultDisabled', true);
                this.data.set('checkbox.defaultChecked', true);
                masterPublic && this.data.set('checkbox.attachChecked', true);
                masterPublic && this.data.set('attachDisabled', true);
            } else {
                this.data.set('defaultDisabled', false);
                this.data.set('attachDisabled', false);
            }
            // 被选择的安全组，下拉列表选项置灰
            const selectList = this.data.get('select.datasource');
            _.each(selectList, item => {
                if (_.find(list, i => i.value === item.value)) {
                    item.disabled = true;
                } else {
                    item.disabled = false;
                }
            });
        });

        // 云边集群不支持企业安全组
        const handleClusterType = () => {
            if (this.data.get('clusterType') === 'cloudEdge') {
                this.data.set('enableEnterpriseSecurityGroup', false);
            }
        };
        handleClusterType();
        this.watch('clusterType', () => handleClusterType());
    }

    getFormData() {
        const result = {};
        const {checkbox, securityGroupIdList, securityGroupType, enterpriseSecurityGroupId} =
            this.data.get();
        result.securityGroupType = securityGroupType;
        if (securityGroupType === 'normal') {
            const customSecurityGroups = _.map(securityGroupIdList, item => item.value);
            result.customSecurityGroups = customSecurityGroups;
            const hideDefaultOption = this.data.get('hideDefaultOption');
            result.enableCCERequiredSecurityGroup = hideDefaultOption
                ? false
                : checkbox.defaultChecked;
            result.enableCCEOptionalSecurityGroup = hideDefaultOption
                ? false
                : checkbox.attachChecked;
        } else {
            result.customSecurityGroups = enterpriseSecurityGroupId
                ? [enterpriseSecurityGroupId]
                : [];
        }
        return result;
    }

    // 获取安全组下拉列表选项
    getSecurityList() {
        const vpcId = this.data.get('vpcId');
        if (!vpcId) {
            return;
        }
        const next = data => {
            const list = data.page?.result || data.result;
            const result = _.map(list || [], item => {
                return {
                    text: item.name,
                    label: item.name,
                    value: item.securityGroupId,
                };
            });
            this.data.set('select.datasource', result);
            const securityGroupIdList = this.data.get('securityGroupIdList');
            if (securityGroupIdList?.length) {
                this.nextTick(() => {
                    this.data.set('securityGroupIdList', _.cloneDeep(securityGroupIdList));
                });
            }
        };
        if ($flag.CceClusterCreateSecurityVpcId) {
            return this.$http
                .getInstanceList({
                    vpcId: this.data.get('vpcOriginId'),
                    pageNo: 1,
                    pageSize: 10000,
                })
                .then(next);
        }
        return this.data.get('sdk').getSecurityList({vpcId}).then(next);
    }

    addSecurityGroup() {
        this.data.push('securityGroupIdList', {});
    }

    deleteSecurityItem(index) {
        this.data.splice('securityGroupIdList', [index, 1]);
    }

    async getBecSecurityList() {
        const data = await this.$http.getBecSecurityList();

        const list = data?.result?.result || [];
        const result = list.map(item => {
            return {
                text: item.name,
                label: item.name,
                title: item.name,
                value: item.id,
            };
        });
        this.data.set('select.datasource', result);
    }

    validateForm() {
        const securityGroupType = this.data.get('securityGroupType');
        if (securityGroupType === 'normal') {
            const securityGroupIdList = this.data.get('securityGroupIdList');
            const hasSecurity = _.filter(securityGroupIdList, i => i.value);
            if (securityGroupIdList.length !== hasSecurity.length) {
                return Promise.reject();
            }
            return Promise.resolve();
        } else {
            if (this.data.get('enterpriseSecurityGroupId')) {
                return Promise.resolve();
            } else {
                return Promise.reject();
            }
        }
    }

    async getEnterpriseSecurityGroupList() {
        const {enterpriseSecurityGroupWhite, enterpriseSecurityGroupId} = this.data.get();
        if (enterpriseSecurityGroupWhite) {
            const data = await this.$http.getEnterpriseSecurityGroupList();
            const list =
                data?.page?.result?.map(e => ({
                    text: e.name,
                    value: e.esgId,
                })) || [];
            this.data.set('enterpriseSecurityGroupList', list);
            if (!enterpriseSecurityGroupId && list.length) {
                this.data.set('enterpriseSecurityGroupId', list[0].value);
            }
        }
    }
}

@asComponent('@cee-cluster-security-group')
export default class SecurityGroupStore extends connect.san(StoreMap)(SecurityGroup) {}
