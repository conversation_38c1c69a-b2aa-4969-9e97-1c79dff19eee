import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Loading} from '@baidu/sui';
import {store} from 'san-store';

const {asComponent} = decorators;

const template = html`<div class="cce-cluster-v2-shopping-cart-bec">
    <div class="price-item">
        <span class="label">负载均衡：</span>
        <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
        <span s-else class="price">¥{{blbPrice || '-'}}/实例/分钟</span>
    </div>
    <template s-if="exposedPublic">
        <div class="price-item">
            <span class="label">公网IP：</span>
            <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
            <span s-else class="price">¥{{eipPrice || '-'}}/分</span>
        </div>
        <div class="price-item">
            <span class="label">带宽：</span>
            <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
            <span s-else class="price">¥{{bandwidthPrice || '-'}}/Mbps/日</span>
        </div>
    </template>
</div>`;

@asComponent('@cce-cluster-v2-shopping-cart-bec')
export default class ShoppingCartBec extends Component {
    static template = template;

    static components = {
        's-loading': Loading,
    };

    attached() {
        this.getBecPrice();
        this.watch('exposedPublic', () => {
            this.getBecPrice();
        });
    }

    async getBecPrice() {
        const params = {
            configs: [
                {
                    serviceType: 'BEC',
                    count: 1,
                    duration: 1,
                    timeUnit: 'MINUTE',
                    productType: 'postpay',
                    region: 'global',
                    chargeItem: 'RunningTimeMinutes',
                    flavor: [
                        {name: 'loadbalance', value: '1个'},
                        {name: 'subServiceType', value: 'bec_cpt1_default', scale: 1},
                    ],
                    amount: 1,
                    scene: 'NEW',
                },
            ],
        };
        const exposedPublic = this.data.get('exposedPublic');
        if (exposedPublic) {
            params.configs.push(
                {
                    serviceType: 'BEC',
                    count: 1,
                    duration: 1,
                    timeUnit: 'MINUTE',
                    productType: 'postpay',
                    region: 'global',
                    chargeItem: 'RunningTimeMinutes',
                    flavor: [
                        {name: 'ip', value: '1个'},
                        {name: 'subServiceType', value: 'bec_cpt1_default', scale: 1},
                    ],
                    amount: 1,
                    scene: 'NEW',
                },
                {
                    serviceType: 'BEC',
                    count: 1,
                    duration: 1,
                    timeUnit: 'MINUTE',
                    productType: 'postpay',
                    region: 'global',
                    chargeItem: 'RunningTimeDayOfBandwidth',
                    flavor: [],
                    amount: 1000000,
                    scene: 'NEW',
                },
            );
        }
        this.data.set('priceLoading', true);
        const data = await this.$http.v3Price(params);
        this.data.set('priceLoading', false);
        const priceData = data?.result || [];
        const blbPrice = priceData[0]?.price;
        const eipPrice = priceData[1]?.price;
        const bandwidthPrice = priceData[2]?.price;
        this.data.set('blbPrice', blbPrice);
        this.data.set('eipPrice', eipPrice);
        this.data.set('bandwidthPrice', bandwidthPrice);
        store.dispatch('setBecPrice', {
            blbPrice,
            eipPrice,
            bandwidthPrice,
        });
    }
}
