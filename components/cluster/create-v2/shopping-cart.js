/**
 * 创建页价格展示
 *
 * @file components/cluster/create-v2/shopping-cart.js
 * <AUTHOR>
 */

import _, {isString} from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {math, moneyUtil} from '@baiducloud/billing-sdk';
import {Loading} from '@baidu/sui';

const {asComponent} = decorators;
const moneyUtilDefault = moneyUtil.default;

/* eslint-disable */
const template = html`<div
    class="cce-cluster-v2-shopping-cart"
    s-if="!priceLoading || showBlb || showBcc"
>
    <div s-if="showBlb || showBcc || showGroup" class="prices-wrapper">
        <div class="price-item-list">
            <div class="price-item" s-if="showBlb">
                <div class="price-item-title">BLB实例配置费用：</div>
                <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
                <div class="price-item-content-wrap" s-else>
                    <span class="price-item-content">
                        {{priceDetail.instance.blb.basePrice.moneyShow | raw | empty}}
                    </span>
                    <span class="price-item-extra">
                        {{priceDetail.instance.blb.basePrice.extra || '' | raw | empty}}
                    </span>
                </div>
            </div>
            <div class="price-item" s-if="showBcc">
                <div class="price-item-title">
                    {{isExpand ? 'Worker' : 'Master'}}节点实例配置费用：
                </div>
                <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
                <div class="price-item-content-wrap" s-else>
                    <span
                        class="price-item-content"
                        s-if="priceDetail.instance.bcc.basePrice.moneyShow"
                    >
                        {{priceDetail.instance.bcc.basePrice.moneyShow | raw | empty}}
                    </span>
                    <label class="price-item-extra" s-if="priceDetail.instance.bcc.basePrice.extra">
                        {{priceDetail.instance.bcc.basePrice.extra || '' | raw | empty}}
                    </label>
                    <span
                        class="price-item-content"
                        s-if="priceDetail.instance.bcc.bbcPrice.moneyShow"
                    >
                        {{priceDetail.instance.bcc.bbcPrice.moneyShow | raw | empty}}
                    </span>
                </div>
            </div>
            <div class="price-item" s-if="showGroup">
                <div class="price-item-title">节点实例配置费用：</div>
                <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
                <div class="price-item-content-wrap" s-else>
                    <span class="price-item-content" s-if="priceDetail.instance.group.moneyShow">
                        {{priceDetail.instance.group.moneyShow | raw | empty}}
                    </span>
                    <label class="price-item-extra" s-if="priceDetail.instance.group.extra">
                        {{priceDetail.instance.group.extra || '' | raw | empty}}
                    </label>
                    <span class="price-item-content" s-if="priceDetail.instance.group.bbcMoneyShow">
                        {{priceDetail.instance.group.bbcMoneyShow | raw | empty}}
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="prices-wrapper" s-if="showEipBandWidthPrice || showEipTrafficPrice">
        <div class="price-item-list">
            <div class="price-item" s-if="showEipBandWidthPrice">
                <div class="price-item-title">带宽网络费用：</div>
                <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
                <div class="price-item-content-wrap" s-else>
                    <span class="price-item-content">
                        {{priceDetail.network.eip.bandwidthPrice.moneyShow | raw | empty}}
                    </span>
                    <label class="price-item-extra">
                        {{priceDetail.network.eip.bandwidthPrice.extra | raw | empty}}
                    </label>
                </div>
            </div>
            <div class="price-item" s-if="showEipTrafficPrice">
                <div class="price-item-title">流量网络费用：</div>
                <s-loading s-if="priceLoading" size="small" loading="{{true}}" />
                <span s-else class="price-item-content">
                    {{priceDetail.network.eip.trafficPrice.moneyShow | raw | empty}}
                </span>
            </div>
        </div>
    </div>
</div>`;
/* eslint-enable */

@asComponent('@cce-cluster-v2-shopping-cart')
export default class ShoppingCart extends Component {
    static template = template;

    static components = {
        's-loading': Loading,
    };

    static computed = {
        showBlb() {
            const {
                instance: {blb},
            } = this.data.get('priceDetail');
            return blb?.basePrice?.money;
        },
        showBcc() {
            const {
                instance: {bcc},
            } = this.data.get('priceDetail');
            return (
                (bcc.basePrice && bcc.basePrice.moneyShow) ||
                (bcc.bbcPrice && bcc.bbcPrice.moneyShow)
            );
        },
        showGroup() {
            const {
                instance: {group},
            } = this.data.get('priceDetail');
            return (group.basePrice && group.moneyShow) || (group.bbcPrice && group.bbcMoneyShow);
        },
        showEipBandWidthPrice() {
            return (
                !!this.data.get('queryBccPrice.eipBandwidthPrice.money') ||
                !!this.data.get('queryBlbPrice.eipBandwidthPrice.money') ||
                !!this.data.get('queryGroupPrice.eipBandwidthPrice')
            );
        },
        showEipTrafficPrice() {
            return (
                !!this.data.get('queryBccPrice.eipTrafficPrice.money') ||
                !!this.data.get('queryGroupPrice.eipTrafficPrice')
            );
        },
    };

    initData() {
        return {
            priceLoading: true,
            priceDetail: {
                instance: {
                    bcc: {},
                    blb: {},
                    group: {},
                },
                network: {
                    eip: {
                        bandwidthPrice: {},
                        trafficPrice: {},
                    },
                },
            },
        };
    }

    attached() {
        this.watch('queryBccPrice', () => this.updateShoppingCart());
        this.watch('queryBlbPrice', () => this.updateShoppingCart());
        this.watch('queryGroupPrice', () => this.updateShoppingCart());
    }

    getPriceExtra(money, unit = '') {
        if (this.data.get('payType') === 'prepay') {
            return '';
        }
        if (money <= 0) {
            return '';
        }
        let moneyDay;
        let moneyMonth;
        if (isString(money) && money.includes('~')) {
            const min = money.split('~')[0];
            const max = money.split('~')[1];
            let minMoneyDay = moneyUtilDefault.showMoney(min * 1440);
            let maxMoneyDay = moneyUtilDefault.showMoney(max * 1440);
            let minMoneyMonth = moneyUtilDefault.showMoney(min * 30 * 1440);
            let maxMoneyMonth = moneyUtilDefault.showMoney(max * 30 * 1440);
            moneyDay = `${minMoneyDay} ~ ${maxMoneyDay}`;
            moneyMonth = `${minMoneyMonth} ~ ${maxMoneyMonth}`;
        } else {
            moneyDay = moneyUtilDefault.showMoney(money * 1440);
            moneyMonth = moneyUtilDefault.showMoney(money * 30 * 1440);
        }
        return `预计<span class="extra-price">￥${moneyDay}/天${unit}，￥${moneyMonth}/月${unit}</span>`;
    }

    getPriceText(money, unit = '/分钟') {
        if (isString(money) && money.includes('~')) {
            const min = money.split('~')[0];
            const max = money.split('~')[1];
            return `${min} ~ ${max}${unit}`;
        }
        return (
            (money <= 0 ? moneyUtilDefault.showMoney : moneyUtilDefault.formatNoPrecision)(
                money + '' || '0',
            ) + unit
        );
    }

    updateShoppingCart() {
        const bccPrice = this.data.get('queryBccPrice');
        const blbPrice = this.data.get('queryBlbPrice');
        const groupPrice = this.data.get('queryGroupPrice');
        const payConfig = this.data.get('payConfig');
        const payType = payConfig?.payType || 'postpay';
        this.data.set('payType', payType);
        if (bccPrice) {
            this.data.set('priceDetail.instance.bcc.basePrice', bccPrice.basePrice);
            if (bccPrice.basePrice) {
                const bccMoney = bccPrice.basePrice.money || 0;
                const unit = this.data.get('payType') === 'prepay' ? '' : '/分钟/个';
                this.data.set(
                    'priceDetail.instance.bcc.basePrice.moneyShow',
                    this.getPriceText(bccMoney, unit),
                );
                this.data.set(
                    'priceDetail.instance.bcc.basePrice.extra',
                    this.getPriceExtra(bccMoney),
                );
            } else {
                this.data.set('priceDetail.instance.bcc.basePrice.moneyShow', '');
                this.data.set('priceDetail.instance.bcc.basePrice.extra', '');
            }
            if (bccPrice.bbcPrice) {
                const unit = this.data.get('payType') === 'prepay' ? '' : '/月';
                this.data.set(
                    'priceDetail.instance.bcc.bbcPrice.moneyShow',
                    this.getPriceText(bccPrice.bbcPrice.money, unit),
                );
            } else {
                this.data.set('priceDetail.instance.bcc.bbcPrice.moneyShow', '');
            }
        } else {
            this.data.set('priceDetail.instance.bcc', {});
            this.data.set('priceDetail.network.eip.bandwidthPrice', {});
            this.data.set('priceDetail.network.eip.trafficPrice', {});
        }
        if (groupPrice) {
            this.data.set('priceDetail.instance.group.basePrice', groupPrice.bccPrice);
            if (groupPrice.bccPrice) {
                const bccMoney = groupPrice.bccPrice || 0;
                const unit = this.data.get('payType') === 'prepay' ? '' : '/分钟';
                this.data.set(
                    'priceDetail.instance.group.moneyShow',
                    this.getPriceText(bccMoney, unit),
                );
                this.data.set('priceDetail.instance.group.extra', this.getPriceExtra(bccMoney));
            } else {
                this.data.set('priceDetail.instance.group.moneyShow', '');
                this.data.set('priceDetail.instance.group.extra', '');
            }
            if (groupPrice.bbcPrice) {
                this.data.set('priceDetail.instance.group.bbcPrice', groupPrice.bbcPrice);
                const unit = this.data.get('payType') === 'prepay' ? '' : '/月';
                this.data.set(
                    'priceDetail.instance.group.bbcMoneyShow',
                    this.getPriceText(groupPrice.bbcPrice, unit),
                );
            } else {
                this.data.set('priceDetail.instance.group.bbcMoneyShow', '');
            }
        } else {
            this.data.set('priceDetail.instance.group', {});
            this.data.set('priceDetail.network.eip.bandwidthPrice', {});
            this.data.set('priceDetail.network.eip.trafficPrice', {});
        }
        if (blbPrice) {
            this.data.set('priceDetail.instance.blb.basePrice', blbPrice?.basePrice);
            const eipMoney = bccPrice?.eipBandwidthPrice?.money;
            const unit = this.data.get('payType') === 'prepay' ? '' : '/分钟';
            const bandwitdthMoneyWithBlb =
                math.safeAdd(eipMoney || 0, blbPrice.eipBandwidthPrice.money) || 0;

            this.data.set(
                'priceDetail.network.eip.bandwidthPrice.moneyShow',
                this.getPriceText(bandwitdthMoneyWithBlb, unit),
            );
            this.data.set(
                'priceDetail.network.eip.bandwidthPrice.extra',
                this.getPriceExtra(bandwitdthMoneyWithBlb),
            );
            const blbMoney = blbPrice.basePrice.money + '';
            this.data.set(
                'priceDetail.instance.blb.basePrice.moneyShow',
                this.getPriceText(blbMoney, unit),
            );
            this.data.set('priceDetail.instance.blb.basePrice.extra', this.getPriceExtra(blbMoney));
        } else {
            this.data.set('priceDetail.instance.blb', {});
        }
        //带宽
        let bandwidthMoneyWithBlb =
            (bccPrice?.eipBandwidthPrice?.money || 0) + (blbPrice?.eipBandwidthPrice?.money || 0);
        let groupEipMoney = groupPrice?.eipBandwidthPrice;
        if (groupEipMoney && isString(groupEipMoney) && groupEipMoney.includes('~')) {
            const min = groupEipMoney.split('~')[0];
            const max = groupEipMoney.split('~')[1];
            bandwidthMoneyWithBlb = `${bandwidthMoneyWithBlb + Number(min)}~${
                bandwidthMoneyWithBlb + Number(max)
            }`;
        } else {
            bandwidthMoneyWithBlb = bandwidthMoneyWithBlb + (groupPrice?.eipBandwidthPrice || 0);
        }

        if (!_.isUndefined(bandwidthMoneyWithBlb)) {
            const unit = this.data.get('payType') === 'prepay' ? '' : '/分钟';
            this.data.set(
                'priceDetail.network.eip.bandwidthPrice.moneyShow',
                this.getPriceText(bandwidthMoneyWithBlb, unit),
            );
            this.data.set(
                'priceDetail.network.eip.bandwidthPrice.extra',
                this.getPriceExtra(bandwidthMoneyWithBlb),
            );
        } else {
            this.data.set('priceDetail.network.eip.bandwidthPrice.moneyShow', '');
            this.data.set('priceDetail.network.eip.bandwidthPrice.extra', '');
        }
        // 流量
        const trafficMoney = bccPrice?.eipTrafficPrice?.money || groupPrice?.eipTrafficPrice;
        if (!_.isUndefined(trafficMoney)) {
            this.data.set(
                'priceDetail.network.eip.trafficPrice.moneyShow',
                this.getPriceText(trafficMoney, '/GB'),
            );
        } else {
            this.data.set('priceDetail.network.eip.trafficPrice.moneyShow', '');
        }
    }
}
