@import './instance/style.less';
@import './network/style.less';
@import './master/style.less';
@import './expand/style.less';
@import './worker/style.less';

.cluster-create-text-line {
    line-height: 30px;
}

.cluster-node-rebuild-tip {
    color: #f38900;
    line-height: 20px;
}

.cluster-node-image-tip {
    color: #f38900;
    margin-top: 10px;
    width: 800px;
}

.cluster-create-node-invalid-label {
    color: #f33e3e;
    margin-top: 4px;
}

.bui-form-item-rebuild {
    line-height: 30px;
}

.cluster-create-form-item-empty {
    line-height: 30px;
}

.cluster-create-cidr-confirm {
    margin-left: 20px;
}

.bui-form-item-vpcId,
.bui-form-item-k8sVersion {
    .cce-error {
        line-height: 30px;
    }
}

.bui-form-item-IPversion {
    a {
        margin-left: 10px;
    }
}

.bui-textbox {
    input,
    textarea {
        font-family: inherit;
        font-size: 100%;
    }
}

.worker-advanced-toggle {
    margin: 0;
}

.cce-cluster-v2-create-json-dialog {
    position: relative;

    .create-json-tip {
        color: #999;
        margin-bottom: 10px;

        a {
            margin-left: 15px;
        }
    }

    .clip-board-main {
        position: absolute;
        top: 50px;
        z-index: 1;
        right: 20px;
    }

    .cce-cluster-json-cni-tip {
        margin-top: 10px;
        color: #f38900;
    }
}

.cce-cluster-v2-shopping-cart {
    display: inline-flex;
    align-items: flex-start;
    margin-left: 40px;

    .prices-wrapper {
        padding: 10px 0;
        position: relative;
        margin-right: 40px;
        display: flex;
        line-height: 20px;

        .price-title {
            color: #5c5f66;
        }

        .price-item-list {
            display: flex;
        }

        .price-item {
            color: #5c5f66;

            & + .price-item {
                margin-left: 40px;
            }

            .price-item-content-wrap {
                display: flex;
                flex-direction: column;
            }
        }

        .price-item-title {
            vertical-align: top;
        }

        .price-item-content {
            color: #f33d3d;
            font-weight: 500;
            font-size: 16px;
            line-height: 28px;
            white-space: nowrap;
        }

        .price-item-extra {
            display: block;
        }

        .extra-price {
            color: #f33d3d;
        }
    }
}
.cce-cluster-v2-shopping-cart-bec {
    margin-left: 40px;
    display: flex;
    .price-item {
        display: flex;
        flex-direction: column;
        margin-right: 40px;
        .label {
            line-height: 18px;
            font-size: 12px;
            color: #999;
        }
        .price {
            color: #f33d3d;
            font-size: 20px;
            font-weight: 500;
            line-height: 28px;
        }
    }
}

.network-advanced-toggle {
    .network-advanced-title {
        display: inline-block;
        zoom: 1;
        color: #2468f2;
        cursor: pointer;
        position: relative;

        .bui-icon {
            position: absolute;
            transform: rotate(180deg);
        }

        span {
            margin-left: 20px;
        }
    }
}

.network-advanced-toggle.network-advanced-toggle-close {
    margin: 0;

    .bui-icon {
        transform: rotate(0deg);
    }
}

.network-advanced-content-close {
    display: none;
}

.enable-service-controller-link {
    margin-left: 8px;
}

.security-control {
    .bui-button {
        padding-left: 0;
        margin-bottom: 5px;
    }

    .bui-checkbox {
        margin-top: 20px;
        vertical-align: bottom;
    }

    .tips-warning {
        color: #f38900;
        margin-left: 40px;
    }
}

.mt4 {
    margin-top: 4px;
}

.security-select-container {
    .icon-close {
        position: relative;
        top: 2px;
    }

    .select-warning {
        color: #ea2e2e;
        margin-top: -5px;
    }
}

.s-resource-reserved-form {
    margin-top: 20px;
    margin-bottom: 20px;

    .s-form-item-extra {
        margin-top: 5px;
        color: #999;
    }

    .s-link {
        line-height: 30px;
    }

    .kubelet-param-form-wrap {
        .s-form-item {
            display: inline-block;
            margin-bottom: 0 !important;
        }

        .memoryHelp {
            width: 180px;
            white-space: nowrap;
        }

        .help-red {
            color: #f33e3e;
        }
    }
}

.bui-form-item-exposedPublic > .bui-form-item-content {
    padding-top: 4px;
}

.bui-form-item-podSubnets,
.bui-form-item-subnets {
    .subnet-table-container {
        width: 986px;
    }
}

.cce-cluster-create-script {
    .bui-textbox-limit {
        display: none;
    }
}

.cce-cluster-create-v2,
.cce-cluster-group-create {
    .bui-form .bui-form-item.bui-form-item-postUserScriptFailedAutoCordon {
        margin-top: 5px;
        margin-bottom: 0;
        position: relative;
        top: -14px;
        height: 10px;
    }
}
