/**
 * @file components/cluster/create-v2/subnet.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';

const {asComponent, invokeBceSanUI} = decorators;

const template = html`<div>
    <ui-radio-select value="{=type.value=}" datasource="{{type.datasource}}"> </ui-radio-select>
    <ui-tip layer-width="200" message="{{message}}" />
    <ui-form-item name="{{name}}">
        <div s-if="type.value === 'DEFAULT'">
            推荐子网：{{selectedItem ? selectedItem.text : '-'}}
        </div>
        <div s-else>
            <div s-if="loading.status === 'loading'" class="loading">
                <ui-loading size="small" />
            </div>
            <div s-if="loading.status === 'error'" class="cce-error">数据异常，请刷新重试</div>
            <div s-if="loading.status === 'done'">
                <ui-select
                    s-if="loading.list.length"
                    width="240"
                    value="{=id=}"
                    datasource="{{datasource}}"
                />
                <div s-else class="cluster-create-form-item-empty">暂无数据</div>
            </div>
        </div>
    </ui-form-item>
</div>`;

@asComponent('@cce-cluster-create-subnet')
@invokeBceSanUI
export default class Subnet extends Component {
    static template = template;

    static computed = {
        value() {
            const type = this.data.get('type.value');
            if (type === 'DEFAULT') {
                const selectedItem = this.data.get('selectedItem');
                return selectedItem ? selectedItem.value : '';
            }

            return this.data.get('id');
        },
    };

    initData() {
        return {
            type: {
                value: 'DEFAULT',
                datasource: [
                    {
                        text: '使用推荐子网',
                        value: 'DEFAULT',
                    },
                    {
                        text: '使用自定义子网',
                        value: 'CUSTOM',
                    },
                ],
            },
            datasource: [],
            selectedItem: null,
        };
    }
}
