/**
 * @file tag-config.js
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Button, Form, Link} from '@baidu/sui';
import {Suggestion} from '@baiducloud/bce-ui/san';
import {OutlinedLink, OutlinedPlus} from '@baidu/sui-icon';
import './tag-config.less';

const {asComponent, invokeBceSanUI, invokeAppComp} = decorators;

const template = html`
    <template>
        <s-form class="tag-config-form" label-align="left">
            <s-form-item label="{{isEdit ? '' : label}}">
                <div class="bg-wrap {{isEdit ? '' : 'expand'}}">
                    <div id="tagList" class="tag-item" s-for="index in tagIndexList">
                        <!--<label class="inline-label">标签键：</label>-->
                        <ui-suggestion
                            placeholder="请选择或手动输入标签键"
                            value="{=tagObj[index].tagKey=}"
                            hasArrow="{{true}}"
                            datasource="{{tagObj[index].tagKeyList}}"
                            on-input="onTagKeyInput($event, index)"
                            on-change="onTagKeyChange($event, index)"
                            width="220"
                        />
                        <!--<label class="inline-label">值：</label>-->
                        <ui-suggestion
                            placeholder="请选择或手动输入标签值"
                            value="{=tagObj[index].tagValue=}"
                            hasArrow="{{true}}"
                            datasource="{{tagObj[index].tagValueList}}"
                            on-input="onTagValueInput($event, index)"
                            on-change="onTagValueChange($event, index)"
                            width="220"
                        />
                        <a
                            s-if="tagIndexList.length > 1 || isEdit"
                            class="close"
                            on-click="onRemoveTag(index)"
                            >删除</a
                        >
                    </div>
                    <p s-if="{{tagKeyTip}}" class="com-color-red">{{tagKeyTip}}</p>
                    <p s-if="{{tagValueTip}}" class="com-color-red">{{tagValueTip}}</p>
                    <span s-if="{{tagTip}}" class="com-color-red">{{tagTip}}</span>
                    <div class="tag-config-add">
                        <s-button skin="stringfy" on-click="onAddTag">
                            <s-icon-plus class="tag-add-icon" />
                            添加标签
                        </s-button>
                    </div>
                    <!--bca-disable-next-line-->
                    <p class="cce-tip-grey">{{isEdit ? '' : help | raw}}</p>
                </div>
            </s-form-item>
        </s-form>
    </template>
`;

@asComponent('@tag-config')
@invokeBceSanUI
@invokeAppComp
export default class TagConfig extends Component {
    static template = template;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-link': Link,
        's-icon-link': OutlinedLink,
        's-icon-plus': OutlinedPlus,
        'ui-suggestion': Suggestion,
    };

    initData() {
        return {
            label: '资源标签：',
            help: '标签包含键和值两部分，标签支持按用途、所有者或项目等对资源进行分类。',
            tagList: [],
            tagIndex: 1, // 收集当前tagIndex
            tagIndexList: [0],
            tagKeyTip: '',
            tagValueTip: '',
            tags: [],
            tagTip: '',
            doc: 'https://cloud.baidu.com/doc/TAG/index.html',
        };
    }

    onTagKeyInput(e, index) {
        this.checkTagKey(index, e.value);
        this.updateTagValueDS(e.value, index);
    }

    onTagValueInput(e, index) {
        this.checkTagValue(index, e.value);
    }

    onTagKeyChange(e, index) {
        this.checkTagKey(index, e.value);
        this.updateTagValueDS(e.value, index);
    }

    /**
     * [onAddTag 添加标签]
     */
    onAddTag() {
        // 如果存在非法标签，则不可再添加新标签
        if (this.checkAllTag(true)) {
            let tagIndex = this.data.get('tagIndex');
            // 给标签键设置下拉框的datasource，过滤掉已添加的key
            this.updateTagKeyDS(tagIndex);
            // 更新相应的标签值
            this.updateTagValueDS('', tagIndex);
            this.data.push('tagIndexList', tagIndex);
            // 收集新增tagIndex
            this.data.set('tagIndex', ++tagIndex);
        }
    }

    /**
     * [onRemoveTag 删除标签]
     *
     * @param  {[type]} index [标签索引]
     */
    onRemoveTag(index) {
        let {tagObj, tagIndexList} = this.data.get();
        delete tagObj[index];
        this.data.set('tagObj', tagObj);
        this.data.removeAt('tagIndexList', tagIndexList.indexOf(index));
        this.checkAllTag();
    }

    /**
     * 校验所有标签合法性
     *
     * @return {boolean} 是否合法
     */
    checkAllTag(disallowEmptyTagKey) {
        const tagObj = this.data.get('tagObj') || {};
        if (Object.keys(tagObj).length < 1) {
            this.data.set('tagTip', '');
            this.data.set('tagKeyTip', '');
            this.data.set('tagValueTip', '');

            return true;
        }
        const isValid = !_.some(_.keys(tagObj), index => {
            const tagKey = this.data.get(`tagObj[${index}].tagKey`);
            const tagValue = this.data.get(`tagObj[${index}].tagValue`);
            const isTagKeyValid = this.checkTagKey(index, tagKey, disallowEmptyTagKey);
            const isTagValueValid = this.checkTagValue(index, tagValue);
            return !isTagKeyValid || !isTagValueValid;
        });
        !isValid && focusTo('#tagList', 'center');
        return isValid;
    }

    /**
     * 获取视图中已存在的标签key
     *
     * @param {number} tagIndex 标签索引
     *
     * @return {Array} createds 已存在的标签key
     */
    findCreatedKeys(tagIndex) {
        let {tagObj, tagIndexList} = this.data.get();
        tagIndexList = _.difference(tagIndexList, [+tagIndex]);
        let createds = _.map(tagIndexList, key => tagObj[key].tagKey);
        return createds;
    }

    /**
     * 过滤并更新标签数据源
     *
     * @param {number} tagIndex 标签索引
     * @param {Array} createds 已存在的标签
     *
     */
    updateTagKeyDS(tagIndex) {
        let allKeys = this.data.get('tagKeys');
        let createdKeys = this.findCreatedKeys(tagIndex);
        let data = allKeys.filter(item => !createdKeys.includes(item.value));
        this.data.set(`tagObj[${tagIndex}].tagKey`, '');
        this.data.set(`tagObj[${tagIndex}].tagKeyList`, data);
    }

    /**
     * 更新标签值数据源
     *
     * @param {string} tagKey 标签键
     * @param {number} tagIndex 标签索引
     *
     */
    updateTagValueDS(tagKey, tagIndex) {
        let tagValueList = [];
        if (tagKey) {
            let allTagKeys = this.data.get('tagKeys');
            let isExist = _.some(allTagKeys, item => item.value === tagKey);
            if (isExist) {
                // 更新对应的tagValues的datasource,取当前key对应的value列表
                let tagGroups = this.data.get('tagGroups');
                tagValueList = tagGroups[tagKey];
            } else {
                // 如果用户输入一个新key，则更新对应value列表为所有value值
                let allTagVals = this.data.get('tagValues');
                tagValueList = allTagVals;
            }
        }
        this.data.set(`tagObj[${tagIndex}].tagValue`, '');
        this.data.set(`tagObj[${tagIndex}].tagValueList`, tagValueList);
    }

    /**
     * [loadTagList 获取标签列表]
     *
     * @return {type} [description]
     */
    loadTagList() {
        return this.$http
            .getTagList({serviceType: [], region: ['global']})
            .then(({result}) => {
                let tagKeys = _.uniq(_.pluck(result, 'tagKey'));
                let tagValues = _.uniq(_.pluck(result, 'tagValue')).filter(item => item !== ''); // 过滤掉空值

                tagKeys = tagKeys.map(item => ({text: item, value: item}));

                this.data.set('tagKeys', tagKeys);

                tagValues = tagValues.map(item => ({text: item, value: item}));
                this.data.set('tagValues', tagValues);
                let tagGroups = _.groupBy(result, 'tagKey');
                _.each(tagGroups, (tags, key) => {
                    tagGroups[key] = tags
                        .filter(item => item.tagValue !== '')
                        .map(item => {
                            return {text: item.tagValue, value: item.tagValue};
                        });
                });
                this.data.set('tagGroups', tagGroups);
                this.data.set('tagObj', {
                    0: {
                        tagKey: '',
                        tagValue: '',
                        tagKeyList: tagKeys,
                        tagValueList: tagValues,
                    },
                });
            })
            .catch(e => {
                // 容错：如果失败，依然默认展示“默认项目”
                this.data.set('tagObj', {
                    0: {
                        tagKey: '',
                        tagValue: '',
                        tagKeyList: [{text: '默认项目', value: '默认项目'}],
                        tagValueList: [],
                    },
                });
                this.data.set('tagIndexList', [0]);
            });
    }

    getFormData() {
        const {tagObj, tagIndexList} = this.data.get();
        const tags = (
            tagObj
                ? tagIndexList.map(key => ({
                      tagKey: tagObj[key].tagKey,
                      tagValue: tagObj[key].tagValue,
                  }))
                : []
        ).filter(tag => !!tag.tagKey);

        return tags;
    }

    validateForm() {
        return this.checkAllTag();
    }

    /**
     * 校验标签键合法性
     *
     * @param {number} tagIndex 标签索引
     * @param {string} tagKey 标签key
     *
     * @return {boolean} 是否合法
     */
    checkTagKey(tagIndex, tagKey, disallowEmptyTagKey) {
        let isValid = true;
        let isUniqueValid = true;
        const createdKeys = this.findCreatedKeys(tagIndex);
        // 校验key是否为空&格式&校验key唯一性
        if (!tagKey) {
            if (disallowEmptyTagKey) {
                isValid = false;
                this.data.set('tagKeyTip', '请输入键值');
            }
        } else if (!/^[\u4e00-\u9fa5\w\-\/\.]{0,65}$/.test(tagKey)) {
            isValid = false;
            this.data.set(
                'tagKeyTip',
                '标签键必须是长度1-65大小写字母、数字、中文以及-_ /.特殊字符',
            );
        } else if (createdKeys.includes(tagKey)) {
            isUniqueValid = false;
            this.data.set('tagTip', `标签键必须唯一。键为${tagKey}的标签发生重复。`);
        }

        isUniqueValid && this.data.set('tagTip', '');
        isValid && this.data.set('tagKeyTip', '');

        return isValid && isUniqueValid;
    }

    /**
     * 校验标签值合法性
     *
     * @param {number} tagIndex 标签索引
     * @param {string} tagValue 标签value
     *
     * @return {boolean} 是否合法
     */
    checkTagValue(tagIndex, tagValue) {
        let isValid = true;
        if (!/^(?!所有值|空值$)[\u4e00-\u9fa5\w\-\/\.]{0,65}$/.test(tagValue)) {
            isValid = false;
        }
        this.data.set(
            'tagValueTip',
            isValid
                ? ''
                : '标签值必须是长度1-65大小写字母、数字、中文以及-_ /.特殊字符，不包含关键字"所有值"、"空值"',
        );
        return isValid;
    }

    inited() {
        this.loadTagList().then(() => {
            const {isEdit, tags, initTags} = this.data.get();

            if ((isEdit || initTags) && tags?.length > 0) {
                this.initTags(tags);
            }
        });
        this.watch('tags', val => {
            this.initTags(val);
        });
    }

    initTags(tags) {
        const tagObj = {};
        const tagIndexList = [];
        const tagGroups = this.data.get('tagGroups') || {};
        const allTagKeys = this.data.get('tagKeys') || [];
        const existedTagKeys = tags.map(tag => tag.tagKey);

        tags.forEach((tag, index) => {
            const {tagKey, tagValue} = tag;
            const tagKeyList = allTagKeys.filter(
                ({value}) => value === tagKey || !existedTagKeys.includes(value),
            );
            const tagValueList = tagGroups[tagKey] || [];

            tagObj[index] = {
                tagKey,
                tagValue,
                tagKeyList,
                tagValueList,
            };
            tagIndexList[index] = index;
        });

        this.data.set('tagObj', tagObj);
        this.data.set('tagIndexList', tagIndexList);
        this.data.set('tagIndex', tags.length);
    }
}
