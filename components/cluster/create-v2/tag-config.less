.tag-config-form {
    .s-form-item-label {
        width: 130px;
    }

    .s-form-item {
        &.s-form-item-inline {
            &:first-child {
                margin-bottom: 0;
            }

            &.tag-config-extra {
                margin-top: 12px;
            }
        }

        .s-form-item-control {
            .tag-item {
                margin-bottom: 10px;

                > label {
                    vertical-align: middle;
                }

                .close {
                    vertical-align: middle;
                    color: #2468f2;
                    cursor: pointer;
                }

                .bui-textbox-default {
                    &::after {
                        width: 6px;
                        height: 6px;
                        border-width: 1px;
                        top: 14px;
                        right: 14px;
                    }
                    input {
                        font-size: 12px;
                    }
                }
            }

            .bui-textbox {
                height: auto;
            }
        }

        .com-color-red {
            color: #eb5252;
        }

        .tag-config-add {
            display: flex;
            align-items: center;

            s-link {
                display: inline-flex;
                margin: 0 12px 0 20px;
            }

            .s-button {
                padding-left: 0;
            }
            .tag-add-icon {
               position: relative;
               top: -1px;
            }
        }

        &.mb10 {
            margin-bottom: 10px;
        }

        .bg-wrap {
            padding: 16px;
            background: #F7F7F9;
            border-radius: 6px;

            &.expand {
                width: 890px;
            }
        }
    }
}
