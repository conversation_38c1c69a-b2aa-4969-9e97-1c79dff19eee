/**
 *
 * @file components/cluster/create-v2/template/config.js
 * <AUTHOR>
 */

export const ClusterTemplateType = {
    CUSTOM: 'CUSTOM',
    MANAGEDPRO: 'MANAGEDPRO',
    START: 'START',
    HPC: 'HPC',
    BBC: 'BBC',
    EXIST: 'EXIST',
    SERVERLESS: 'SERVERLESS',
    BEC: 'BEC',
    ARM: 'ARM',
};

export default [
    {
        type: ClusterTemplateType.MANAGEDPRO,
        icon: 'master',
        title: '标准Kubernetes托管集群',
        desc: '集群Master由容器引擎CCE完全托管，您只需要购买Worker节点来运行工作负载即可',
        case: '适用于稳定性要求较高的生产环境，既提供了高可用保障，还能节省资源和运维成本',
        tagText: '推荐',
        tag: 'normal',
    },
    {
        type: ClusterTemplateType.CUSTOM,
        icon: 'jiedian',
        title: '标准Kubernetes独立集群',
        desc: '集群Master将会部署在您购买的服务器实例上，需要自行规划、维护、升级服务器集群',
        case: '适用于Kubernetes研究与定制化场景',
    },
    {
        type: ClusterTemplateType.START,
        icon: 'start-guide',
        title: '入门测试集群',
        desc: '入门级别的标准Kubernetes独立集群，自定义单副本Master节点和若干Worker节点',
        case: '适用于对稳定性和可靠性要求较低，希望低成本快速部署的非生产（体验测试）集群',
    },
    {
        type: ClusterTemplateType.HPC,
        icon: 'ai-app1',
        title: 'AI HPC集群',
        desc: '单机8卡，Nvlink互联，百G高速RDMA网络',
        case: '大规模AI训练与推理',
        tag: 'new',
        tagText: 'NEW',
    },
    {
        type: ClusterTemplateType.BBC,
        icon: 'bbc',
        title: '裸金属集群',
        desc: '托管和自定义Master节点两种部署模式，支持智能网卡BBC节点',
        case: '适合高负载和高性能的业务',
    },
    {
        type: ClusterTemplateType.EXIST,
        icon: 'bcc',
        title: '使用已有服务器创建集群',
        desc: `可选择在${CCE_CONSOLE_TITLE}上已购买的服务器资源作为Master节点和Worker节点来创建集群`,
        case: '根据自身需求选择不同的集群配置，集群创建过程不产生其他费用',
    },
    {
        type: ClusterTemplateType.SERVERLESS,
        icon: 'yunjisuan',
        title: 'Serverless Kubernetes集群',
        desc: '无需管理任何节点，通过容器实例创建工作负载，集群支持绝大部分Kubernetes API',
        case: '轻量级Kubernetes，无需创建和管理任何节点资源，容器实例只在运行时按需计费',
    },
    {
        type: ClusterTemplateType.BEC,
        icon: 'bcc',
        title: '边缘Kubernetes集群',
        desc: 'Master独立部署在云端，可接入多地域边缘计算节点作为Worker节点的集群',
        case: '云端管控边缘资源的调度、进行部署和运维，边缘场景下获得一致的Kubernetes体验',
        tag: 'new',
        tagText: 'NEW',
    },
    {
        type: ClusterTemplateType.ARM,
        icon: 'ar',
        title: 'ARM Kubernetes集群',
        desc: '仅支持购买百度智能云ARM架构的服务器资源作为Master节点和Worker节点来创建集群',
        case: '对性能要求不高，希望降低资源使用成本',
        tag: 'new',
        tagText: 'NEW',
    },
];
