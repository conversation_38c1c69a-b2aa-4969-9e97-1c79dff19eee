.cce-create-template-list {
    background-color: #f7f7f9;
    .cce-create-template-item {
        width: 305px;
        background-color: #fff;
        border-radius: 4px;
        margin-top: 15px;
        margin-bottom: 15px;
        margin-right: 30px;
        float: left;
        &:nth-child(3n) {
            margin-right: 0px;
        }
    }

    .template-title,
    .template-content {
        padding: 20px;
    }

    .template-title {
        // background: #F6F7Fb;
        color: #000;
        font-size: 14px;
        position: relative;

        .iconfont {
            font-size: 25px;
            color: #333;
            margin-right: 15px;
            vertical-align: middle;
        }

        .template-title-content {
            display: inline-block;
            zoom: 1;
            width: 214px;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .template-desc,
    .template-case {
        margin-bottom: 20px;
        color: #999;
        min-height: 35px;

        label {
            font-weight: bold;
        }
    }

    .template-bottom {
        text-align: center;
    }

    .bui-button-label {
        font-size: 14px;
    }

    .template-tag {
        position: absolute;
        right: 10px;
        line-height: 18px;
        border-radius: 15px;
        padding: 0 8px;
        font-size: 12px;
        color: #fff;
        display: inline-block;
        zoom: 1;
        min-width: 45px;
        text-align: center;
    }

    .template-tag-normal {
        background: #108cee;
    }

    .template-tag-new {
        background: #f38900;
    }
}
