/**
 * @file components/cluster/create-v2/template/template.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {store} from 'san-store';

import tips from '../../../../utils/tips';
import {ClusterType} from '../../../../utils/enums';
import {MasterType, CreateType, ClusterHA, NetworkMode} from '../enums';
import Config from './config';
import {checkWhiteByName} from '../../../../common/white';
import {PermissionType} from '../../../../utils/enums';

const {asComponent, invokeBceSanUI} = decorators;
const $flag = ServiceFactory.resolve('$flag');

const template = html`<div>
    <ui-biz-legend label="{{title}}">
        <div class="cce-create-template-list">
            <div class="cce-create-template-item" s-for="item in list">
                <div class="template-title">
                    <ui-icon name="{{item.icon}}" />
                    <span title="{{item.title}}" class="template-title-content"
                        >{{item.title}}</span
                    >
                    <span s-if="item.tag" class="template-tag template-tag-{{item.tag}}"
                        >{{item.tagText || '-'}}</span
                    >
                </div>
                <div class="template-content">
                    <p class="template-desc">
                        <label>描述：</label>
                        <span>{{item.desc}}</span>
                    </p>
                    <p class="template-case">
                        <label>场景：</label>
                        <span>{{item.case}}</span>
                    </p>
                    <p class="template-bottom">
                        <ui-button
                            s-if="item.type === 'SERVERLESS'"
                            label="创建"
                            size="large"
                            skin="primary"
                            on-click="onClick(item.type)"
                            tip="{{role.tip}}"
                            disabled="{{role.disabled}}"
                            layerStyle="{{layerStyle}}"
                        />
                        <ui-button
                            s-elif="item.type === 'BEC'"
                            label="创建"
                            size="large"
                            skin="primary"
                            on-click="onClick(item.type)"
                            tip="{{bec.tip}}"
                            disabled="{{bec.disabled}}"
                            layerStyle="{{becLayerStyle}}"
                        />
                        <ui-button
                            s-else
                            label="创建"
                            size="large"
                            skin="primary"
                            on-click="onClick(item.type)"
                        />
                    </p>
                </div>
            </div>
        </div>
    </ui-biz-legend>
</div>`;

@asComponent('@cce-cluster-create-template')
@invokeBceSanUI
export default class Template extends Component {
    static template = template;

    initData() {
        let list = Config;
        if (!$flag.CceCreateManagedMasterCluster) {
            list = _.filter(list, item => item.type !== 'MANAGEDPRO');
        }
        if (!$flag.CceClusterCreateBBC) {
            list = _.filter(list, item => item.type !== 'BBC');
        }
        if (!$flag.CceCceClusterCreateAiHpc) {
            list = _.filter(list, item => item.type !== 'HPC');
        }
        if (!$flag.CceCceClusterCreateServerless) {
            list = _.filter(list, item => item.type !== 'SERVERLESS');
        }
        if (!$flag.CceEableARMCluster || !checkWhiteByName(PermissionType.ENABLEARM)) {
            list = _.filter(list, item => item.type !== 'ARM');
        }
        // 云边集群只有bj、su、bd、hkg部署了
        const region = window.$context.getCurrentRegion().id;
        const allRegion = window.$context.getEnum('AllRegion');
        // 开放gz 用来灰度测试
        const enableRegion = [
            allRegion.BJ,
            allRegion.SU,
            allRegion.BD,
            allRegion.HKG,
            allRegion.GZ,
            allRegion.FWH,
        ];
        if (!$flag.CceCceClusterCreateCloudEdge || !_.includes(enableRegion, region)) {
            list = _.filter(list, item => item.type !== 'BEC');
        }

        if ($flag.CceClusterNoTestTemplate) {
            list = _.filter(list, item => item.type !== 'START');
        }
        return {
            title: '选择模板',
            list,
            role: {},
            layerStyle: {
                width: '320px',
            },
            bec: {},
            becLayerStyle: {
                width: '180px',
            },
        };
    }

    attached() {
        const hasBec = _.find(this.data.get('list'), item => item.type === 'BEC');
        hasBec &&
            this.checkCloudEdge()
                .then(data => {
                    if (!(data && data.isExist)) {
                        return Promise.reject();
                    }
                })
                .catch(() => {
                    this.data.set('bec.disabled', true);
                    this.data.set(
                        'bec.tip',
                        '创建边缘Kubernetes集群需先开通白名单' +
                            ' <a href="' +
                            tips.doc.bec +
                            '" target="_blank">' +
                            '立即开通' +
                            '</a>',
                    );
                });

        const hasServerless = _.find(this.data.get('list'), item => item.type === 'SERVERLESS');
        if (hasServerless) {
            const region = window.$context.getCurrentRegion().id;
            const bci = window.$context.SERVICE_TYPE?.BCI;
            const regions = _.keys(bci?.region);
            if (!_.includes(regions, region)) {
                this.data.set('role.disabled', true);
                this.data.set('role.tip', '暂不支持该地域');

                return;
            }
            this.preCheckCluster()
                .then(() => this.checkStsRole())
                .catch(err => {
                    if (err) {
                        if (err.code === 'BLBNotAvailable') {
                            this.data.set('role.disabled', true);
                            const tip = `创建该类型集群，将默认为API Server创建一个内网BLB实例。
                                您暂时不符合在当前地域下创建BLB的条件，请查看&nbsp;&nbsp;
                                <a href="${tips.doc.serverlessBlb}" target="_blank">创建BLB实例说明</a>
                            `;
                            this.data.set('role.tip', tip);
                        } else if (err.code === 'RegionNotAvailable') {
                            this.data.set('role.disabled', true);
                            this.data.set('role.tip', '暂不支持该地域');
                        }
                    }
                });
        }
    }

    preCheckCluster() {
        return this.$http.preCheckCluster();
    }

    checkCloudEdge() {
        // 云边集群白名单控制
        return this.$http.checkWhiteList('EnableCCEonBEC');
    }

    checkStsRole() {
        const AllRegion = window.$context.getEnum('AllRegion');
        const tip =
            '创建Serverless集群需要先开通容器实例服务 <a href="/bci/" target="_blank">立即开通</a>';
        return this.$http
            .checkStsRole({roleName: 'BceServiceRole_bci'}, {region: AllRegion.BJ})
            .then(({result}) => {
                if (result && result.id) {
                    this.data.set('role.disabled', false);
                    this.data.set('role.tip', '');
                } else {
                    this.data.set('role.disabled', true);
                    this.data.set('role.tip', tip);
                }
            })
            .catch(() => {
                this.data.set('role.disabled', true);
                this.data.set('role.tip', tip);
            });
    }

    onClick(type) {
        store.dispatch('setClusterType', null);
        store.dispatch('setClusterTemplateType', type);
        switch (type) {
            case 'START':
                store.dispatch('setMasterType', MasterType.CUSTOM);
                store.dispatch('setClusterHA', ClusterHA.HA1);
                break;
            case 'MANAGEDPRO':
                store.dispatch('setMasterType', MasterType.MANAGEDPRO);
                break;
            case 'CUSTOM':
                store.dispatch('setMasterType', MasterType.CUSTOM);
                break;
            case 'EXIST':
                store.dispatch('setCreateType', CreateType.EXIST);
                break;
            case 'SERVERLESS':
                store.dispatch('setIsServerless', true);
                break;
            case 'BEC':
                store.dispatch('setClusterType', ClusterType.CLOUD_EDGE);
                break;
            case 'BBC':
                store.dispatch('setNetworkMode', NetworkMode.VPC_HYBRID);
                break;
            case 'HPC':
                store.dispatch('setClusterType', ClusterType.AI_HPC);
                break;
            case 'ARM':
                store.dispatch('setClusterType', ClusterType.ARM);
                break;
            default:
                break;
        }
        this.fire('next');
    }
}
