/**
 * @file components/cluster/create-v2/worker/index.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, ServiceFactory, html} from '@baiducloud/runtime';
import * as AsyncValidator from 'async-validator';
import {connect} from 'san-store';
import IamSelct from '@components/iam-select';
import StoreMap from '../../../../pages/cluster/create-v2/store/map';
import {createK8sForm} from '../../../../common/biz/helper';
import tips from '../../../../utils/tips';
import {MasterType} from '../enums';
import AdvancedConfigContainer from '@pages/cluster/create-v2/components/advanced-config-container';

const {asComponent, invokeBceSanUI, invokeComp} = decorators;
const $flag = ServiceFactory.resolve('$flag');

const Schema = AsyncValidator.default;

const rules = new Schema({});

const template = /* san */ html`<template>
    <ui-biz-legend label="{{title}}" class="cce-create-cluster-worker-config">
        <ui-form
            s-ref="form"
            rules="{{rules}}"
            form-data="{=formData=}"
            form-errors="{{formErrors}}"
        >
            <ui-form-item inline label="VPC网络：" s-if="!isManaged">
                <span class="cluster-create-text-line"> {{selectedVpcItem.text}} </span>
            </ui-form-item>

            <ui-form-item
                inline
                s-if="!isManaged"
                label="Worker安全组："
                help="${tips.cluster.securityGroup}"
            >
                <cee-cluster-security-group
                    s-ref="security-group"
                    type="Worker"
                    exposedPublic="{{exposedPublic}}"
                    vpcId="{{selectedVpcItem.shortId}}"
                    vpcOriginId="{{selectedVpcItem.vpcId}}"
                />
            </ui-form-item>

            <ui-form-item inline label="节点配置：" s-if="!isManaged" required>
                <cce-cluster-create-node
                    s-ref="worker"
                    node-type="Worker"
                    on-add="onAdd"
                    on-delete="onDelete"
                />
                <div class="cluster-create-node-invalid-label" s-if="error">
                    托管Master需创建至少2个Worker节点
                </div>
            </ui-form-item>

            <iam-select s-if="supportIamSelect" s-ref="iamSelect"></iam-select>

            <advanced-config-container open="{=advancedOpen=}">
                <cce-data-root s-ref="dataRoot" runtimeType="{{runtimeType}}" />

                <cce-cluster-create-script s-ref="script" />

                <ui-form-item
                    inline
                    label="封锁节点（cordon）："
                    name="enableCordon"
                    help="开启封锁节点后，节点将处于不可调度状态，新创建的Pod将不会调度到该节点上。若需要取消封锁，请执行kubectl uncordon命令。"
                >
                    <ui-switch checked="{=formData.enableCordon=}" />
                </ui-form-item>

                <cee-resource-reserved2 s-ref="resourceReserved" labelWidth="140px" />

                <cce-flex-tag
                    class="tags-hidden-border"
                    hiddenK8s="{{true}}"
                    s-ref="flexTag"
                    s-if="!$flag.CceClusterCreateDefaultTags"
                />

                <label-form
                    class="tag-labels"
                    s-ref="labelsForm"
                    form-data="{{labelFormData}}"
                    isflex="{{true}}"
                />

                <cce-taints-config s-ref="taints" />
            </advanced-config-container>
        </ui-form>
    </ui-biz-legend>
</template>`;

@invokeBceSanUI
@invokeComp(
    '@cce-cluster-create-script',
    '@cce-cluster-create-node',
    '@cce-flex-tag',
    '@cee-cluster-security-group',
    '@cce-taints-config',
    '@cee-resource-reserved2',
    '@cce-data-root',
)
class Worker extends Component {
    static template = template;

    static components = {
        'label-form': createK8sForm(),
        'iam-select': IamSelct,
        'advanced-config-container': AdvancedConfigContainer,
    };

    static computed = {
        supportIamSelect() {
            const region = window.$context.getCurrentRegionId();
            const serviceTypes = this.data.get('serviceTypes');
            if (region === 'edge' || this.data.get('clusterType') !== 'arm') {
                return false;
            }
            return serviceTypes.includes('BCC') || serviceTypes.includes('EBC');
        },
    };

    initData() {
        return {
            title: 'Worker配置',
            formData: {
                enableCordon: false,
            },
            formErrors: null,
            rules,
            advancedOpen: false,
            error: false,
            labelFormData: {},
            $flag,
            serviceTypes: [],
        };
    }

    getFormData() {
        const formData = this.data.get('formData');
        const runtimeType = this.data.get('runtimeType');
        const runtimeVersion = this.data.get('runtimeType');
        const worker = this.ref('worker');
        const iamRole = this.ref('iamSelect') ? this.ref('iamSelect').getFormData() : {};
        const script = this.ref('script');
        const flexTagData = this.ref('flexTag') ? this.ref('flexTag').getFormData() : {};
        const {kubeReserved, systemReserved, customKubeletParams} =
            this.ref('resourceReserved').getFormData();
        const bccTags = flexTagData ? flexTagData.bccTags : [];
        const relationTag = flexTagData ? flexTagData.relationTag : false;
        let labels = {};
        const tagsLabel = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');
        const tags = _.map(tagsLabel.getValue(), (value, key) => ({key, value}));
        const securityGroup = this.ref('security-group');
        _.each(tags, tag => {
            if (tag.key || tag.value) {
                labels[tag.key] = tag.value;
            }
        });
        // 去掉显存共享设置，使用默认参数
        labels['cce.baidubce.com/gpu-share-device-plugin'] = 'disable';

        const taints = this.ref('taints') && this.ref('taints').getFormData();
        const dataRoot = this.ref('dataRoot') && this.ref('dataRoot').getFormData();
        return _.extend(
            {},
            {
                workers: worker.getFormData(),
                workerAdvanced: _.extend(
                    {},
                    formData,
                    script ? script.getFormData() : {},
                    {labels},
                    {tags: bccTags},
                    {relationTag},
                    {securityGroup: securityGroup && securityGroup.getFormData()},
                    {kubeReserved},
                    {systemReserved},
                    {customKubeletParams},
                    {taints},
                    dataRoot,
                    {runtimeType},
                    {runtimeVersion},
                    {iamRole},
                ),
            },
        );
    }

    onAdd(data) {
        this.checkNodeServiceType(data);
        this.fire('node-change', {
            type: 'worker',
            data,
        });
        this.data.set('error', false);
    }

    onDelete(data) {
        this.checkNodeServiceType(data);
        this.fire('node-change', {
            type: 'worker',
            data,
        });
        this.data.set('error', false);
    }

    checkNodeServiceType(data) {
        const serviceTypes = data?.customData?.map(item => item.serviceType) || [];
        this.data.set('serviceTypes', serviceTypes);
    }

    validateForm() {
        const advancedOpen = this.data.get('advancedOpen');
        const masterType = this.data.get('masterType');
        const tags = this.ref('labelsForm') && this.ref('labelsForm').ref('labelsForm');
        const flexTag = this.ref('flexTag');
        const taints = this.ref('taints');
        const resourceReserved = this.ref('resourceReserved');
        const worker = this.ref('worker');
        const dataRoot = this.ref('dataRoot');

        this.data.set('error', false);
        if (masterType === MasterType.MANAGEDPRO) {
            const securityGroup = this.ref('security-group');

            const promiseArr = [
                worker.validateForm(),
                Promise.all([
                    flexTag ? flexTag.validateForm() : Promise.resolve(),
                    dataRoot ? dataRoot.validateForm() : Promise.resolve(),
                    tags.validateForm(),
                    securityGroup.validateForm(),
                    resourceReserved.validateForm(),
                ]).catch(() => {
                    if (!advancedOpen) {
                        this.data.set('advancedOpen', true);
                    }

                    return Promise.reject();
                }),
            ];

            return Promise.all(promiseArr).then(() => {
                if (worker.getInstanceCount() < 2) {
                    this.data.set('error', true);

                    return Promise.reject();
                }

                return Promise.resolve();
            });
        }
        const customPromiseArr = [
            flexTag ? flexTag.validateForm() : Promise.resolve(),
            tags.validateForm(),
            taints.validateForm(),
        ];
        return Promise.all(customPromiseArr)
            .then(() => {
                return Promise.resolve();
            })
            .catch(() => {
                if (!advancedOpen) {
                    this.data.set('advancedOpen', true);
                }

                return Promise.reject();
            });
    }
}

@asComponent('@cce-cluster-create-worker')
export default class WorkerStore extends connect.san(StoreMap)(Worker) {}
