/**
 * @file components/cluster/instance/field.js
 * <AUTHOR>
 */

import _ from 'lodash';

import {InstanceTypeV2} from '../../../utils/enums';

export function getData(result) {
    const instancePage = _.get(result, 'instancePage', []);
    const nodes = _.map(instancePage.instanceList, node => {
        node = _.extend(node.spec, node.status);
        let {instancePhase} = node;
        const {instanceType, clusterRole, instanceChargingType, clusterID} = node;
        const {cpu, mem, rootDiskSize} = node.instanceResource;
        const {instanceUUID, instanceID, vpcIP, eip, hostname} = node.machine;
        const {eipBandwidth} = node.eipOption;
        const obj = {
            instanceShortId: instanceID,
            status: instancePhase.toLocaleUpperCase(),
            instanceUuid: instanceUUID,
            availableZone: node.vpcConfig.availableZone,
            cpu,
            memory: mem,
            sysDisk: rootDiskSize,
            instanceType: InstanceTypeV2.getAliasFromValue(instanceType) || instanceType,
            groupId: node.instanceGroupID,
            groupName: node.instanceGroupName,
            fixIp: vpcIP,
            role: clusterRole,
            paymentMethod: instanceChargingType,
            clusterUuid: clusterID,
            eip: eip,
            eipBandwidth: eipBandwidth,
            vpcId: node.vpcConfig.vpcID,
            hostname
        };
        return _.defaults(obj, node);
    });
    instancePage.result = nodes;
    delete instancePage.instanceList;
    return instancePage;
}
