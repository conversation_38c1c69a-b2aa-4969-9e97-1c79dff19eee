/**
 * @file components/cluster/instance/list.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Select, Form, Button, Input, Table} from '@baidu/sui';
import {OutlinedSearch} from '@baidu/sui-icon';

import {fixedToUnit} from '../../../utils/helper';
import {NodeInstanceType, ProductType, InstanceStatus} from '../../../utils/enums';
import {getRegionInfo} from '../../../utils/bec-flavor';

const {asComponent} = decorators;

/* eslint-disable */
const template = html`<template>
    <s-table
        class="instance-table"
        columns="{{table.columns}}"
        datasource="{{table.datasource}}"
        selection="{=table.selection=}"
        max-height="{{table.maxHeight}}"
        on-selected-change="onSelectedChange"
        on-sort="onSort"
        on-filter="onFilter"
        loading="{{table.loading}}"
        >
        <div slot="c-instanceName">
            <a href="/bec/#/bec/vm-instance/detail?vmId={{row.machine.instanceID}}" title="{{row.instanceName}}" class="instance-name">
                {{row.instanceName}}
            </a>
            <br />
            <span class="instance-id" title="{{row.machine.instanceID}}">{{row.machine.instanceID}}</span>
        </div>
        <div slot="c-payment">
            <span>{{row.instanceChargingType | getPaymentText}}<span>
        </div>
        <div slot="c-status">
            <span
                s-if="row.instancePhase === 'ready' || row.instancePhase === 'running'"
                class="node-normal">Ready</span>
            <span
                s-elif="row.instancePhase === 'ready_scheduling_disabled'"
                class="node-normal">Ready<br><span class="node-error">已封锁</span></span>
            <span s-elif="row.instancePhase === 'not_ready'" class="node-error">NotReady</span>
            <span s-else class="status {{row.instancePhase | statusClass}}">{{row.instancePhase | statusText}}</span>
        </div>
        <div slot="c-role">
            <span>{{row.clusterRole | getRoleText}}<span>
        </div>
        <div slot="c-eip">
            <span>{{row.machine.eip || '-'}}/{{row.eipOption.eipBandwidth ? row.eipOption.eipBandwidth + 'Mbps' : '-'}}</span>
        </div>
        <div slot="c-vpcIP">
            <span>{{row.machine.vpcIP}}</span>
        </div>
        <div slot="c-config">
            <span>{{row.instanceResource | getResourceConfig}}</span>
        </div>
        <div slot="c-edge">
            <span>{{row.becOption | getSingleProvider}}</span>
        </div>
        <div slot="c-resoure">
            {{row | getResource | raw | empty}}
        </div>
    </s-table>
</template>
`;
/* eslint-enable */

@asComponent('@cce-bec-instance-list')
export default class InstanceList extends Component {
    static template = template;

    static components = {
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-input': Input,
        's-table': Table,
        's-option': Select.Option,
        's-icon-search': OutlinedSearch,
    };

    static filters = {
        getPaymentText(value) {
            return ProductType.getTextFromValue(value);
        },
        statusClass(value) {
            value = value ? value.toLocaleUpperCase() : '';
            const config = InstanceStatus.fromValue(value);
            return config ? config.klass : '';
        },
        statusText(value) {
            value = value ? value.toLocaleUpperCase() : '';
            return InstanceStatus.getTextFromValue(value) || '-';
        },
        getRoleText(value) {
            return NodeInstanceType.getTextFromValue(value || 'slave');
        },
        getResourceConfig(value) {
            const cpu = value.cpu || '-';
            const mem = value.mem || '-';
            const rootDisk = value.rootDiskSize || '-';
            const dataDisk = value.dataDisk || '-';
            const gpu = value.gpu || '-';
            let result = '';
            result += cpu !== '-' ? cpu + '核/' : cpu + '/';
            result += mem !== '-' ? mem + 'GB/' : mem + '/';
            result += rootDisk !== '-' ? rootDisk + 'GB/' : rootDisk + '/';
            result += dataDisk !== '-' ? dataDisk + 'GB/' : dataDisk + '/';
            result += gpu !== '-' ? 'Tesla T4*' + gpu : gpu;
            return result;
        },
        getSingleProvider(value) {
            if (!value || _.isEmpty(value)) {
                return '-';
            }
            const provider = [
                {
                    city: value.becCity,
                    region: value.becRegion,
                    replicas: 1,
                    serviceProvider: value.becServiceProvider,
                },
            ];
            return value && getRegionInfo(provider);
        },
        getResource(item) {
            if (item.role === 'master') {
                return '-';
            }
            const {
                memoryRequests = 0,
                memoryAllocatable = 0,
                cpuRequests = 0,
                cpuAllocatable = 0,
            } = _.get(item, 'resource.allocatedResources', {}) || {};
            return html`
                <div class="resource-name">CPU(核):</div>
                ${fixedToUnit(cpuRequests, 1000)}/${fixedToUnit(cpuAllocatable, 1000)}
                <br />
                <div class="resource-name">内存(G):</div>
                ${fixedToUnit(memoryRequests, 1024 * 1024 * 1024)}
                /${fixedToUnit(memoryAllocatable, 1024 * 1024 * 1024)}
                <br />
            `;
        },
    };
    onSelectedChange(e) {
        this.fire('selected-change', e);
    }
    onSort(e) {
        this.fire('sort', e);
    }
    onFilter(e) {
        this.fire('filter', e);
    }
}
