/**
 * @file cpmonents/cluster/precheck/createPreCheck.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, ServiceFactory} from '@baiducloud/runtime';
import {Table, Alert, Loading} from '@baidu/sui';
import {WorkFlowStatus, WorkFlowTaskType, WorkFlowPreCheckStatus} from '../../../utils/enums';

import PreCheckHeader from './precheck-header';

const $flag = ServiceFactory.resolve('$flag');

const {asComponent} = decorators;
const Options = {'x-silent': true};

/* eslint-disable */
const template = html`
<div class="cce-cluster-precheck">
    <precheck-header
        on-reCheck="reCheck"
        on-preConfirm="preConfirm"
        errorNum="{{errorNum}}"
        verifyNum="{{verifyNum}}"
        error="{{error}}"
        checkAccountError="{{checkAccountError}}"
        tip="{{tip}}"
    />
    <s-table
        loading="{{loading}}"
        columns="{{columns}}"
        datasource="{{datasource}}"
        error="{{tableError}}">
        <div slot="loading">
            <div class="table-loading-wrap">
                <s-loading
                    size="large"
                    loading="{{true}}">
                </s-loading>
                <div class="loading-text">初始化中...</div>
            </div>
        </div>
        <div slot="error">
            <div class="table-error-wrap">
                <div class="table-fail-icon">
                    <svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <title>07icon/正确错误警示/错误</title>
                        <g id="数据输入" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="10-输入框" transform="translate(-1197.000000, -6648.000000)">
                                <g id="感叹号" transform="translate(1198.000000, 6649.000000)">
                                    <circle id="Oval" stroke="#EA2E2E" fill="#EA2E2E" cx="20" cy="20" r="20"></circle>
                                    <polygon id="Shape" fill="#FFFFFF" fill-rule="nonzero" points="30 12.6315789 27.3684211 10 20 17.5438596 12.6315789 10 10 12.6315789 17.5438596 20 10 27.3684211 12.6315789 30 20 22.4561404 27.3684211 30 30 27.3684211 22.4561404 20"></polygon>
                                </g>
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="table-error-tip">初始化失败！</div>
                <div class="table-error-message">{{errorMessage}}</div>
            </div>
        <div>
    </s-table>
</div>`;
/* eslint-disable */

@asComponent('@cce-create-precheck')
export default class CreatePreCheck extends Component {
    static template = template;

    static components = {
        's-table': Table,
        's-alert': Alert,
        's-loading': Loading,
        'precheck-header': PreCheckHeader,
    };

    initData() {
        return {
            loading: true,
            columns: [
                {
                    name: 'workflowTaskType',
                    label: '检查项',
                    render(item) {
                        return (
                            WorkFlowTaskType.getTextFromValue(item.workflowTaskType) ||
                            _.escape(item.taskName)
                        );
                    },
                },
                {
                    name: 'workflowTaskPhase',
                    label: '状态',
                    render(item) {
                        // 如果taskExecuteResult里有状态 优先判断这个值
                        if (item.taskExecuteResult && item.taskExecuteResult.result) {
                            const config = WorkFlowPreCheckStatus.fromValue(
                                item.taskExecuteResult.result,
                            );
                            return `<span class="status ${config.klass || 'unavailable'}">
                                ${config.text || '-'}
                            </span>`;
                        }

                        let klass = '';
                        let text = '';
                        if ([WorkFlowStatus.SUCCESSDED].includes(item.workflowTaskPhase)) {
                            klass = 'normal';
                            text = '通过';
                        }
                        if (
                            [
                                WorkFlowStatus.FAILED,
                                WorkFlowStatus.UNKNOWN,
                                WorkFlowStatus.PAUSED,
                            ].includes(item.workflowTaskPhase)
                        ) {
                            klass = 'error';
                            text = '不通过';
                        }
                        if ([WorkFlowStatus.UPGRADING].includes(item.workflowTaskPhase)) {
                            klass = 'rolling';
                            text = '检查中';
                        }
                        if ([WorkFlowStatus.PENDING].includes(item.workflowTaskPhase)) {
                            klass = 'unavailable';
                            text = '待开始';
                        }
                        if (!klass || !text) {
                            return '-';
                        }
                        return `<span class="status ${klass}">${text}</span>`;
                    },
                },
                {
                    name: 'taskExecuteResult',
                    label: '说明',
                    width: 400,
                    render(item) {
                        let checkAccountError = '';
                        if (item.workflowTaskType === 'CheckAccount') {
                            if (
                                [
                                    WorkFlowStatus.FAILED,
                                    WorkFlowStatus.UNKNOWN,
                                    WorkFlowStatus.PAUSED,
                                ].includes(item.workflowTaskPhase)
                            ) {
                                checkAccountError = `<a href="/qualify/#/qualify/index" traget="_blank">，去完成</a>`;
                            }
                        }

                        let message = item?.taskExecuteResult?.message || '';

                        // 这个文案是后端接口返回，针对虚商临时处理
                        if ($flag.CceSupportXS) {
                            // eslint-disable-next-line
                            message = message.replace('或<a href="https://console.bce.baidu.com/finance/#/finance/account/recharge" target="_blank">充值</a>', '');
                        }

                        return message + checkAccountError;
                    },
                },
            ],
            datasource: [],
            tip: '',
            error: false,
            tableError: false,
        };
    }

    attached() {
        this.createWorkflow();
    }

    createWorkflow() {
        const {payload} = this.data.get();
        return this.$http
            .createPrecheckWorkflow(payload, Options)
            .then(({result}) => {
                this.data.set('workflowID', result.workflowID);

                return this.getPrecheckWorkflow(result.workflowID);
            })
            .catch(e => {
                if (e.message && e.message.global) {
                    this.data.set('tableError', true);
                    this.data.set('errorMessage', e.message.global);
                }
                this.data.set('loading', false);
            });
    }

    reCheck() {
        const {workflowID} = this.data.get();
        this.data.set('loading', true);
        return this.$http
            .suspendResumeWorkflowCreate(
                {
                    workflowID,
                    action: 'resume',
                },
                Options,
            )
            .then(() => this.getPrecheckWorkflow(workflowID))
            .catch(e => {
                if (e.message && e.message.global) {
                    this.data.set('tableError', true);
                    this.data.set('errorMessage', e.message.global);
                }
                this.data.set('loading', false);
            });
    }
    preConfirm() {
        const {payload} = this.data.get();
        payload.workflowConfig.CreateClusterPrecheckWorkflowConfig.confirmed = true;
        this.data.set('loading', true);
        return this.$http
            .createPrecheckWorkflow(payload, Options)
            .then(({result}) => {
                this.data.set('workflowID', result.workflowID);

                return this.getPrecheckWorkflow(result.workflowID);
            })
            .catch(e => {
                if (e.message && e.message.global) {
                    this.data.set('tableError', true);
                    this.data.set('errorMessage', e.message.global);
                }
                this.data.set('loading', false);
            });
    }

    getPrecheckWorkflow(workflowID) {
        this.data.set('error', false);
        this.dispatch('preCheck', false);
        return this.$http
            .getPrecheckWorkflow(workflowID)
            .then(({result}) => {
                const status = _.get(result, 'workflow.status.phase', '');

                const taskGroupList = _.get(result, 'workflow.status.taskGroupList', []);
                const taskGroup = _.find(taskGroupList, item => item.taskGroupName === 'PreCheck');
                const datasource = taskGroup ? taskGroup.taskList : [];
                this.data.set('datasource', datasource);

                // 接口异步 只有在status是fail 没有taskList的情况下 才认为是失败情况
                if (this.data.get('datasource').length === 0) {
                    const errorMessage = _.get(result, 'workflow.status.errorMessage');
                    if (errorMessage) {
                        this.data.set('tableError', true);
                        this.data.set('errorMessage', errorMessage);
                    }
                } else {
                    this.data.set('tableError', false);
                    this.data.set('errorMessage', '');
                }

                if (status === WorkFlowStatus.FAILED) {
                    this.data.set('error', true);

                    this.data.set(
                        'errorNum',
                        _.filter(
                            taskGroup.taskList,
                            task =>
                                [
                                    WorkFlowStatus.FAILED,
                                    WorkFlowStatus.UNKNOWN,
                                    WorkFlowStatus.PAUSED,
                                ].includes(task?.workflowTaskPhase) &&
                                task.taskExecuteResult?.result !== WorkFlowPreCheckStatus.VERIFYING,
                        ).length,
                    );
                    this.data.set(
                        'verifyNum',
                        _.filter(
                            taskGroup.taskList,
                            task =>
                                task.taskExecuteResult?.result === WorkFlowPreCheckStatus.VERIFYING,
                        ).length,
                    );
                }

                if ([WorkFlowStatus.SUCCESSDED, WorkFlowStatus.FAILED].includes(status)) {
                    if (this.timer) {
                        clearInterval(this.timer);
                        this.timer = null;
                    }

                    this.dispatch('preCheck', !this.data.get('error'));

                    this.data.set('loading', false);
                } else {
                    if (!this.timer) {
                        this.timer = setInterval(() => this.getPrecheckWorkflow(workflowID), 5000);
                    }
                    if (
                        !(
                            status !== WorkFlowStatus.FAILED &&
                            this.data.get('datasource').length === 0
                        )
                    ) {
                        this.data.set('loading', false);
                    }
                }
            })
            .catch(() => {
                this.data.set('loading', false);
                this.data.set('datasource', []);
            });
    }
}
