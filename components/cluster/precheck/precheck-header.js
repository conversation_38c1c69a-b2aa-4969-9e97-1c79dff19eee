/**
 * @file cpmonents/cluster/precheck/index.js
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Alert} from '@baidu/sui';

/* eslint-disable */
const template = html` <template>
    <s-alert s-if="tip && !error" skin="info" showIcon="{{false}}"> {{tip}} </s-alert>
    <s-alert s-if="error" skin="error">
        当前有<span s-if="errorNum">{{errorNum}}项未通过检查</span>
        <span s-if="errorNum && verifyNum">、</span>
        <span s-if="verifyNum">{{verifyNum}}项需要确认</span>
        ，您可以根据说明内容
        <a
            s-if="errorNum"
            href="javascript:void(0)"
            on-click="reCheck"
            data-track-id="cce_cluster_pre_check"
            data-track-name="重新检查"
        >
            重新检查
        </a>
        <span s-if="errorNum && verifyNum">和</span>
        <a
            s-if="verifyNum"
            href="javascript:void(0)"
            on-click="preConfirm"
            data-track-id="cce_cluster_pre_confirm"
            data-track-name="确认"
        >
            确认 </a
        ><span s-if="verifyNum">（已确认检查项将不再重新检查）</span>
    </s-alert>
</template>`;
/* eslint-disable */

export default class PreCheckHeader extends Component {
    static template = template;

    static components = {
        's-alert': Alert,
    };
    reCheck() {
        this.fire('reCheck');
    }
    preConfirm() {
        this.fire('preConfirm', true);
    }
}
