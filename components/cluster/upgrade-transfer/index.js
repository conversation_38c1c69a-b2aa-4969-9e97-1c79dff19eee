/**
 * @file components/cluster/upgrade-transfer/index.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {Table, Pagination, Button, Popover} from '@baidu/sui';
import {SearchBox} from '@baidu/sui-biz';
import {OutlinedDown, OutlinedUp, OutlinedClose} from '@baidu/sui-icon';

import Advanced from '../create-v2/advanced';
import Search from '../create-v2/instance/search';

const {asComponent} = decorators;

const template = html`<template>
    <div class="cce-cluster-upgrade-worker-transfer">
        <div class="all-instance-list">
            <div class="operation-wrap">
                <s-search-box
                    s-if="!advanced"
                    value="{=searchbox.keyword=}"
                    keyword-type="{=searchbox.keywordType=}"
                    datasource="{{searchbox.keywordTypes}}"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    on-search="onSearch"
                    width="200"
                />
                <cce-advanced open="{=advanced=}">
                    <div slot="icon">
                        <label>批量搜索</label>
                        <s-icon-down color="#108cee" s-if="!advanced" />
                        <s-icon-up color="#108cee" s-else />
                    </div>
                    <cce-search s-if="advanced" on-search="onBatchSearch" />
                </cce-advanced>
                <input type="text" style="display: none" />
            </div>
            <s-table
                s-ref="existTable"
                max-height="300"
                selection="{=selection=}"
                columns="{{table.schema}}"
                datasource="{{table.datasource}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-selected-change="onTableSelected($event)"
                on-row-enter="onRowEnter"
                on-row-leave="onRowLeave"
            >
                <div slot="c-instanceID">
                    <s-popover
                        s-if="!!row.disabledReason"
                        visible="{{row.visible}}"
                        content="{{row.disabledReason}}"
                    >
                        {{row.status.machine.instanceID || '-'}}
                    </s-popover>
                    <span s-else> {{row.status.machine.instanceID || '-'}} </span>
                </div>
                <div slot="error">
                    啊呀，出错了？
                    <a href="javascript:void(0)" on-click="getWorkerNodeList">重新加载</a>
                </div>
            </s-table>

            <s-pagination
                s-if="pager.count > 0"
                page="{=pager.page=}"
                total="{=pager.count=}"
                page-size="{=pager.size=}"
                on-pagerSizeChange="onPagerSizeChange($event)"
                on-pagerChange="onPagerChange($event)"
            />
        </div>
        <div class="selected-instance-list">
            <div class="selected-instance-list-wrap">
                <div class="operation-wrap">
                    已选择
                    <span class="count">{{selected.datasource.length}}</span>
                    个资源
                    <s-button skin="stringfy" on-click="onClearAll" class="clear-all"
                        >清空</s-button
                    >
                </div>
                <s-table
                    max-height="300"
                    columns="{{selected.schema}}"
                    datasource="{{selected.datasource}}"
                >
                    <div slot="c-vpcIP">
                        {{row.status.machine.vpcIP}}
                        <a
                            href="javascript:void(0)"
                            class="selected-delete"
                            on-click="onSelectedDelete(row, rowIndex)"
                        >
                            <s-icon-close />
                        </a>
                    </div>
                </s-table>
            </div>
        </div>
    </div>
</template> `;

@asComponent('@cce-cluster-upgrade-worker-transfer')
class Transfer extends Component {
    static template = template;

    static components = {
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-search-box': SearchBox,
        's-icon-close': OutlinedClose,
        's-icon-down': OutlinedDown,
        's-icon-up': OutlinedUp,
        'cce-advanced': Advanced,
        'cce-search': Search,
        's-popover': Popover,
    };

    initData() {
        return {
            selection: {
                mode: 'multi',
                selectedIndex: [],
                disabledIndex: [],
            },
            searchbox: {
                keyword: '',
                keywordType: ['name'],
                placeholder: '请输入实例名称搜索',
                keywordTypes: [
                    {
                        text: '实例名称',
                        value: 'instanceName',
                    },
                    {
                        text: '实例ID',
                        value: 'instanceID',
                    },
                    {
                        text: '内网IP',
                        value: 'vpcIP',
                    },
                ],
            },
            table: {
                schema: [
                    {
                        name: 'instanceID',
                        label: '实例ID',
                    },
                    {
                        name: 'cceInstanceID',
                        label: '实例名称',
                        render(item) {
                            return _.get(item, 'spec.cceInstanceID', '-');
                        },
                    },
                    {
                        name: 'version',
                        label: 'Kubernetes 版本',
                        render(item) {
                            return _.get(item, 'upgradeNodeFields.k8sVersion', '-');
                        },
                    },
                    {
                        name: 'vpcIP',
                        label: '内网IP',
                        render(item) {
                            return _.get(item, 'status.machine.vpcIP', '-');
                        },
                    },
                ],
                datasource: [],
            },
            selected: {
                schema: [
                    {
                        name: 'instanceID',
                        label: '实例ID',
                        render(item) {
                            return _.get(item, 'status.machine.instanceID', '-');
                        },
                    },
                    {
                        name: 'vpcIP',
                        label: '内网IP',
                        render(item) {
                            return _.get(item, 'status.machine.vpcIP', '-');
                        },
                    },
                ],
                datasource: [],
            },
            pager: {
                size: 10,
                page: 1,
                count: 0,
            },
            advanced: false,
        };
    }

    attached() {
        this.getWorkerNodeList();

        this.watch('advanced', value => {
            if (!value) {
                this.data.set('filters', '');
            } else {
                this.data.set('searchbox.keywordType', ['name']);
                this.data.set('searchbox.keyword', '');
            }
        });

        this.watch('selected.datasource', data => {
            const nodechange = this.data.get('nodechange');
            if (typeof nodechange === 'function') {
                nodechange(data);
            }
        });
    }

    onKeywordTypeChange(e) {
        const keywordTypes = this.data.get('searchbox.keywordTypes');
        const selectedItem = _.find(keywordTypes, item => item.value === e.value[0]);
        this.data.set('searchbox.placeholder', `请输入${selectedItem.text}进行搜索`);
    }

    getWorkerNodeList(payload = {pageNo: 1}) {
        this.data.set('selection.selectedIndex', []);

        this.data.set('table.loading', true);
        const {searchbox} = this.data.get();
        const pageSize = this.data.get('pager.size');
        const {keywordType, keyword} = searchbox;
        const options = _.extend(
            {
                keywordType,
                keyword,
                orderBy: keywordType[0],
                order: 'asc',
                pageSize,
                enableUpgradeNodeFields: true,
                clusterUuid: this.data.get('clusterUuid'),
                clusterRole: 'node',
            },
            payload,
        );

        return this.$http
            .listNodesV2(options)
            .then(({result}) => {
                if (!result) {
                    this.data.set('table.datasource', []);
                    return;
                }

                let instancePage = _.get(result, '.instancePage', []);

                const disabledIndex = [];
                _.each(instancePage.instanceList, (item, i) => {
                    const upgradeNodeFields = item.upgradeNodeFields;
                    if (upgradeNodeFields && !upgradeNodeFields.canBeSelected) {
                        disabledIndex.push(i);
                        item.disabledReason = upgradeNodeFields.reason;
                    }
                });

                // 编辑状态 已经选中的节点
                const list = this.data.get('selected.datasource');
                if (list && list.length > 0) {
                    /* eslint-disable */
                    let selectedIndex = [];
                    _.each(list, item => {
                        const findIndex = _.findIndex(
                            instancePage.instanceList,
                            d =>
                                _.get(d, 'spec.cceInstanceID') ===
                                _.get(item, 'spec.cceInstanceID'),
                        );
                        if (findIndex > -1) {
                            selectedIndex.push(findIndex);
                        }
                    });
                    this.data.set('selection.selectedIndex', selectedIndex);
                    /* eslint-enable */
                }

                this.data.set('selection.disabledIndex', disabledIndex);
                this.data.set('table.datasource', instancePage.instanceList);

                this.data.set('pager.count', _.get(instancePage, '.totalCount', 0));
                this.data.set('pager.page', _.get(instancePage, '.pageNo', 1));
            })
            .catch(e => {
                this.data.set('table.datasource', []);
                this.data.set('table.error', true);
            })
            .finally(() => {
                this.data.set('table.loading', false);
            });
    }

    onClearAll(e) {
        this.data.set('selected.datasource', []);
        this.data.set('selection.selectedIndex', []);
    }

    onSearch() {
        this.getWorkerNodeList();
    }

    onBatchSearch(e) {
        let filters = '';

        if (e.value && e.value.length > 0) {
            filters = e.value.join(',');
        }

        this.data.set('filters', filters);
        this.getWorkerNodeList({ipList: filters, pageNo: 1});
    }

    onPagerSizeChange({value}) {
        this.data.set('pager.size', value.pageSize);
        const payload = {ipList: this.data.get('filters') || '', pageNo: 1};
        this.getWorkerNodeList(payload);
    }

    onPagerChange({value}) {
        const payload = {ipList: this.data.get('filters') || '', pageNo: value.page};
        this.getWorkerNodeList(payload);
    }

    onTableSelected(e) {
        const value = e.value;
        // 之前选中的
        const selectedIndex = this.data.get('selection.selectedIndex');
        // 增加的
        const addItemsIndex = _.difference(value.selectedIndex, selectedIndex);
        // 删除的
        const deleteItemsIndex = _.difference(selectedIndex, value.selectedIndex);
        const datasource = this.data.get('table.datasource');
        _.each(deleteItemsIndex, index => {
            const item = datasource[+index];
            const findIndex = _.findIndex(
                this.data.get('selected.datasource'),
                data => data.spec?.cceInstanceID === item.spec?.cceInstanceID,
            );
            this.data.removeAt('selected.datasource', findIndex);
        });
        _.each(addItemsIndex, index => {
            const item = datasource[+index];
            this.data.push('selected.datasource', item);
            const i = this.data.get('selected.datasource').length - 1;
            this.data.set(`selected.datasource[${i}].loading`, true);
        });
    }

    onSelectedDelete(row, rowIndex) {
        const findIndex = _.findIndex(
            this.data.get('table.datasource'),
            data => _.get(data, 'spec.cceInstanceID') === _.get(row, 'spec.cceInstanceID'),
        );

        this.data.remove('selection.selectedIndex', findIndex);
        this.data.removeAt('selected.datasource', rowIndex);
    }

    getFormData() {
        return {
            instanceList: this.data.get('selected.datasource'),
        };
    }

    validateForm() {
        const selected = this.data.get('selected.datasource');
        if (selected.length > 0) {
            return true;
        }

        return false;
    }

    onRowEnter(e) {
        this.data.set(`table.datasource[${e.value}].visible`, true);
    }

    onRowLeave(e) {
        this.data.set(`table.datasource[${e.value}].visible`, false);
    }
}
