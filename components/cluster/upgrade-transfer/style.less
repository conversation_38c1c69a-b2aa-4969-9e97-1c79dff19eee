.cce-cluster-upgrade-worker-transfer {
    display: flex;
    justify-content: space-between;

    .all-instance-list {
        width: 530px;

        .s-pagination {
            margin-top: 10px;
            float: right;
        }
    }

    .selected-instance-list {
        margin-left: 20px;
        width: 270px;
    }

    .operation-wrap {
        margin-bottom: 20px;
        min-height: 30px;
        line-height: 30px;

        .count {
            color: #f33e3e;
        }

        .clear-all.s-button {
            padding: 0;
            display: inline;
            line-height: unset;
            float: right;
        }
    }

    .network-advanced-toggle {
        float: right;
        margin-right: 20px;
        position: relative;
    }

    .network-advanced-toggle.network-advanced-toggle-close {
        position: relative;
        top: 0;
        right: 20px;
    }

    .cce-cluster-transfer-search {
        margin: 0;

        .search-button {
            margin-top: 10px;
            font-size: 0;

            .s-button-skin-primary {
                margin-right: 10px;
            }
        }
    }

    .selected-delete {
        float: right;
    }

    .s-table-row-disabled {
        &:hover {
            cursor: not-allowed;

            > td {
                background: none;
            }
        }
        .s-table-cell {
            .s-table-cell-text {
                color: #999;
            }
        }
    }
}
