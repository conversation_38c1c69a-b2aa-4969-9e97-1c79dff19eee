/**
 * @file yaml-create.js
 * <AUTHOR>
 */

 import {Component} from 'san';
 import {html} from '@baiducloud/runtime';
 import {Button, Dialog, Form} from '@baidu/sui';
 import {ClipBoard} from '@baidu/sui-biz';
 import {ACEEditor} from '@baiducloud/bce-ui/san';
 
 export const getYamlTemplate = (slot) => html`
     <template>
         <s-dialog s-ref="dialog" open="{=open=}" title="{{title}}" class="workload-create-yaml">
             <div class="create-json-tip">
                 根据界面已选配置生成YAML，可自定义参数并提交创建{{info}}。每次打开编辑器都将重新生成参数，若需保存已编辑内容，请复制到剪切板。
                 ${slot}
             </div>
             <div class="subrow-content">
                 <ui-aceeditor
                     width="700"
                     height="400"
                     value="{=content=}"
                     class="aceeditor"
                     mode="ace/mode/yaml"
                     theme="ace/theme/twilight"
                     wrap="{{editor.wrap}}"
                     showGutter="{{editor.showGutter}}"
                 />
 
                 <s-clip-board class="clip-board" text="{{content}}">
                     <s-button>复制</s-button>
                 </s-clip-board>
             </div>
 
             <div slot="footer">
                 <s-button width="46" on-click="onClose">取消</s-button>
                 <s-button width="46" skin="primary" on-click="onConfirm">创建</s-button>
             </div>
         </s-dialog>
     </template>
 `;
 
 export class yamlCompoent extends Component {
     static template = getYamlTemplate();
 
     static components = {
         's-form': Form,
         's-form-item': Form.Item,
         's-button': Button,
         's-dialog': Dialog,
         's-clip-board': ClipBoard,
         'ui-aceeditor': ACEEditor
     };
 
     initData() {
         return {
             open: true,
             editor: {
                 value: '',
                 wrap: 'free',
                 showGutter: true
             },
             title: 'YAML创建',
             info: '工作负载',
             content: '',
         };
     }
 
     onClose() {
         this.data.set('open', false);
     }
 
     async onConfirm() {
         const content = this.data.get('content');
         this.fire('confirm', content);
         this.data.set('open', false);
     }
 }
 