/**
 * @file yaml-create.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Dialog, Form} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {ACEEditor} from '@baiducloud/bce-ui/san';
import {yamlCompoent, getYamlTemplate} from './yaml-common-create';

const template = html`
    <template>
        <div class="create-json-tip">
            <a href="{{yamlCreateUrl}}" target="_blank">使用YAML创建工作负载</a>
        </div>
    </template>
`;

export default class extends yamlCompoent {
    static template = getYamlTemplate(template);

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-dialog': Dialog,
        's-clip-board': ClipBoard,
        'ui-aceeditor': ACEEditor
    };

    initData() {
        const data = super.initData();
        return _.extend(data, {
            title: 'YAML创建工作负载',
            info: '工作负载',
            yamlCreateUrl: ''
        });
    }

    async attached() {
        const {type, clusterUuid, namespace} = this.data.get();
        const yamlCreateUrl =
            `#/cce/application/${type}/yaml-create?clusterUuid=${clusterUuid}` + `&namespaceName=${namespace}`;
        this.data.set('yamlCreateUrl', yamlCreateUrl);
    }

    onClose() {
        this.data.set('open', false);
    }

    async onConfirm() {
        const content = this.data.get('content');
        this.fire('confirm', content);
        this.data.set('open', false);
    }
}
