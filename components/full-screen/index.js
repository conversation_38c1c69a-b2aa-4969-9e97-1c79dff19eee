import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OutlinedFullScreen, OutlinedFullScreenOut} from '@baidu/sui-icon';
import {requestFullScreen, exitFullScreen} from '../../utils/util';
import './index.less';

const template = html`<template>
    <div
        class="{{isFullScreen ? 'full-screen full-screen-container': 'full-screen-container'}}"
        style="{{style}}"
        s-ref="full-screen-container"
    >
        <div class="action-bar">
            <slot name="action">
                <!--
                    示例： 
                    <template slot="action">
                        <div class="action-item">复制</div>
                        <div class="action-item">下载</div>
                    </template>
                -->
            </slot>
            <div class="action-item">
                <div on-click="toggleFullScreen">
                    <s-full width="16" s-if="!isFullScreen" />
                    <s-full-out width="16" s-else />
                </div>
            </div>
        </div>
        <div class="full-screen-container-content"><slot></slot></div>
    </div>
</template> `;

export default class FullScreen extends Component {
    static template = template;

    static components = {
        's-full': OutlinedFullScreen,
        's-full-out': OutlinedFullScreenOut,
    };

    static computed = {};

    initData() {
        return {
            isFullScreen: false,
        };
    }

    inited() {
        document.addEventListener('fullscreenchange', () => {
            if (!document.fullscreenElement) {
                this.data.set('isFullScreen', false);
            }
        });
    }

    toggleFullScreen() {
        const isFullScreen = this.data.get('isFullScreen');
        const containerRef = this.ref('full-screen-container');
        if (containerRef) {
            if (isFullScreen) {
                exitFullScreen();
            } else {
                requestFullScreen(containerRef);
            }
            this.data.set('isFullScreen', !isFullScreen);
        }
    }
}
