/**
 * @file pages/cluster/role/components/delete-dialog.js
 * <AUTHOR> Console
 * @description 删除确认弹框组件
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Dialog, Button, Table} from '@baidu/sui';
import './style.less';

const {invokeAppComp} = decorators;

const template = html`
    <template>
        <s-dialog
            class="delete-dialog"
            open="{=open=}"
            confirming="{=confirming=}"
            title="删除资源"
            on-confirm="onConfirm"
            on-close="onClose"
        >
            <div class="cce-deployment-delete">
                <div class="cce-deployment-warn-tip cce-tip-warn">
                    温馨提示：危险操作，请确认是否继续操作！
                </div>

                <div class="cce-deployment-delete-text">请确认是否要删除以下资源：</div>
                <s-table
                    class="deployment-delete-list"
                    columns="{{table.columns}}"
                    datasource="{{table.datasource}}"
                />
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" on-click="onConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

@invokeAppComp
export default class DeleteDialog extends Component {
    static template = template;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-table': Table,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            resourceName: '',
            createTime: '',
            namespace: '',
            resourceType: '', // 'role' 或 'clusterrole'
            table: {
                columns: [
                    {name: 'resourceName', label: '资源名称'},
                    {name: 'createTime', label: '创建时间'},
                ],
                datasource: [],
            },
        };
    }

    show(resourceName, createTime, namespace, resourceType = 'clusterrole') {
        this.data.set('open', true);
        this.data.set('resourceName', resourceName);
        this.data.set('createTime', createTime);
        this.data.set('namespace', namespace);
        this.data.set('resourceType', resourceType);

        // 根据资源类型动态设置表格结构
        let columns, datasource;

        if (resourceType === 'role' && namespace) {
            // Role 资源显示命名空间
            columns = [
                {name: 'resourceName', label: '资源名称'},
                {name: 'namespace', label: '命名空间'},
                {name: 'createTime', label: '创建时间'},
            ];
            datasource = [
                {
                    resourceName: resourceName,
                    namespace: namespace,
                    createTime: createTime,
                },
            ];
        } else {
            // ClusterRole 资源不显示命名空间
            columns = [
                {name: 'resourceName', label: '资源名称'},
                {name: 'createTime', label: '创建时间'},
            ];
            datasource = [
                {
                    resourceName: resourceName,
                    createTime: createTime,
                },
            ];
        }

        this.data.set('table.columns', columns);
        this.data.set('table.datasource', datasource);
    }

    onConfirm() {
        this.data.set('confirming', true);
        this.fire('confirm');
    }

    onClose() {
        this.data.set('open', false);
        this.data.set('confirming', false);
        this.fire('close');
    }
}
