.delete-dialog {
    /* deployment-delete */
    .cce-deployment-warn-tip {
        font-size: 12px;
        margin-bottom: 20px;
        height: 30px;
        line-height: 30px;
        background-color: #fcf7f1;
        padding: 0 20px;
    }

    .cce-deployment-delete-text {
        margin-top: 10px;
        font-size: 13px;
    }

    .deployment-delete-list {
        margin-top: 10px;
    }
}

// 角色管理页面样式，参考事件中心样式
.role-manage {
    min-width: 1160px;
    margin: 0 24px;

    .title {
        font-size: 16px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        padding: 24px 0 0;
        margin-bottom: 8px;
    }

    .role-placeholder {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 14px;
    }

    .s-pagination {
        display: flex;
        justify-content: flex-end;
        margin: 16px 0;
    }
}

// Role 列表样式，与 ClusterRole 保持一致
.role-list {
    // 参考事件中心的工具栏样式
    .mt10 {
        margin-top: 10px;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .float-right {
        float: right;

        .s-button {
            margin-left: 8px;
            padding: 0 7px;
        }

        .button-icon {
            display: inline-flex;
        }
    }

    // 过滤器工具栏样式
    .role-filter {
        height: 32px;
        margin: 10px 0;

        .create {
            float: left;
        }

        .name {
            float: right;
            margin-left: 8px;
        }

        .refresh {
            float: right;
            margin-left: 8px;
            width: 32px;

            .s-button {
                padding: 0 7px;
            }
        }
    }

    // 表格样式
    .role-list-table {
        margin-top: 16px;

        .s-table {
            .s-table-container {
                overflow-x: hidden;
            }

            .s-table-body {
                max-height: calc(100vh - 400px);
                overflow-y: auto;
                overflow-x: hidden;
            }
        }
    }

    .s-table {
        .system-role-tag {
            margin-left: 8px;
        }
    }

    .s-pagination {
        height: 32px;
        float: right;
        margin-top: 10px;
        text-align: right;
    }
}
