/**
 * @file pages/cluster/role/detail/cluster-role-info.js
 * <AUTHOR> Console
 * @description ClusterRole 详情信息组件
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Table} from '@baidu/sui';
import {AppLegend, AppDetailCell} from '@baidu/sui-biz';
import RoleRulesTable from '../components/role-rules-table';
import {utcToTime} from '../../../../utils/util';
import './style.less';

const template = html`
    <template>
        <s-legend label="基本信息" class="clusterrole-info">
            <!-- 第一行：资源名称、创建时间、UUID -->
            <s-app-detail-cell divide="3" datasource="{{baseDatasource}}">
                <template slot="c-name">{{item.value}}</template>
                <template slot="c-createTime">{{item.value | toTime}}</template>
                <template slot="c-uid">{{item.value}}</template>
            </s-app-detail-cell>

            <!-- 第二行：标签和注解 -->
            <s-app-detail-cell divide="1" datasource="{{extraDatasource}}">
                <template slot="c-labels">
                    <span s-if="item.value.length === 0">-</span>
                    <div class="app-detail-labels-flow" s-else>
                        <span class="service-label" s-for="d, index in item.value"
                            >{{d.name}}：{{d.value}}</span
                        >
                    </div>
                </template>
                <template slot="c-annotations">
                    <span s-if="item.value.length === 0">-</span>
                    <div class="app-detail-labels-flow" s-else>
                        <span class="service-label" s-for="d, index in item.value"
                            >{{d.name}}：{{d.value}}</span
                        >
                    </div>
                </template>
            </s-app-detail-cell>
        </s-legend>
        <s-legend label="规则">
            <role-rules-table rules="{{detail.rules}}" />
        </s-legend>
    </template>
`;

export default class extends Component {
    static template = template;

    static components = {
        's-button': Button,
        's-legend': AppLegend,
        's-app-detail-cell': AppDetailCell,
        's-table': Table,
        'role-rules-table': RoleRulesTable,
    };

    initData() {
        return {
            baseDatasource: [
                {
                    label: '资源名称：',
                    slot: 'name',
                    value: '',
                },
                {
                    label: '创建时间：',
                    slot: 'createTime',
                    value: '',
                },
                {
                    label: 'UUID：',
                    slot: 'uid',
                    value: '',
                },
            ],
            extraDatasource: [
                {key: 'labels', label: '标签：', slot: 'labels', value: []},
                {key: 'annotations', label: '注解：', slot: 'annotations', value: []},
            ],
        };
    }

    static filters = {
        toTime(value) {
            return utcToTime(value);
        },
        safeArray(value) {
            // 确保值是数组格式，用于安全地处理标签和注解
            if (!value) {
                return [];
            }
            if (Array.isArray(value)) {
                return value;
            }
            if (typeof value === 'object') {
                return Object.entries(value).map(([name, val]) => ({name, value: val}));
            }
            return [];
        },
    };

    attached() {
        // 检查是否已经有 detail 数据
        const detail = this.data.get('detail');
        if (detail && Object.keys(detail).length > 0) {
            this.loadDetailData(detail);
        }

        this.watch('detail', item => {
            this.loadDetailData(item);
        });
    }

    refreshData() {
        this.fire('refresh');
    }

    // 处理详情信息
    loadDetailData(value) {
        const detail = value || this.data.get('detail');

        if (!detail || Object.keys(detail).length === 0) {
            return;
        }

        // 处理基础数据源（第一行：资源名称、创建时间、UUID）
        const baseDatasource = [
            {
                label: '资源名称：',
                slot: 'name',
                value: detail.name || '',
            },
            {
                label: '创建时间：',
                slot: 'createTime',
                value: detail.createTime || '',
            },
            {
                label: 'UUID：',
                slot: 'uid',
                value: detail.uid || '',
            },
        ];

        // 处理额外数据源（标签和注解）
        const extraDatasource = [
            {key: 'labels', label: '标签：', slot: 'labels', value: detail.labels || []},
            {
                key: 'annotations',
                label: '注解：',
                slot: 'annotations',
                value: detail.annotations || [],
            },
        ];

        // 更新数据源
        this.data.set('baseDatasource', baseDatasource);
        this.data.set('extraDatasource', extraDatasource);
    }
}
