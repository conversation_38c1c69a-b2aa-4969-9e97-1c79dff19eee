/**
 * @file pages/cluster/role/detail/role-info.js
 * <AUTHOR> Console
 * @description Role 详情信息组件
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Table} from '@baidu/sui';
import {AppLegend, AppDetailCell} from '@baidu/sui-biz';
import RoleRulesTable from '../components/role-rules-table';
import {utcToTime} from '../../../../utils/util';
import './style.less';

const template = html`
    <template>
        <s-legend label="基本信息" class="role-info">
            <!-- 第一行：资源名称、命名空间、创建时间 -->
            <s-app-detail-cell divide="3" datasource="{{baseDatasource}}">
                <template slot="c-name">{{item.value}}</template>
                <template slot="c-namespace">{{item.value}}</template>
                <template slot="c-createTime">{{item.value | toTime}}</template>
            </s-app-detail-cell>

            <!-- 第二行：UUID -->
            <s-app-detail-cell divide="3" datasource="{{secondRowDatasource}}">
                <template slot="c-uid">{{item.value}}</template>
            </s-app-detail-cell>

            <!-- 第三行：标签 -->
            <s-app-detail-cell divide="1" datasource="{{labelsDatasource}}">
                <template slot="c-labels">
                    <span s-if="item.value.length === 0">-</span>
                    <div class="app-detail-labels-flow" s-else>
                        <span class="service-label" s-for="d, index in item.value"
                            >{{d.name}}：{{d.value}}</span
                        >
                    </div>
                </template>
            </s-app-detail-cell>

            <!-- 第四行：注解 -->
            <s-app-detail-cell divide="1" datasource="{{annotationsDatasource}}">
                <template slot="c-annotations">
                    <span s-if="item.value.length === 0">-</span>
                    <div class="app-detail-labels-flow" s-else>
                        <span class="service-label" s-for="d, index in item.value"
                            >{{d.name}}：{{d.value}}</span
                        >
                    </div>
                </template>
            </s-app-detail-cell>
        </s-legend>

        <!-- 规则 -->
        <s-legend label="规则" class="rules-info">
            <role-rules-table rules="{{detail.rules}}" />
        </s-legend>
    </template>
`;

export default class extends Component {
    static template = template;

    static components = {
        's-button': Button,
        's-legend': AppLegend,
        's-app-detail-cell': AppDetailCell,
        's-table': Table,
        'role-rules-table': RoleRulesTable,
    };

    initData() {
        return {
            baseDatasource: [
                {
                    label: '资源名称：',
                    slot: 'name',
                    value: '',
                },
                {
                    label: '命名空间：',
                    slot: 'namespace',
                    value: '',
                },
                {
                    label: '创建时间：',
                    slot: 'createTime',
                    value: '',
                },
            ],
            secondRowDatasource: [
                {
                    label: 'UUID：',
                    slot: 'uid',
                    value: '',
                },
            ],
            labelsDatasource: [{key: 'labels', label: '标签：', slot: 'labels', value: []}],
            annotationsDatasource: [
                {key: 'annotations', label: '注解：', slot: 'annotations', value: []},
            ],
        };
    }

    static filters = {
        toTime(value) {
            return utcToTime(value);
        },
    };

    static computed = {
        detail() {
            return this.data.get('detail') || {};
        },
    };

    attached() {
        this.watch('detail', value => {
            this.loadDetailData(value);
        });

        // 初始加载
        const detail = this.data.get('detail');
        if (detail && Object.keys(detail).length > 0) {
            this.loadDetailData(detail);
        }
    }

    // 处理详情信息
    loadDetailData(value) {
        const detail = value || this.data.get('detail');

        if (!detail || Object.keys(detail).length === 0) {
            return;
        }

        // 处理基础数据源（第一行：资源名称、命名空间、创建时间）
        const baseDatasource = [
            {
                label: '资源名称：',
                slot: 'name',
                value: detail.name || '',
            },
            {
                label: '命名空间：',
                slot: 'namespace',
                value: detail.namespace || '',
            },
            {
                label: '创建时间：',
                slot: 'createTime',
                value: detail.createTime || '',
            },
        ];

        // 处理第二行数据源（UUID）
        const secondRowDatasource = [
            {
                label: 'UUID：',
                slot: 'uid',
                value: detail.uid || '',
            },
        ];

        // 处理标签数据源 - 转换为与 ClusterRole 一致的格式
        const labelsData = this.formatLabelsData(detail.labels || []);
        const labelsDatasource = [
            {key: 'labels', label: '标签：', slot: 'labels', value: labelsData},
        ];

        // 处理注解数据源 - 转换为与 ClusterRole 一致的格式
        const annotationsData = this.formatLabelsData(detail.annotations || []);
        const annotationsDatasource = [
            {key: 'annotations', label: '注解：', slot: 'annotations', value: annotationsData},
        ];

        this.data.set('baseDatasource', baseDatasource);
        this.data.set('secondRowDatasource', secondRowDatasource);
        this.data.set('labelsDatasource', labelsDatasource);
        this.data.set('annotationsDatasource', annotationsDatasource);
    }

    // 格式化标签/注解数据，转换为与 ClusterRole 一致的格式
    formatLabelsData(data) {
        if (!data || !Array.isArray(data)) {
            return [];
        }

        // 将 {key, value} 格式转换为 {name, value} 格式
        return data.map(item => ({
            name: item.key || item.name || '',
            value: item.value || '',
        }));
    }
}
