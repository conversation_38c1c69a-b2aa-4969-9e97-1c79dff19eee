.clusterrole-detail-view {
    .app-page-title,
    .s-page-title {
        display: flex;
        align-items: center;

        > .s-button {
            margin-left: 8px;

            &:first-of-type {
                margin-left: auto;
            }
        }
    }

    .app-tab-page,
    .s-tab-page {
        padding: 0;
    }

    .app-legend {
        margin-bottom: 0;
    }

    .s-legend {
        .s-legend-highlight {
            margin-bottom: 16px;
        }
    }

    .s-tabpane-wrapper {
        position: relative;
        width: 100%;
    }

    .clusterrole-info {
        margin-bottom: 24px;
    }

    .s-tabs-line.s-tabs-vertical {
        .s-tabnav {
            margin: 0;
        }
        .s-tabpane {
            padding: 24px;
        }
    }

    .refresh-btn {
        float: right;
        padding: 0 7px;
    }

    .role-rules-table {
        .s-table {
            td {
                vertical-align: top;
                word-break: break-all;
            }
        }
    }
}
