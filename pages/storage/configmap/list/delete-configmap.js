/**
 * 删除配置项
 *
 * @file pages/storage/configmap/list/delete.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Table} from '@baiducloud/bce-ui/san';
import {html} from '@baiducloud/runtime';

/* eslint-disable */
const template = html`<div class="cce-configmap-delete">
    <div class="cce-configmap-warn-tip cce-tip-warn">温馨提示：危险操作，请确认是否继续操作！</div>

    <div class="cce-configmap-delete-text">请确认是否要删除以下的配置项：</div>
    <ui-table
        class="configmap-delete-list"
        schema="{{table.schema}}"
        cell-builder="{{table.cellRenderer}}"
        datasource="{{table.datasource}}"
    />
    <div></div>
</div>`;
/* eslint-enable */

export default class DeleteConfigmap extends Component {
    static template = template;

    static components = {
        'ui-table': Table,
    };

    initData() {
        return {
            table: {
                schema: [
                    {name: 'configmapName', label: '名称'},
                    {name: 'namespaceName', label: '命名空间'},
                    {name: 'createTime', label: '创建时间'},
                ],
                datasource: [],
                cellRenderer(item, key, col, rowIndex) {
                    const value = item[key];
                    switch (key) {
                        default:
                            return _.escape(value) || '-';
                    }
                },
            },
        };
    }

    attached() {
        const {clusterUuid, namespaceName, configmapName, createTime} = this.data.get();
        this.data.set('table.datasource', [
            {
                clusterUuid,
                namespaceName,
                configmapName,
                createTime,
            },
        ]);
    }

    doSubmit() {
        const {configmapName, namespaceName, clusterUuid, createTime} = this.data.get();
        return this.$http.deleteAppConfigMap({
            configmapName,
            namespaceName,
            clusterUuid,
            createTime,
        });
    }
}
