/**
 * @file Field
 * <AUTHOR>
 */
import _ from 'lodash';

export function getData(data, clusterUuid, clusterName = '') {
    if (data.success === true) {
        data = data.result;
        let d = {
            clusterUuid: clusterUuid,
            clusterName: clusterName,
            namespace: data.objectMeta.namespace,
            name: data.objectMeta.name,
            data: _.map(data.data, function (n, v) {
                return {
                    name: v,
                    value: n
                };
            })
        };
        return d;
    }
    return {};
}