/**
 * @file pages/storage/configmap/list/list.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {redirect, decorators} from '@baiducloud/runtime';
import {notification, Icon} from '@baiducloud/bce-ui/san';

import {waitActionDialog} from '../../../../common/biz/helper';
import listPage from '../../../../common/list/list-page';
import asTable from '../../../../common/list/table';
import {utcToTime} from '../../../../utils/util';

import './style.less';
import schema from './schema';
import DeleteConfigmap from './delete-configmap';
import {tableSlot} from './slot';

const {invokeBceSanUI} = decorators;

const Table = asTable(tableSlot, {
    'ui-icon': Icon,
});

@invokeBceSanUI
export default class ConfigMapList extends listPage(schema, Table) {
    listRequester(payload) {
        return this.listAppConfigMap(payload).then(result => {
            if (result.success) {
                const clusterUuid = this.data.get('clusterUuid');
                const clusterName = this.data.get('clusterName');
                let obj = this.getData(result, clusterUuid, clusterName, payload.pageNo);
                return obj.tableObj;
            }
            return Promise.resolve(() => {
                return {
                    pageSize: 20,
                    totalCount: 0,
                    result: [],
                };
            });
        });
    }
    listAppConfigMap(payload) {
        const {clusterUuid, namespace} = this.data.get();
        if (!clusterUuid || !namespace) {
            return Promise.reject();
        }
        if (payload.pageNo) {
            this.data.set('pageNo', payload.pageNo);
        }
        return this.$http.listAppConfigMap({
            ...payload,
            clusterUuid,
            namespaceName: namespace,
        });
    }

    getData(oData, clusterUuid, clusterName, pageNo = 1) {
        if (oData.success === true) {
            let data = oData.result;
            let tableObj = {
                totalCount: data.listMeta.totalItems,
                pageNo: pageNo,
                result: _.map(data.items, function (o) {
                    return {
                        clusterUuid: clusterUuid,
                        clusterName: clusterName,
                        namespaceName: o.objectMeta.namespace,
                        configmapName: o.objectMeta.name,
                        createTime: o.objectMeta.creationTimestamp,
                        createTimeFormat: utcToTime(o.objectMeta.creationTimestamp),
                    };
                }),
            };
            return {
                tableObj,
            };
        }
    }

    createConfigmap() {
        const {clusterUuid, namespace} = this.data.get();
        redirect(
            '#/cce/application/configmap/create?clusterUuid=' +
                clusterUuid +
                '&namespaceName=' +
                namespace,
        );
    }

    deleteConfigmap(item) {
        const dialogOptions = {
            title: '删除配置项',
        };

        const actionOptions = {
            configmapName: item.configmapName,
            namespaceName: item.namespaceName,
            clusterUuid: item.clusterUuid,
            createTime: item.createTimeFormat,
        };

        const dialog = waitActionDialog(DeleteConfigmap, dialogOptions, actionOptions);

        dialog.on('success', () => {
            notification.success('删除成功');
            this.data.set('pager.page', 1);
            this.data.set('selectedItems', []);
            this.refreshTable();
        });

        this.$childs.push(dialog);
    }

    onShowConfigmapPrivateInfo() {
        const isConfigmapPrivateHidden = this.data.get('isConfigmapPrivateHidden');
        this.data.set('isConfigmapPrivateHidden', !isConfigmapPrivateHidden);
    }

    attached() {
        super.attached();
        this.watch('clusterUuid', () => this.readyLoad());
        this.watch('namespace', () => this.readyLoad());
        this.watch('clusterName', () => this.readyLoad());
    }

    readyLoad() {
        const clusterUuid = this.data.get('clusterUuid');
        const clusterName = this.data.get('clusterName');
        const namespace = this.data.get('namespace');
        if (clusterUuid && clusterName && namespace) {
            this.data.set('pager.page', 1);
            this.refreshTable();
        }
    }
}
