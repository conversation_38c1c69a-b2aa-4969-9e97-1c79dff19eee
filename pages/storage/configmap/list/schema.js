/**
 * @file pages/storage/configmap/list/schema.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {redirect, html, ServiceFactory} from '@baiducloud/runtime';

import tips from '../../../../utils/tips';
import {utcToTime} from '../../../../utils/util';
import {drawerTpl} from './slot';
import {getData as getConfigMapInfoData} from './field';

const $flag = ServiceFactory.resolve('$flag');

export default {
    title: '配置项 <span class="sub-title">ConfigMap</span>',
    $pageClass: ['cce-configmap-list'],
    tip: drawerTpl,
    toolbar: [
        {
            type: 'button',
            label: '新建配置项',
            skin: 'create',
            actionType: 'custom',
            action() {
                this.createConfigmap();
            },
        },
    ],
    $helps: $flag.CceSupportXS
        ? []
        : [
              {
                  type: 'link',
                  icon: 'information',
                  label: '帮助文档',
                  link: tips.doc.configmap,
              },
          ],
    $withSearchbox: true,
    $withPager: true,
    $withRefresh: true,
    body: {
        api: 'listRequester',
        filter: {
            placeholder: '请输入配置项名称进行搜索',
            width: 200,
            $searchbox: {
                keywordType: 'configmapName',
            },
            keywordTypes: [{text: '配置项名称', value: 'configmapName'}],
        },
        $extraPayload: {
            manner: 'page',
        },
        columns: [
            {name: 'configmapName', label: '名称', width: '30%'},
            {name: 'namespaceName', label: '命名空间', width: '20%'},
            {name: 'createTime', label: '创建时间', width: '30%'},
            {name: 'action', label: '操作', width: '20%'},
        ],
        $cellRenderer(item, key, col, row) {
            switch (key) {
                case 'configmapName':
                    return (
                        '<a href="javascript:void(0)" data-command="DETAIL" ' +
                        `class="text-overflow-ellipsis" title="${item.configmapName}">${item.configmapName}</a>`
                    );
                case 'createTime':
                    return utcToTime(item[key]);
                case 'action':
                    return html`
                        <a href="javascript:void(0)" data-command="EDIT">修改</a>
                        <a href="javascript:void(0)" data-command="DELETE">删除</a>
                    `;
                default:
                    return _.escape(item[key]);
            }
        },
        $commands: {
            EDIT: {
                actionType: 'custom',
                action(item) {
                    redirect(
                        '#/cce/application/configmap/edit?clusterUuid=' +
                            item.clusterUuid +
                            '&clusterName=' +
                            item.clusterName +
                            '&namespaceName=' +
                            item.namespaceName +
                            '&name=' +
                            item.configmapName,
                    );
                },
            },
            DELETE: {
                actionType: 'custom',
                action(item) {
                    this.deleteConfigmap(item);
                },
            },
            DETAIL: {
                actionType: 'custom',
                action(obj) {
                    let options = {
                        clusterUuid: obj.clusterUuid,
                        namespaceName: obj.namespaceName,
                        configmapName: obj.configmapName,
                    };
                    this.$http.getAppConfigMapInfo(options).then(response => {
                        let configmap = getConfigMapInfoData(response, '', '');
                        setTimeout(() => {
                            this.data.set('detailPayload', obj);
                            this.data.set('detailPayloadData', configmap.data);
                            this.data.set('configmapDetailExpand', true);
                        }, 100);
                    });
                },
            },
        },
    },
};
