/**
 * @file pages/storage/configmap/list/slot.js
 * <AUTHOR>
 */
import {html} from '@baiducloud/runtime';

export const tableSlot = html`

`;

export const drawerTpl = html`
<ui-as-drawer 
    expand="{= configmapDetailExpand =}"
    expandTo="right"
    width="600px"
    mask="{{true}}"
    class="configmap-drawer"
>
    <div slot="header">
        <div class="text-overflow-ellipsis">
            <span>{{detailPayload.configmapName}}</span>
        </div>
    </div>
    <div class = "configmap-drawer-detail application-detail">
        <div class="detail-content content">
            <div class="detail-parts-table">
                <dl class="detail-part-1-col">
                    <dt><h4>基本信息</h4></dt>
                    <dd>
                        <div class="detail-row">
                            <div class="table info-table">
                                <div class="tr">
                                    <div class="td"> 
                                        <div class="name-info">
                                            <label>名称：</label>
                                        </div>
                                        <div class="text-overflow-ellipsis">
                                            <span>{{detailPayload.configmapName}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="tr">
                                    <div class="td"><label>命名空间：</label>
                                    <span>{{detailPayload.namespaceName}}</span></div>
                                </div>
                                <div class="tr">
                                    <div class="td"><label>创建时间：</label>
                                    <span>{{detailPayload.createTime | formatTime}} </span></div>
                                </div>
                            </table>
                        </div>
                    </dd>
                </dl>
                <dl class="detail-part-1-col">
                    <dt><h4>数据信息</h4>
                        <a class="eye-a" on-click="onShowConfigmapPrivateInfo" href="javascript:void(0)">
                        <i class="iconfont icon-eye{{isConfigmapPrivateHidden ? 'close' : ''}}"></i>
                        </a>
                    </dt>
                    <dd>
                        <div class="detail-row">
                            <div class="table info-table">
                                <div class="tr">
                                    <p s-for="x in detailPayloadData">
                                        <span class="configmap-data-name">{{x.name}}</span>：
                                        <span>{{x.value.length}}字节</span>
                                        <p class="{{isConfigmapPrivateHidden ? 'p-hidden' : ''}} secret-private-p">
                                            <pre>{{x.value}}</pre>
                                        </p>
                                        <br>
                                    </p>
                                </div>
                            </table>
                        </div>
                    </dd>
                </dl>
            </div>
        </div>
    </div>
</ui-as-drawer>
`;