
/* configmap-delete */
.cce-configmap-warn-tip {
    font-size: 12px;
    margin-bottom: 20px;
    height: 30px;
    line-height: 30px;
    background-color: #FCF7F1;
    padding: 0 20px;
}

.cce-configmap-delete-text {
    margin-top: 10px;
    font-size: 13px;
}

.configmap-drawer {
    .bui-drawer {
        top: 50px !important;

        .bui-drawer-body {
            padding-bottom: 74px;
        }

        .configmap-drawer-detail {
            .detail-content {
                background-color: #FFF;
                overflow: hidden;
            }
            .configmap-data-name {
                font-weight: bold;
            }
            .detail-parts-table {
                margin-right: 0 !important;
                .detail-part-1-col {
                    margin: 0 0 20px 0;
                }
                dt {
                    h4 {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 22px;
                        color: #151B26;
                        display: inline-block;
                        zoom: 1;
                        span {
                            vertical-align: middle;
                        }
                    }
                }
                dd {
                    display: table;
                    table-layout: fixed;
                    width: 100%;
                    margin: 20px 0 0 0;
                    line-height: 24px;
                    .detail-row {
                        display: table-row;
                        height: 24px;
                        line-height: 30px;
                        .name-info {
                            float: left;
                        }
                        label {
                            width: 100px;
                            color: #666;
                            display: inline-block;
                        }
                        .p-hidden {
                            display: none;
                        }
                        pre {
                            white-space: pre-wrap;
                            word-break: break-word;
                        }
                    }
                }
            }
        }
    }
}