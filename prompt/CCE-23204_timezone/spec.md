# CCE-23204 时区同步功能设计文档

## 需求概述

在百度云容器引擎（CCE）控制台的工作负载创建界面中，为所有工作负载类型（Deployment、StatefulSet、DaemonSet、Job、CronJob）添加时区同步功能。该功能通过一个复选框组件实现，允许用户选择是否启用"容器与所在节点使用相同时区"。

## 功能规格

### 1. UI 组件规格

#### 1.1 组件位置

-   **位置**: 在基本信息（info.js）表单中，注解（Annotation）组件的下方
-   **标签文本**: "时区同步："
-   **复选框文本**: "容器与所在节点使用相同时区"
-   **默认状态**: 未选中

#### 1.2 组件样式

-   使用现有的 `s-form-item` 和 `s-checkbox` 组件
-   保持与其他表单项一致的样式和间距
-   参考现有复选框组件的实现模式

### 2. 功能实现规格

#### 2.1 数据流设计

**表单数据结构**:

```javascript
formData: {
    // 现有字段...
    timezoneSync: false; // 新增字段，默认为 false
}
```

**最终 YAML 输出**:
当 `timezoneSync` 为 `true` 时，在工作负载的 Pod 模板中添加：

```yaml
spec:
    template:
        spec:
            containers:
                - name: container-name
                  volumeMounts:
                      - mountPath: /etc/localtime
                        name: host-timezone
                        readOnly: true
            volumes:
                - name: host-timezone
                  hostPath:
                      path: /etc/localtime
```

#### 2.2 实现策略

**方案选择**: 将时区同步作为特殊的系统级数据卷处理，而不是通过现有的数据卷配置界面。

**理由**:

1. 时区同步是系统级功能，用户不需要手动配置挂载路径和卷名
2. 避免与用户自定义数据卷冲突
3. 简化用户操作，只需勾选复选框即可

### 3. 技术实现方案

#### 3.1 文件修改清单

1. **components/cluster/workload/info.js**

    - 添加时区同步复选框组件
    - 更新表单数据结构
    - 更新表单验证和数据获取逻辑

2. **pages/workload/create/index.js**

    - 修改 `getWorkloadData` 方法，处理时区同步数据卷
    - 确保时区同步卷在所有容器中都被挂载

3. **components/cluster/workload/data-flavor.js**
    - 添加时区同步数据卷的生成逻辑
    - 确保时区同步卷不与用户自定义卷冲突

#### 3.2 详细实现步骤

**步骤 1: 修改 info.js 组件**

在 `components/cluster/workload/info.js` 中：

1. 在模板中添加时区同步复选框（在 k8s-annotation 组件后）
2. 在 `initData` 中添加 `timezoneSync: false`
3. 在 `getFormData` 方法中包含时区同步状态

**步骤 2: 修改工作负载创建逻辑**

在 `pages/workload/create/index.js` 中：

1. 修改 `getWorkloadData` 方法，检查时区同步状态
2. 当启用时区同步时，为每个容器添加时区卷挂载
3. 在 Pod 模板的 volumes 中添加时区卷定义

**步骤 3: 添加时区同步卷处理逻辑**

创建专门的时区同步卷处理函数：

```javascript
// 生成时区同步卷定义
function getTimezoneVolume() {
    return {
        name: 'host-timezone',
        hostPath: {
            path: '/etc/localtime',
        },
    };
}

// 生成时区同步卷挂载
function getTimezoneVolumeMount() {
    return {
        name: 'host-timezone',
        mountPath: '/etc/localtime',
        readOnly: true,
    };
}
```

#### 3.3 兼容性考虑

1. **向后兼容**: 新功能默认关闭，不影响现有工作负载
2. **跨工作负载类型**: 确保在所有五种工作负载类型中都能正常工作
3. **容器级别**: 时区同步应用于所有容器（包括初始化容器）

#### 3.4 错误处理

1. **卷名冲突**: 检查用户是否已定义名为 'host-timezone' 的卷
2. **挂载路径冲突**: 检查用户是否已在 '/etc/localtime' 路径挂载其他卷
3. **优雅降级**: 如果发生冲突，显示警告信息但不阻止创建

### 4. 测试计划

#### 4.1 功能测试

1. **基本功能测试**

    - 验证复选框在所有工作负载类型中正确显示
    - 验证选中/取消选中状态正确保存
    - 验证生成的 YAML 包含正确的卷配置

2. **集成测试**

    - 验证与现有数据卷功能的兼容性
    - 验证与标签、注解功能的兼容性
    - 验证表单验证逻辑正常工作

3. **边界测试**
    - 测试卷名冲突场景
    - 测试挂载路径冲突场景
    - 测试大量容器的场景

#### 4.2 UI 测试

1. **样式一致性**: 确保新组件与现有 UI 风格一致
2. **响应式布局**: 验证在不同屏幕尺寸下的显示效果
3. **交互体验**: 验证复选框的交互反馈

### 5. 部署和发布

#### 5.1 发布策略

1. **灰度发布**: 先在测试环境验证功能完整性
2. **功能开关**: 考虑添加功能开关，便于紧急回滚
3. **用户文档**: 更新用户手册，说明时区同步功能的使用方法

#### 5.2 监控指标

1. **使用率**: 统计时区同步功能的使用频率
2. **错误率**: 监控因时区同步导致的创建失败
3. **性能影响**: 监控功能对创建性能的影响

### 6. 风险评估

#### 6.1 技术风险

-   **低风险**: 功能相对独立，不影响核心创建流程
-   **卷冲突**: 通过预检查和用户提示降低风险
-   **性能影响**: 额外的卷挂载对性能影响微乎其微

#### 6.2 用户体验风险

-   **学习成本**: 功能简单直观，学习成本低
-   **误操作**: 默认关闭状态，避免意外启用

### 7. 后续优化

#### 7.1 功能增强

1. **时区选择**: 未来可考虑支持自定义时区选择
2. **批量操作**: 支持在批量创建时统一设置时区同步
3. **模板保存**: 在工作负载模板中保存时区同步设置

#### 7.2 性能优化

1. **缓存优化**: 对时区卷定义进行缓存
2. **懒加载**: 只在需要时生成时区相关配置

## 总结

本设计文档详细描述了在 CCE 控制台中实现时区同步功能的完整方案。该功能通过在基本信息表单中添加一个简单的复选框，为用户提供了便捷的时区同步配置选项。实现方案考虑了兼容性、可维护性和用户体验，确保功能的稳定性和易用性。
