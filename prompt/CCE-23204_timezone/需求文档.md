# 创建工作负载支持配置时区同步（包含全部工作负载类型）

## 需求说明

本需求希望在创建全部工作负载类型（Deployment、StatefulSet、DaemonSet、Job、CronJob）的基本信息界面中注解（Annotation）组件的下方添加一个"时区同步" checkbox 组件，用于选择是否启用"容器与所在节点使用相同时区"。

样式类似 ![image](../pic/jietu-1753166261258.jpg)

在勾选启用"容器与所在节点使用相同时区"后，为工作负载模版添加下面的挂载内容：

```
# Kubernetes Pod 示例
volumeMounts:
- mountPath: /etc/localtime
  name: host-timezone
  readOnly: true
volumes:
- name: host-timezone
  hostPath:
    path: /etc/localtime
```
