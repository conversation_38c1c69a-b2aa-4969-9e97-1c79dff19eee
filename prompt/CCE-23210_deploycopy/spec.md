# Deployment 复制创建功能详细设计

## 1. 需求概述

在 Deployment 类型工作负载列表页面的【更多】操作中新增【复制创建】操作，位置放在标签注解下方。点击后直接跳转到创建工作负载页面，复用原工作负载的容器配置信息。

### 1.1 功能范围

-   **适用范围**：仅 deployment 类型工作负载列表
-   **不适用**：statefulset 和 daemonset 类型列表
-   **复制内容**：
    -   基本信息：保持创建默认状态（不复用）
    -   容器配置：全部复用工作负载的配置信息
    -   高级设置：保持创建默认状态（不复用）

## 2. 技术方案

### 2.1 整体架构

```
列表页面 → 复制创建按钮 → 获取detail数据 → 跳转创建页面 → 预填充容器配置
```

### 2.2 核心组件分析

#### 2.2.1 当前列表页面结构

-   **文件位置**：`pages/workload/deployment2/index.js`
-   **更多操作菜单**：`moreOpt` 数组配置
-   **当前菜单项**：重启、编辑 YAML、标签注解、日志、APM 控制台、删除

#### 2.2.2 创建页面结构

-   **文件位置**：`pages/workload/create/index.js`
-   **路由**：`/cce/application/workload/create`
-   **核心组件**：`container-hybrid-config` 组件用于容器配置

#### 2.2.3 数据获取逻辑

-   **API**：`getAppDeploymentInfo` 获取基础信息
-   **YAML API**：`getAppYaml` 获取完整配置
-   **数据处理**：`getData` 函数处理 API 返回数据

## 3. 实现方案

### 3.1 列表页面修改

#### 3.1.1 更多操作菜单配置

**文件**：`pages/workload/deployment2/index.js`

在 `moreOpt` 数组中的标签注解项后添加复制创建选项：

```javascript
moreOpt: [
    {
        text: '重启',
        value: 'reloadWorkload',
    },
    {
        text: '编辑YAML',
        value: 'editYaml',
    },
    {
        text: '标签注解',
        value: 'labelAnnotation',
    },
    {
        text: '复制创建',  // 新增
        value: 'copyCreate',
    },
    {
        text: '日志',
        value: 'log',
    },
    // ... 其他选项
],
```

#### 3.1.2 复制创建方法实现

```javascript
// 复制创建方法
async copyCreate(row) {
    try {
        // 1. 获取deployment详细信息
        const result = await this.$http.getAppDeploymentInfo({
            clusterUuid: row.clusterUuid,
            namespaceName: row.namespaceName,
            deploymentName: row.name,
        });

        // 2. 获取YAML配置
        const yamlRes = await this.$http.getAppYaml('deployment', {
            clusterUuid: row.clusterUuid,
            namespaceName: row.namespaceName,
            name: row.name,
        });

        // 3. 处理数据
        const detail = getData(result, row.clusterUuid, row.clusterName, 'deployment');
        detail.yamlContent = yamlRes.result;

        // 4. 将数据存储到sessionStorage
        const copyData = {
            detail,
            sourceInfo: {
                name: row.name,
                namespace: row.namespaceName,
                clusterUuid: row.clusterUuid,
            }
        };
        sessionStorage.setItem('deployment_copy_data', JSON.stringify(copyData));

        // 5. 跳转到创建页面
        const {clusterUuid, namespaceName, clusterName} = row;
        redirect(`#/cce/application/workload/create?clusterUuid=${clusterUuid}&namespace=${namespaceName}&type=deployment&clusterName=${clusterName}&copy=true`);

    } catch (error) {
        Notification.error('获取工作负载信息失败');
    }
}
```

### 3.2 创建页面修改

#### 3.2.1 路由参数处理

**文件**：`pages/workload/create/index.js`

在 `attached` 方法中检测复制参数：

```javascript
attached() {
    const {clusterUuid, namespace, type, clusterName, copy} = this.data.get('route.query');

    // 现有逻辑...
    this.data.set('clusterUuid', clusterUuid);
    this.data.set('namespace', namespace !== 'all' ? namespace : 'default');
    this.data.set('workloadType', type);
    this.data.set('clusterName', clusterName);

    // 新增：处理复制逻辑
    if (copy === 'true') {
        this.data.set('isCopy', true);
        this.loadCopyData();
    }

    const backTo = `/cce/application/${type}/list?clusterUuid=${clusterUuid}&namespaceName=${namespace}`;
    this.data.set('backTo', backTo);
    this.getK8sVersion();
}
```

#### 3.2.2 复制数据加载方法

```javascript
// 加载复制数据
loadCopyData() {
    try {
        const copyDataStr = sessionStorage.getItem('deployment_copy_data');
        if (copyDataStr) {
            const copyData = JSON.parse(copyDataStr);
            this.data.set('copyDetail', copyData.detail);
            this.data.set('sourceInfo', copyData.sourceInfo);

            // 清除sessionStorage中的数据
            sessionStorage.removeItem('deployment_copy_data');

            // 设置页面标题提示
            this.data.set('pageTitle', `创建工作负载（复制自：${copyData.sourceInfo.name}）`);
        }
    } catch (error) {
        console.error('加载复制数据失败:', error);
    }
}
```

#### 3.2.3 容器配置组件数据传递

直接传递复制的 detail 数据给 `container-hybrid-config` 组件，组件会自动以编辑模式渲染：

```javascript
<container-hybrid-config
    class="mt20"
    detail="{{isCopy ? copyDetail : clusterInfo}}"
    clusterUuid="{{clusterUuid}}"
    workloadType="{{workloadType}}"
    s-ref="{{steps.datasource[1].ref}}"
/>
```

### 3.3 组件复用说明

**无需修改现有组件**：

-   `container-hybrid-config.js` 和 `container-config.js` 保持不变
-   当传入包含 `yamlContent` 的 `detail` 数据时，组件会自动进入编辑模式
-   组件内部的 `watch('detail')` 逻辑会自动处理数据变化
-   现有的 `editMode()` 方法会解析 YAML 内容并预填充容器配置

**工作原理**：

1. 创建页面传入复制的 `detail` 数据（包含 `yamlContent`）
2. `container-config` 组件检测到 `detail` 不为空，自动调用 `editMode()`
3. `editMode()` 解析 `yamlContent` 中的容器配置并填充到表单
4. 用户看到预填充的容器配置，可以直接提交或修改后提交

## 4. 数据流转

### 4.1 数据获取流程

```
1. 用户点击"复制创建"
   ↓
2. 调用getAppDeploymentInfo获取基础信息
   ↓
3. 调用getAppYaml获取YAML配置
   ↓
4. 通过getData处理数据格式
   ↓
5. 存储到sessionStorage
   ↓
6. 跳转到创建页面
```

### 4.2 创建页面数据处理

```
1. 检测copy=true参数
   ↓
2. 从sessionStorage读取复制数据
   ↓
3. 传递给container-hybrid-config组件
   ↓
4. 组件以copy模式初始化
   ↓
5. 预填充容器配置信息
```

## 5. 关键技术点

### 5.1 数据存储方案

-   **方案选择**：使用 sessionStorage 临时存储复制数据
-   **优势**：页面刷新后数据仍存在，关闭浏览器后自动清理
-   **数据结构**：

```javascript
{
    detail: {
        // 完整的detail数据，包含yamlContent
    },
    sourceInfo: {
        name: '源工作负载名称',
        namespace: '命名空间',
        clusterUuid: '集群ID'
    }
}
```

### 5.2 组件复用机制

-   **无需新增模式**：直接复用现有的编辑模式逻辑
-   **自动模式切换**：组件根据 `detail` 数据是否为空自动选择模式
-   **模式说明**：
    -   `detail` 为空或仅包含集群信息：create 模式（空白初始化）
    -   `detail` 包含 `yamlContent`：edit 模式（预填充配置）

### 5.3 容器配置复用

-   **复用范围**：容器镜像、资源配置、环境变量、挂载卷等
-   **不复用**：工作负载名称、副本数等基本信息
-   **实现方式**：直接传入完整的 `detail` 数据，让组件自动处理

## 6. 用户体验优化

### 6.1 页面标题提示

-   创建页面标题显示：`创建工作负载（复制自：原工作负载名称）`
-   明确告知用户当前是复制创建操作

### 6.2 错误处理

-   数据获取失败时显示错误提示
-   sessionStorage 数据异常时降级为普通创建模式

### 6.3 性能优化

-   使用 sessionStorage 避免 URL 参数过长
-   数据使用后立即清理，避免内存泄漏

## 7. 测试要点

### 7.1 功能测试

-   复制创建按钮仅在 deployment 列表显示
-   点击后正确跳转到创建页面
-   容器配置正确预填充
-   基本信息和高级设置保持默认状态

### 7.2 异常测试

-   网络异常时的错误处理
-   sessionStorage 数据异常处理
-   不同浏览器兼容性测试

### 7.3 边界测试

-   复杂容器配置的复制
-   大量环境变量和挂载卷的处理
-   特殊字符和中文名称处理

## 8. 实现细节补充

### 8.1 需要导入的依赖

**文件**：`pages/workload/deployment2/index.js`

```javascript
import {getData} from '../detail/field'; // 导入数据处理函数
import {redirect} from '@baiducloud/runtime'; // 导入路由跳转函数
import {Notification} from '@baidu/sui'; // 导入通知组件
```

### 8.2 菜单显示控制

确保复制创建选项仅在 deployment 类型显示，在现有的模块类型判断逻辑中：

```javascript
// 在 attached 或 inited 方法中
const moduleName = this.data.get('moduleName');
if (moduleName !== 'deployment') {
    // 移除复制创建选项
    const moreOpt = this.data.get('moreOpt');
    const filteredOpt = moreOpt.filter(item => item.value !== 'copyCreate');
    this.data.set('moreOpt', filteredOpt);
}
```

### 8.3 错误处理增强

```javascript
async copyCreate(row) {
    try {
        // 显示加载状态
        const loadingNotification = Notification.info('正在获取工作负载配置...', {
            duration: 0 // 不自动关闭
        });

        // API调用逻辑...

        // 关闭加载提示
        loadingNotification.close();

    } catch (error) {
        console.error('复制创建失败:', error);

        // 根据错误类型显示不同提示
        if (error.code === 'NETWORK_ERROR') {
            Notification.error('网络连接失败，请检查网络后重试');
        } else if (error.code === 'PERMISSION_DENIED') {
            Notification.error('权限不足，无法获取工作负载配置');
        } else {
            Notification.error('获取工作负载信息失败，请稍后重试');
        }
    }
}
```

### 8.4 数据清理机制

在创建页面添加数据清理逻辑：

```javascript
// 页面卸载时清理数据
detached() {
    // 清理可能残留的复制数据
    sessionStorage.removeItem('deployment_copy_data');
}

// 创建成功后清理数据
async onSubmit() {
    try {
        // 创建逻辑...
        await this.createWorklaod(content, namespace);

        // 清理复制数据
        sessionStorage.removeItem('deployment_copy_data');

        // 成功提示和跳转...
    } catch (error) {
        // 错误处理...
    }
}
```

## 9. 兼容性考虑

### 9.1 浏览器兼容性

-   **sessionStorage 支持**：IE8+ 及现代浏览器
-   **JSON 序列化**：使用原生 JSON.stringify/parse
-   **降级方案**：如果 sessionStorage 不可用，使用内存存储

### 9.2 数据格式兼容性

-   确保 getData 函数返回的数据格式与 container-config 组件期望的格式一致
-   处理可能的 API 数据结构变化

### 9.3 版本兼容性

-   新增功能不影响现有功能
-   向后兼容，不破坏现有的创建和编辑流程

## 10. 性能优化建议

### 10.1 数据获取优化

-   考虑缓存机制，避免重复获取相同数据
-   使用 Promise.all 并行获取基础信息和 YAML 配置

### 10.2 内存管理

-   及时清理 sessionStorage 数据
-   避免在组件中保存大量数据引用

### 10.3 用户体验优化

-   添加加载状态提示
-   考虑添加复制进度指示器
-   优化页面跳转动画

## 11. 后续扩展可能

### 11.1 功能扩展

-   支持批量复制创建
-   支持跨集群复制
-   支持复制时修改部分配置

### 11.2 其他工作负载类型

-   如果需求扩展，可以将复制功能扩展到 StatefulSet 和 DaemonSet
-   抽象通用的复制逻辑组件

### 11.3 模板化

-   将复制的配置保存为模板
-   支持从模板创建工作负载
