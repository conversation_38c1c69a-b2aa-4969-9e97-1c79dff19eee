# Deployment 复制创建功能测试指南

## 功能实现完成情况

✅ **已完成的功能**：
1. 在 deployment 列表的【更多】操作中添加了【复制创建】选项
2. 复制创建选项仅在 deployment 类型列表中显示
3. 点击复制创建后获取完整的 deployment 配置数据
4. 跳转到创建页面并预填充容器配置
5. 页面标题显示复制来源提示
6. 完善的错误处理和数据清理机制

## 测试步骤

### 1. 基本功能测试

#### 1.1 菜单显示测试
- **测试路径**：进入 deployment 列表页面
- **预期结果**：在【更多】下拉菜单中应该看到【复制创建】选项，位置在【标签注解】下方
- **验证点**：
  - deployment 列表中显示复制创建选项
  - statefulset 和 daemonset 列表中不显示复制创建选项

#### 1.2 复制创建流程测试
1. **选择源 deployment**：在 deployment 列表中选择一个现有的 deployment
2. **点击复制创建**：点击【更多】→【复制创建】
3. **验证加载提示**：应该显示"正在获取工作负载配置..."的提示
4. **验证页面跳转**：应该自动跳转到创建工作负载页面
5. **验证页面标题**：页面标题应该显示"创建工作负载（复制自：原deployment名称）"
6. **验证容器配置预填充**：
   - 容器配置页面应该预填充原 deployment 的容器信息
   - 包括镜像地址、资源配置、环境变量、挂载卷等
7. **验证基本信息**：基本信息页面应该保持默认状态（空白）
8. **验证高级设置**：高级设置页面应该保持默认状态

### 2. 数据验证测试

#### 2.1 容器配置复制验证
选择一个包含以下配置的 deployment 进行复制：
- 多个容器
- 自定义镜像
- 资源限制（CPU/内存）
- 环境变量
- 挂载卷
- 健康检查配置

**验证点**：所有容器配置都应该正确复制到创建页面

#### 2.2 数据隔离验证
- **基本信息**：工作负载名称、副本数等应该为空或默认值
- **高级设置**：调度策略、标签选择器等应该为默认状态
- **容器配置**：应该完整复制原配置

### 3. 异常情况测试

#### 3.1 网络异常测试
- 断网情况下点击复制创建
- **预期结果**：显示网络连接失败提示

#### 3.2 权限异常测试
- 使用无权限用户点击复制创建
- **预期结果**：显示权限不足提示

#### 3.3 数据异常测试
- 手动清除 sessionStorage 后刷新创建页面
- **预期结果**：降级为普通创建模式

### 4. 边界情况测试

#### 4.1 复杂配置测试
测试包含以下复杂配置的 deployment：
- 大量环境变量（>20个）
- 多个挂载卷
- 复杂的健康检查配置
- 特殊字符和中文名称

#### 4.2 浏览器兼容性测试
- Chrome、Firefox、Safari 等主流浏览器
- 验证 sessionStorage 功能正常

### 5. 用户体验测试

#### 5.1 加载状态测试
- 验证加载提示正常显示和关闭
- 验证页面跳转流畅

#### 5.2 错误提示测试
- 验证各种错误情况下的提示信息清晰明确

## 预期测试结果

### ✅ 成功标准
1. 复制创建选项仅在 deployment 列表显示
2. 点击后正确获取数据并跳转
3. 容器配置完整预填充
4. 基本信息和高级设置保持默认
5. 页面标题正确显示复制来源
6. 错误处理和数据清理正常工作

### ❌ 失败情况
1. 复制创建选项在其他工作负载类型中显示
2. 点击后无法获取数据或跳转失败
3. 容器配置预填充不完整或错误
4. 基本信息被错误复制
5. 页面标题显示错误
6. 数据清理不彻底导致内存泄漏

## 调试信息

### 关键数据结构
```javascript
// sessionStorage 中的数据结构
{
    detail: {
        // 完整的 detail 数据，包含 yamlContent
        clusterUuid: "xxx",
        namespace: "xxx", 
        yamlContent: { /* K8s YAML 对象 */ }
    },
    sourceInfo: {
        name: "源工作负载名称",
        namespace: "命名空间",
        clusterUuid: "集群ID"
    }
}
```

### 调试方法
1. **浏览器开发者工具**：查看 Network 面板的 API 调用
2. **Console 日志**：查看错误日志和调试信息
3. **Application 面板**：查看 sessionStorage 中的数据
4. **Vue DevTools**：查看组件数据状态

## 已知限制

1. 仅支持 deployment 类型工作负载
2. 不支持跨集群复制
3. 不支持批量复制
4. 复制后需要手动修改工作负载名称

## 后续优化建议

1. 添加复制进度指示器
2. 支持复制时预设工作负载名称
3. 添加复制配置的预览功能
4. 支持选择性复制部分配置
