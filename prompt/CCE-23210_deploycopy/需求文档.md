# Deployment 新增复制操作

## 需求说明

在 Deployment 类型工作负载列表页面中的【更多】操作中新增【复制创建】操作（位置放在下拉框中标签注解下方）

-   复制创建：单击直接进入创建工作负载页面
    -   复制的内容：
        -   基本信息：保持创建默认状态，不复用工作负载的配置信息
        -   容器配置：全部复用工作负载的配置信息，包含全部配置参数
        -   高级设置：保持创建默认状态，不复用工作负载的配置信息

## 细节要求说明

-   只有在 deployment 类型的列表中才会显示复制创建选项，statefulset 和 daemonset 类型的列表中不会显示复制创建选项
-   复制创建后，会直接自动跳转到创建工作负载页面，不需要用户进行确认
-   复制创建时容器配置中的内容要根据工作负载数据进行渲染，渲染逻辑要和工作负载"详情"的"更新升级"中，"容器配置" 对已有容器的渲染逻辑保持完全一致，从已有数据中获取

## 代码现状说明

-   当前 deployment、statefulset、daemonset 三个类型的列表页面，都是使用的 `pages/workload/deployment2/index.js` 这个文件
-   工作负载"详情"的"更新升级"中，"容器配置" 对已有容器的渲染逻辑在 `components/cluster/workload/container-config.js` 文件

## 实现说明

-   尽可能复用已有函数，不编写重复逻辑
