# Job 工作负载并行数调整功能详细设计文档

## 1. 功能概述

### 1.1 需求背景

在 Kubernetes Job 工作负载管理中，用户需要能够动态调整 Job 的并行度（parallelism），即同时运行的 Pod 数量，以适应不同的业务场景和资源需求。

### 1.2 功能目标

-   在 Job 工作负载列表页面的"更多"操作菜单中新增"调整并行数"功能
-   提供直观的配置界面，允许用户修改 Job 的并行运行 Pod 数
-   确保操作的安全性和用户体验的一致性

## 2. 技术架构分析

### 2.1 现有代码结构分析

基于对现有代码的分析，Job 工作负载相关的核心文件包括：

#### 2.1.1 列表页面组件

-   **主文件**: `pages/workload/job/list/index.js`
-   **功能**: Job 工作负载列表展示和操作管理
-   **关键组件**:
    -   列表渲染和数据获取
    -   "更多"操作下拉菜单 (`moreOpt` 配置)
    -   操作事件处理 (`onMoreOptChange` 方法)

#### 2.1.2 现有操作菜单配置

```javascript
moreOpt: [
    {
        text: '标签注解',
        value: 'labelAnnotation',
    },
    {
        text: '日志',
        value: 'log',
    },
    {
        text: '删除',
        value: 'deleteWorkload',
    },
];
```

#### 2.1.3 Job 配置组件

-   **文件**: `components/cluster/workload/job-config.js`
-   **功能**: Job 创建时的配置界面，包含并行度设置
-   **关键配置**:
    -   `parallelism`: 并行运行 Pod 数配置
    -   输入验证和数据处理逻辑

#### 2.1.4 API 接口

-   **HTTP 客户端**: `utils/client.js`
-   **现有接口**:
    -   `listAppJob()`: 获取 Job 列表
    -   `deleteAppJob()`: 删除 Job
    -   `getAppJobInfo()`: 获取 Job 详情

### 2.2 数据结构分析

#### 2.2.1 Job 数据结构

基于 `common/yaml/job.js` 和相关组件分析，Job 的核心配置结构：

```yaml
apiVersion: batch/v1
kind: Job
metadata:
    name: job-example
spec:
    completions: 1 # 成功运行 Pod 数
    parallelism: 1 # 并行运行 Pod 数 (目标字段)
    backoffLimit: 6 # 失败重试次数
    template:
        # Pod 模板配置
```

#### 2.2.2 列表数据结构

Job 列表项包含的关键字段：

-   `jobName`: Job 名称
-   `namespaceName`: 命名空间
-   `clusterUuid`: 集群 UUID
-   `parallelism`: 当前并行度配置

## 3. 详细设计方案

### 3.1 UI 设计

#### 3.1.1 菜单项添加

在 `pages/workload/job/list/index.js` 的 `moreOpt` 配置中添加新选项：

```javascript
moreOpt: [
    {
        text: '调整并行数',
        value: 'adjustParallelism',
    },
    {
        text: '标签注解',
        value: 'labelAnnotation',
    },
    {
        text: '日志',
        value: 'log',
    },
    {
        text: '删除',
        value: 'deleteWorkload',
    },
];
```

#### 3.1.2 对话框组件设计

创建新的对话框组件 `pages/workload/job/list/adjust-parallelism.js`：

**组件特性**:

-   继承现有对话框模式，保持 UI 一致性
-   使用 `InputNumber` 组件进行数值输入
-   提供输入验证（正整数，最小值为 0）
-   显示当前并行度作为默认值

**模板结构**:

```html
<div class="cce-job-adjust-parallelism">
    <div class="form-content">
        <s-form-item label="并行运行 Pod 数：">
            <s-input-number min="0" value="{=parallelism=}" placeholder="请输入并行运行 Pod 数" />
            <span class="unit">个</span>
        </s-form-item>
        <div class="tip">当前配置：{{currentParallelism}} 个</div>
    </div>
</div>
```

### 3.2 后端接口设计

#### 3.2.1 新增 API 接口

在 `utils/client.js` 中添加调整并行度的接口：

```javascript
/**
 * 调整 Job 并行度
 * @param {Object} params - 参数对象
 * @param {string} params.clusterUuid - 集群 UUID
 * @param {string} params.namespaceName - 命名空间名称
 * @param {string} params.jobName - Job 名称
 * @param {number} params.parallelism - 新的并行度值
 * @returns {Promise} API 响应
 */
adjustJobParallelism(params = {}) {
    const url = `${CCEAPPURL}/job/${params.namespaceName}/${params.jobName}/parallelism`;
    return this.put(url, {
        parallelism: params.parallelism
    }, {
        clusterUuid: params.clusterUuid
    });
}
```

#### 3.2.2 接口规范

-   **请求方法**: PUT
-   **请求路径**: `/api/cce/app/v1/job/{namespace}/{jobName}/parallelism`
-   **请求参数**:
    -   Query: `clusterUuid`
    -   Body: `{ "parallelism": number }`
-   **响应格式**: 标准 API 响应格式

### 3.3 组件实现

#### 3.3.1 对话框组件实现

```javascript
// pages/workload/job/list/adjust-parallelism.js
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, FormItem, InputNumber} from '@baidu/sui';

const template = html`
    <div class="cce-job-adjust-parallelism">
        <div class="form-content">
            <s-form s-ref="form">
                <s-form-item
                    label="并行运行 Pod 数："
                    prop="parallelism"
                    rules="{{rules.parallelism}}"
                >
                    <s-input-number
                        min="0"
                        value="{=formData.parallelism=}"
                        placeholder="请输入并行运行 Pod 数"
                    />
                    <span class="unit">个</span>
                </s-form-item>
            </s-form>
            <div class="current-config-tip">
                <span class="tip-label">当前配置：</span>
                <span class="tip-value">{{currentParallelism}} 个</span>
            </div>
        </div>
    </div>
`;

export default class AdjustParallelism extends Component {
    static template = template;

    static components = {
        's-form': Form,
        's-form-item': FormItem,
        's-input-number': InputNumber,
    };

    initData() {
        return {
            formData: {
                parallelism: 1,
            },
            currentParallelism: 1,
            rules: {
                parallelism: [
                    {
                        required: true,
                        message: '请输入并行运行 Pod 数',
                    },
                    {
                        type: 'number',
                        min: 0,
                        message: '并行数必须大于等于 0',
                    },
                ],
            },
        };
    }

    attached() {
        const {parallelism} = this.data.get();
        this.data.set('formData.parallelism', parallelism);
        this.data.set('currentParallelism', parallelism);
    }

    async doSubmit() {
        const form = this.ref('form');
        await form.validateForm();

        const {clusterUuid, namespaceName, jobName} = this.data.get();

        const {parallelism} = this.data.get('formData');

        return this.$http.adjustJobParallelism({
            clusterUuid,
            namespaceName,
            jobName,
            parallelism,
        });
    }
}
```

#### 3.3.2 列表页面集成

在 `pages/workload/job/list/index.js` 中添加处理方法：

```javascript
// 导入新组件
import AdjustParallelism from './adjust-parallelism';

// 在类中添加处理方法
adjustParallelism(item) {
    const dialogOptions = {
        title: '调整并行数',
        width: 500,
        confirmText: '确认调整'
    };

    const actionOptions = {
        clusterUuid: item.clusterUuid,
        namespaceName: item.namespaceName,
        jobName: item.jobName,
        parallelism: item.parallelism || 1
    };

    const dialog = waitActionDialog(AdjustParallelism, dialogOptions, actionOptions);

    dialog.on('success', () => {
        Notification.success('并行数调整成功');
        this.refreshTable();
    });

    this.$childs.push(dialog);
}
```

### 3.4 样式设计

#### 3.4.1 对话框样式

```less
// pages/workload/job/list/adjust-parallelism.less
.cce-job-adjust-parallelism {
    .form-content {
        padding: 20px 0;

        .sui-form-item {
            margin-bottom: 20px;

            .unit {
                margin-left: 8px;
                color: #666;
                font-size: 14px;
            }
        }

        .current-config-tip {
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-size: 14px;

            .tip-label {
                color: #666;
            }

            .tip-value {
                color: #333;
                font-weight: 500;
            }
        }
    }
}
```

## 4. 实现步骤

### 4.1 第一阶段：基础组件开发

1. **创建对话框组件** (`adjust-parallelism.js`)

    - 实现基础 UI 结构
    - 添加表单验证逻辑
    - 实现数据提交方法

2. **添加样式文件** (`adjust-parallelism.less`)
    - 定义对话框样式
    - 确保与现有 UI 风格一致

### 4.2 第二阶段：API 接口开发

1. **扩展 HTTP 客户端**

    - 在 `utils/client.js` 中添加 `adjustJobParallelism` 方法
    - 定义请求参数和响应处理

2. **后端接口对接**
    - 确认后端 API 接口规范
    - 测试接口连通性和数据格式

### 4.3 第三阶段：列表页面集成

1. **修改菜单配置**

    - 更新 `moreOpt` 数组，添加新选项
    - 确保选项位置在首位

2. **添加事件处理**

    - 实现 `adjustParallelism` 方法
    - 集成对话框组件和事件监听

3. **导入依赖**
    - 添加新组件的导入语句
    - 确保组件正确注册

### 4.4 第四阶段：测试和优化

1. **功能测试**

    - 测试对话框打开和关闭
    - 验证表单验证逻辑
    - 测试 API 调用和响应处理

2. **用户体验优化**
    - 优化加载状态显示
    - 完善错误处理和提示
    - 确保操作反馈及时准确

## 5. 技术考虑

### 5.1 兼容性考虑

-   **浏览器兼容性**: 确保在主流浏览器中正常工作
-   **版本兼容性**: 与现有 Kubernetes Job API 版本兼容
-   **UI 组件兼容性**: 使用现有 UI 组件库，保持风格一致

### 5.2 安全性考虑

-   **权限验证**: 确保用户有修改 Job 配置的权限
-   **输入验证**: 前端和后端双重验证输入参数
-   **操作确认**: 提供操作确认机制，防止误操作

### 5.3 性能考虑

-   **异步操作**: 使用异步 API 调用，避免阻塞 UI
-   **状态管理**: 及时更新列表状态，反映最新配置
-   **错误处理**: 完善的错误处理和用户提示机制

### 5.4 可维护性考虑

-   **代码复用**: 复用现有组件和工具方法
-   **模块化设计**: 保持组件独立性和可测试性
-   **文档完善**: 提供清晰的代码注释和使用说明

## 6. 测试计划

### 6.1 单元测试

-   对话框组件的渲染和交互测试
-   表单验证逻辑测试
-   API 调用方法测试

### 6.2 集成测试

-   列表页面与对话框的集成测试
-   API 接口的端到端测试
-   用户操作流程测试

### 6.3 用户验收测试

-   UI 交互体验测试
-   功能完整性验证
-   错误场景处理测试

## 7. 风险评估

### 7.1 技术风险

-   **API 接口变更**: 后端接口可能需要调整
-   **组件依赖**: 依赖的 UI 组件可能存在兼容性问题
-   **数据一致性**: 并行度调整后的状态同步问题

### 7.2 业务风险

-   **用户误操作**: 错误的并行度设置可能影响业务
-   **资源消耗**: 过高的并行度可能导致资源不足
-   **任务执行**: 并行度调整可能影响正在运行的任务

### 7.3 缓解措施

-   **充分测试**: 进行全面的功能和性能测试
-   **用户培训**: 提供清晰的使用说明和最佳实践
-   **监控告警**: 实施资源监控和异常告警机制
-   **回滚机制**: 提供配置回滚和恢复功能

## 8. 上线计划

### 8.1 开发阶段

-   **Week 1**: 基础组件开发和样式设计
-   **Week 2**: API 接口开发和集成测试
-   **Week 3**: 功能测试和 Bug 修复
-   **Week 4**: 用户验收测试和文档完善

### 8.2 部署阶段

-   **灰度发布**: 先在测试环境验证功能
-   **小范围试用**: 选择部分用户进行试用
-   **全量发布**: 确认无问题后全量上线

### 8.3 监控和维护

-   **功能监控**: 监控新功能的使用情况和性能表现
-   **用户反馈**: 收集用户使用反馈，持续优化
-   **问题处理**: 及时响应和处理用户报告的问题

## 9. 总结

本设计文档详细规划了 Job 工作负载并行数调整功能的实现方案，包括 UI 设计、后端接口、组件实现、测试计划等各个方面。通过系统性的设计和实现，将为用户提供便捷、安全、可靠的 Job 并行度调整功能，提升 Kubernetes Job 工作负载的管理效率和用户体验。
