# ClusterRole 管理功能详细设计

## 1. 概述

本文档描述了 CCE 控制台中 ClusterRole 管理功能的详细技术设计，包括页面结构、组件设计、API 接口、数据流转等方面的实现细节。

## 2. 功能架构

### 2.1 页面结构

```
pages/cluster/role/
├── index.js                 # 角色管理主入口页面
├── list/
│   ├── index.js             # ClusterRole 列表页面
│   ├── cluster-role-list.js # ClusterRole 列表组件
│   └── style.less           # 列表页面样式
├── detail/
│   ├── index.js             # ClusterRole 详情页面入口
│   ├── detail.js            # ClusterRole 详情组件
│   └── style.less           # 详情页面样式
└── components/
    ├── yaml-dialog.js       # YAML 编辑/查看弹框组件
    ├── delete-dialog.js     # 删除确认弹框组件
    └── role-rules-table.js  # 规则列表表格组件
```

### 2.2 导航结构

-   位置：集群详情 > 安全管理 > 角色（新增）
-   路由：`#/cce/cluster/role?clusterUuid={clusterUuid}`
-   Tab 页：ClusterRole（默认）、Role（暂不实现）

## 3. 页面设计

### 3.1 角色管理主页面 (pages/cluster/role/index.js)

#### 3.1.1 页面模板

```javascript
const template = html`
    <template>
        <div class="cce-role-manage">
            <div class="role-tabs">
                <s-tab active="{=activeTab=}" on-change="onTabChange">
                    <s-tab-panel label="ClusterRole" name="clusterrole">
                        <cluster-role-list
                            cluster-uuid="{{clusterUuid}}"
                            cluster-name="{{clusterName}}"
                        />
                    </s-tab-panel>
                    <s-tab-panel label="Role" name="role" disabled>
                        <!-- Role 功能暂不实现 -->
                    </s-tab-panel>
                </s-tab>
            </div>
        </div>
    </template>
`;
```

#### 3.1.2 组件实现

```javascript
import {Component} from 'san';
import {Tab} from '@baidu/sui';
import ClusterRoleList from './list/cluster-role-list';

export default class RoleManage extends Component {
    static template = template;
    static components = {
        's-tab': Tab,
        's-tab-panel': Tab.Panel,
        'cluster-role-list': ClusterRoleList,
    };

    initData() {
        return {
            activeTab: 'clusterrole',
        };
    }

    onTabChange({value}) {
        this.data.set('activeTab', value);
    }
}
```

### 3.2 ClusterRole 列表页面 (pages/cluster/role/list/cluster-role-list.js)

#### 3.2.1 页面功能

-   列表展示：名称、创建时间、操作列
-   搜索功能：按名称模糊搜索
-   分页功能：支持分页展示
-   创建功能：YAML 方式创建
-   编辑功能：YAML 方式编辑（普通角色）
-   查看功能：YAML 方式查看（系统默认角色）
-   删除功能：删除确认（普通角色）
-   刷新功能：重新加载列表数据

#### 3.2.2 页面模板

```javascript
const template = html`
    <template>
        <div class="cluster-role-list">
            <!-- 操作栏 -->
            <div class="operation-bar">
                <s-button skin="primary" on-click="onCreate">创建</s-button>
                <div class="operation-right">
                    <s-button class="refresh-btn" on-click="onRefresh">
                        <s-icon-refresh />
                    </s-button>
                    <s-search
                        value="{=searchValue=}"
                        on-search="onSearch"
                        placeholder="请输入资源名称搜索"
                        width="200"
                    />
                </div>
            </div>

            <!-- 列表表格 -->
            <s-table columns="{{columns}}" datasource="{{datasource}}" loading="{{loading}}">
                <!-- 名称列 -->
                <div slot="c-name">
                    <a href="{{getDetailUrl(row)}}" target="_blank">{{row.metadata.name}}</a>
                    <s-tag s-if="isSystemRole(row)" size="small" skin="info">系统默认角色</s-tag>
                </div>

                <!-- 创建时间列 -->
                <div slot="c-createTime">{{row.metadata.creationTimestamp | formatTime}}</div>

                <!-- 操作列 -->
                <div slot="c-action">
                    <s-button s-if="isSystemRole(row)" skin="stringfy" on-click="onViewYaml(row)">
                        查看 YAML
                    </s-button>
                    <s-button s-else skin="stringfy" on-click="onEditYaml(row)">
                        编辑 YAML
                    </s-button>

                    <s-tooltip s-if="isSystemRole(row)" content="系统默认角色不支持删除">
                        <s-button skin="stringfy" disabled>删除</s-button>
                    </s-tooltip>
                    <s-button s-else skin="stringfy" on-click="onDelete(row)"> 删除 </s-button>
                </div>
            </s-table>

            <!-- 分页 -->
            <s-pagination
                s-if="totalCount > 0"
                total="{{totalCount}}"
                pageSize="{{pageSize}}"
                pageSizes="{{[20, 50, 100]}}"
                layout="pageSize,pager,go"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />

            <!-- YAML 弹框 -->
            <yaml-dialog
                open="{=yamlDialogOpen=}"
                mode="{{yamlDialogMode}}"
                title="{{yamlDialogTitle}}"
                content="{=yamlContent=}"
                on-confirm="onYamlConfirm"
                on-close="onYamlClose"
            />

            <!-- 删除确认弹框 -->
            <delete-dialog
                open="{=deleteDialogOpen=}"
                resource-name="{{deleteResourceName}}"
                create-time="{{deleteCreateTime}}"
                on-confirm="onDeleteConfirm"
                on-close="onDeleteClose"
            />
        </div>
    </template>
`;
```

#### 3.2.3 组件实现

```javascript
import {Component} from 'san';
import {Button, Table, Pagination, Search, Tag, Tooltip, Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import YamlDialog from '../components/yaml-dialog';
import DeleteDialog from '../components/delete-dialog';
import {utcToTime} from '../../../../../utils/util';
import jsyaml from 'js-yaml';

export default class ClusterRoleList extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        'yaml-dialog': YamlDialog,
        'delete-dialog': DeleteDialog,
    };

    static filters = {
        formatTime: utcToTime,
    };

    initData() {
        return {
            // 列表数据
            datasource: [],
            loading: false,
            totalCount: 0,
            pageSize: 20,
            pageNo: 1,
            searchValue: '',

            // 表格列配置
            columns: [
                {title: '名称', field: 'name', slot: 'name'},
                {title: '创建时间', field: 'createTime', slot: 'createTime'},
                {title: '操作', field: 'action', slot: 'action', width: 200},
            ],

            // 弹框状态
            yamlDialogOpen: false,
            yamlDialogMode: 'create', // create | edit | view
            yamlDialogTitle: '',
            yamlContent: '',
            deleteDialogOpen: false,
            deleteResourceName: '',
            deleteCreateTime: '',
            currentEditRow: null,

            // 系统默认角色列表
            systemRoles: ['cce:admin'],
        };
    }

    attached() {
        this.loadData();
    }

    // 加载列表数据
    async loadData() {
        try {
            this.data.set('loading', true);
            const {clusterUuid, pageNo, pageSize, searchValue} = this.data.get();

            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: 'ClusterRole',
                namespace: '', // ClusterRole 不需要 namespace
                clusterUuid,
                pageNo,
                pageSize,
                name: searchValue?.trim(),
            };

            const response = await this.$http.getResourceObjList(params);
            const items = (response?.result?.items || []).map(item => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    return {};
                }
            });

            this.data.set('datasource', items);
            this.data.set('totalCount', response?.result?.listMeta?.totalItems || 0);
        } catch (error) {
            console.error('加载 ClusterRole 列表失败:', error);
            Notification.error('加载列表失败');
        } finally {
            this.data.set('loading', false);
        }
    }

    // 判断是否为系统默认角色
    isSystemRole(row) {
        const systemRoles = this.data.get('systemRoles');
        return systemRoles.includes(row.metadata?.name);
    }

    // 获取详情页 URL
    getDetailUrl(row) {
        const {clusterUuid, clusterName} = this.data.get();
        return `#/cce/cluster/role/detail?clusterUuid=${clusterUuid}&clusterName=${clusterName}&name=${row.metadata?.name}`;
    }

    // 搜索
    onSearch() {
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 刷新
    onRefresh() {
        this.loadData();
    }

    // 分页变化
    onPageChange({value}) {
        this.data.set('pageNo', value.page);
        this.loadData();
    }

    onPageSizeChange({value}) {
        this.data.set('pageSize', value.pageSize);
        this.data.set('pageNo', 1);
        this.loadData();
    }

    // 创建
    onCreate() {
        const defaultYaml = `apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterRole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]`;

        this.data.set('yamlDialogOpen', true);
        this.data.set('yamlDialogMode', 'create');
        this.data.set('yamlDialogTitle', 'YAML 创建资源');
        this.data.set('yamlContent', defaultYaml);
        this.data.set('currentEditRow', null);
    }

    // 编辑 YAML
    onEditYaml(row) {
        try {
            const yamlContent = jsyaml.safeDump(row);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'edit');
            this.data.set('yamlDialogTitle', '编辑 YAML');
            this.data.set('yamlContent', yamlContent);
            this.data.set('currentEditRow', row);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // 查看 YAML
    onViewYaml(row) {
        try {
            const yamlContent = jsyaml.safeDump(row);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'view');
            this.data.set('yamlDialogTitle', '查看 YAML');
            this.data.set('yamlContent', yamlContent);
            this.data.set('currentEditRow', row);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // 删除
    onDelete(row) {
        this.data.set('deleteDialogOpen', true);
        this.data.set('deleteResourceName', row.metadata?.name);
        this.data.set('deleteCreateTime', utcToTime(row.metadata?.creationTimestamp));
        this.data.set('currentEditRow', row);
    }

    // YAML 弹框确认
    async onYamlConfirm() {
        try {
            const {yamlContent, yamlDialogMode, clusterUuid, currentEditRow} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);

            if (yamlDialogMode === 'create') {
                await this.$http.createResource({
                    clusterUuid,
                    content: yamlContent,
                    namespace: yamlData?.metadata?.namespace || 'default',
                    validate: true,
                });
                Notification.success('创建成功');
            } else if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: 'ClusterRole',
                    name: currentEditRow.metadata?.name,
                    clusterUuid,
                };
                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
            }

            this.data.set('yamlDialogOpen', false);
            this.loadData();
        } catch (error) {
            console.error('操作失败:', error);
            Notification.error(error.message || '操作失败');
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
        this.data.set('currentEditRow', null);
    }

    // 删除确认
    async onDeleteConfirm() {
        try {
            const {clusterUuid, currentEditRow} = this.data.get();
            const params = {
                kind: 'ClusterRole',
                clusterUuid,
                resourceList: [
                    {
                        kind: 'ClusterRole',
                        group: 'rbac.authorization.k8s.io',
                        version: 'v1',
                        name: currentEditRow.metadata?.name,
                    },
                ],
                method: 'delete',
            };

            await this.$http.deleteResource(params);
            this.data.set('deleteDialogOpen', false);
            this.loadData();
            Notification.success('删除成功');
        } catch (error) {
            console.error('删除失败:', error);
            Notification.error(error.message || '删除失败');
        }
    }

    // 删除弹框关闭
    onDeleteClose() {
        this.data.set('deleteDialogOpen', false);
        this.data.set('currentEditRow', null);
    }
}
```

### 3.3 ClusterRole 详情页面 (pages/cluster/role/detail/index.js)

#### 3.3.1 页面入口

```javascript
import {decorators, DetailPage, html} from '@baiducloud/runtime';
import ClusterRoleDetail from './detail';

const {asPage, invokeAppComp, invokeBceSanUI, invokeComp} = decorators;

@asPage('/cce/cluster/role/detail')
@invokeAppComp
@invokeBceSanUI
@invokeComp('@cce-page-title')
class ClusterRoleDetailPage extends DetailPage {
    static pageName = 'cce-cluster-role-detail';
    REGION_CHANGE_LOCATION = '#/cce/cluster/list';

    static template = html`
        <app-detail-page>
            <div slot="pageTitle" class="app-page-title">
                <cce-page-title title-data="{=titleData=}" />
            </div>
            <cluster-role-detail
                cluster-uuid="{{clusterUuid}}"
                cluster-name="{{clusterName}}"
                role-name="{{name}}"
            />
        </app-detail-page>
    `;

    static components = {
        'cluster-role-detail': ClusterRoleDetail,
    };

    initData() {
        return {
            titleData: {
                backto: '',
                title: '',
            },
        };
    }

    attached() {
        const {clusterUuid, clusterName, name} = this.data.get();
        this.data.set('titleData', {
            backto: `#/cce/cluster/role?clusterUuid=${clusterUuid}`,
            title: `ClusterRole: ${name}`,
        });
    }
}

export default ClusterRoleDetailPage;
```

#### 3.3.2 详情组件 (pages/cluster/role/detail/detail.js)

```javascript
import {Component} from 'san';
import {Button, Loading, Notification} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {Legend} from '@baidu/sui-biz';
import YamlDialog from '../components/yaml-dialog';
import RoleRulesTable from '../components/role-rules-table';
import {utcToTime} from '../../../../../utils/util';
import jsyaml from 'js-yaml';

const template = html`
    <template>
        <div class="cluster-role-detail">
            <s-loading s-if="loading" />
            <div s-else>
                <!-- 操作按钮 -->
                <div class="detail-operations">
                    <s-button s-if="isSystemRole" on-click="onViewYaml"> 查看 YAML </s-button>
                    <s-button s-else on-click="onEditYaml"> 编辑 YAML </s-button>
                    <s-button class="refresh-btn" on-click="onRefresh">
                        <s-icon-refresh />
                        刷新
                    </s-button>
                </div>

                <!-- 基本信息 -->
                <s-legend label="基本信息" class="basic-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">名称：</span>
                            <span class="info-value">{{detail.metadata.name}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建时间：</span>
                            <span class="info-value"
                                >{{detail.metadata.creationTimestamp | formatTime}}</span
                            >
                        </div>
                        <div class="info-item">
                            <span class="info-label">UUID：</span>
                            <span class="info-value">{{detail.metadata.uid}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">标签：</span>
                            <span class="info-value"
                                >{{getLabelsText(detail.metadata.labels)}}</span
                            >
                        </div>
                        <div class="info-item">
                            <span class="info-label">注解：</span>
                            <span class="info-value"
                                >{{getAnnotationsText(detail.metadata.annotations)}}</span
                            >
                        </div>
                    </div>
                </s-legend>

                <!-- 规则 -->
                <s-legend label="规则" class="rules-info">
                    <role-rules-table rules="{{detail.rules}}" />
                </s-legend>
            </div>

            <!-- YAML 弹框 -->
            <yaml-dialog
                open="{=yamlDialogOpen=}"
                mode="{{yamlDialogMode}}"
                title="{{yamlDialogTitle}}"
                content="{=yamlContent=}"
                on-confirm="onYamlConfirm"
                on-close="onYamlClose"
            />
        </div>
    </template>
`;

export default class ClusterRoleDetail extends Component {
    static template = template;
    static components = {
        's-button': Button,
        's-loading': Loading,
        's-legend': Legend,
        's-icon-refresh': OutlinedRefresh,
        'yaml-dialog': YamlDialog,
        'role-rules-table': RoleRulesTable,
    };

    static filters = {
        formatTime: utcToTime,
    };

    initData() {
        return {
            loading: false,
            detail: {},
            yamlDialogOpen: false,
            yamlDialogMode: 'view',
            yamlDialogTitle: '',
            yamlContent: '',
            systemRoles: ['cce:admin'],
        };
    }

    static computed = {
        isSystemRole() {
            const detail = this.data.get('detail');
            const systemRoles = this.data.get('systemRoles');
            return systemRoles.includes(detail.metadata?.name);
        },
    };

    attached() {
        this.loadDetail();
    }

    // 加载详情数据
    async loadDetail() {
        try {
            this.data.set('loading', true);
            const {clusterUuid, roleName} = this.data.get();

            const params = {
                group: 'rbac.authorization.k8s.io',
                version: 'v1',
                kind: 'ClusterRole',
                namespace: '',
                clusterUuid,
                pageNo: 1,
                pageSize: 1000,
                name: roleName,
            };

            const response = await this.$http.getResourceObjList(params);
            const items = (response?.result?.items || []).map(item => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    return {};
                }
            });

            const detail = items.find(item => item.metadata?.name === roleName);
            if (detail) {
                this.data.set('detail', detail);
            } else {
                Notification.error('未找到指定的 ClusterRole');
            }
        } catch (error) {
            console.error('加载 ClusterRole 详情失败:', error);
            Notification.error('加载详情失败');
        } finally {
            this.data.set('loading', false);
        }
    }

    // 获取标签文本
    getLabelsText(labels) {
        if (!labels || Object.keys(labels).length === 0) {
            return '-';
        }
        return Object.entries(labels)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ');
    }

    // 获取注解文本
    getAnnotationsText(annotations) {
        if (!annotations || Object.keys(annotations).length === 0) {
            return '-';
        }
        return Object.entries(annotations)
            .map(([key, value]) => `${key}=${value}`)
            .join(', ');
    }

    // 刷新
    onRefresh() {
        this.loadDetail();
    }

    // 编辑 YAML
    onEditYaml() {
        try {
            const detail = this.data.get('detail');
            const yamlContent = jsyaml.safeDump(detail);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'edit');
            this.data.set('yamlDialogTitle', '编辑 YAML');
            this.data.set('yamlContent', yamlContent);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // 查看 YAML
    onViewYaml() {
        try {
            const detail = this.data.get('detail');
            const yamlContent = jsyaml.safeDump(detail);
            this.data.set('yamlDialogOpen', true);
            this.data.set('yamlDialogMode', 'view');
            this.data.set('yamlDialogTitle', '查看 YAML');
            this.data.set('yamlContent', yamlContent);
        } catch (error) {
            console.error('转换 YAML 失败:', error);
            Notification.error('转换 YAML 失败');
        }
    }

    // YAML 弹框确认
    async onYamlConfirm() {
        try {
            const {yamlContent, yamlDialogMode, clusterUuid, detail} = this.data.get();
            const yamlData = jsyaml.load(yamlContent);

            if (yamlDialogMode === 'edit') {
                const params = {
                    group: 'rbac.authorization.k8s.io',
                    version: 'v1',
                    kind: 'ClusterRole',
                    name: detail.metadata?.name,
                    clusterUuid,
                };
                await this.$http.editResource(params, yamlData);
                Notification.success('编辑成功');
                this.data.set('yamlDialogOpen', false);
                this.loadDetail();
            }
        } catch (error) {
            console.error('操作失败:', error);
            Notification.error(error.message || '操作失败');
        }
    }

    // YAML 弹框关闭
    onYamlClose() {
        this.data.set('yamlDialogOpen', false);
        this.data.set('yamlContent', '');
    }
}
```

## 4. 公共组件设计

### 4.1 YAML 弹框组件 (pages/cluster/role/components/yaml-dialog.js)

#### 4.1.1 组件功能

-   支持创建、编辑、查看三种模式
-   集成 ACE 编辑器，支持 YAML 语法高亮
-   支持复制功能
-   表单验证和错误处理

#### 4.1.2 组件实现

```javascript
import {Component} from 'san';
import {Dialog, Button, Tooltip, Notification} from '@baidu/sui';
import {ACEEditor} from '@baiducloud/bce-ui/san';
import {ClipBoard} from '@baidu/sui-biz';
import {OutlinedCopy} from '@baidu/sui-icon';

const template = html`
    <template>
        <s-dialog
            class="yaml-dialog"
            open="{=open=}"
            confirming="{=confirming=}"
            title="{{title}}"
            width="1120"
            height="500"
            closeAfterMaskClick="{{false}}"
            on-confirm="onConfirm"
            on-close="onClose"
        >
            <div class="yaml-dialog-content">
                <div class="yaml-toolbar" s-if="mode !== 'create'">
                    <s-tooltip content="复制">
                        <s-clip-board text="{{content}}">
                            <s-button class="copy-btn">
                                <s-icon-copy />
                            </s-button>
                        </s-clip-board>
                    </s-tooltip>
                </div>
                <ui-aceeditor
                    value="{=content=}"
                    mode="yaml"
                    theme="monokai"
                    height="400"
                    readonly="{{mode === 'view'}}"
                    options="{{editorOptions}}"
                />
            </div>
            <div slot="footer">
                <s-button s-if="mode === 'view'" on-click="onClose">关闭</s-button>
                <template s-else>
                    <s-button on-click="onClose">取消</s-button>
                    <s-button skin="primary" on-click="onConfirm">确定</s-button>
                </template>
            </div>
        </s-dialog>
    </template>
`;

export default class YamlDialog extends Component {
    static template = template;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-tooltip': Tooltip,
        's-clip-board': ClipBoard,
        's-icon-copy': OutlinedCopy,
        'ui-aceeditor': ACEEditor,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            mode: 'create', // create | edit | view
            title: '',
            content: '',
            editorOptions: {
                fontSize: 14,
                showPrintMargin: false,
                wrap: true,
            },
        };
    }

    onConfirm() {
        if (this.data.get('mode') === 'view') {
            return;
        }

        this.data.set('confirming', true);
        this.fire('confirm');
    }

    onClose() {
        this.data.set('open', false);
        this.data.set('confirming', false);
        this.fire('close');
    }
}
```

### 4.2 删除确认弹框组件 (pages/cluster/role/components/delete-dialog.js)

```javascript
import {Component} from 'san';
import {Dialog, Button} from '@baidu/sui';

const template = html`
    <template>
        <s-dialog
            class="delete-dialog"
            open="{=open=}"
            confirming="{=confirming=}"
            title="删除资源"
            width="400"
            on-confirm="onConfirm"
            on-close="onClose"
        >
            <div class="delete-content">
                <p>删除资源后，不可恢复！确定删除已选中资源吗？</p>
                <div class="resource-info">
                    <div class="info-item">
                        <span class="label">资源名称：</span>
                        <span class="value">{{resourceName}}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">创建时间：</span>
                        <span class="value">{{createTime}}</span>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" on-click="onConfirm">确定</s-button>
            </div>
        </s-dialog>
    </template>
`;

export default class DeleteDialog extends Component {
    static template = template;
    static components = {
        's-dialog': Dialog,
        's-button': Button,
    };

    initData() {
        return {
            open: false,
            confirming: false,
            resourceName: '',
            createTime: '',
        };
    }

    onConfirm() {
        this.data.set('confirming', true);
        this.fire('confirm');
    }

    onClose() {
        this.data.set('open', false);
        this.data.set('confirming', false);
        this.fire('close');
    }
}
```

### 4.3 规则表格组件 (pages/cluster/role/components/role-rules-table.js)

```javascript
import {Component} from 'san';
import {Table} from '@baidu/sui';

const template = html`
    <template>
        <div class="role-rules-table">
            <s-table columns="{{columns}}" datasource="{{datasource}}" pagination="{{false}}">
                <div slot="c-resources">
                    <span s-if="!row.resources || !row.resources.length">-</span>
                    <div s-else>
                        <div s-for="resource in row.resources">{{resource}}</div>
                    </div>
                </div>

                <div slot="c-verbs">
                    <span s-if="!row.verbs || !row.verbs.length">-</span>
                    <div s-else>
                        <div s-for="verb in row.verbs">{{verb}}</div>
                    </div>
                </div>

                <div slot="c-nonResourceURLs">
                    <span s-if="!row.nonResourceURLs || !row.nonResourceURLs.length">-</span>
                    <div s-else>
                        <div s-for="url in row.nonResourceURLs">{{url}}</div>
                    </div>
                </div>

                <div slot="c-resourceNames">
                    <span s-if="!row.resourceNames || !row.resourceNames.length">-</span>
                    <div s-else>
                        <div s-for="name in row.resourceNames">{{name}}</div>
                    </div>
                </div>

                <div slot="c-apiGroups">
                    <span s-if="!row.apiGroups || !row.apiGroups.length">-</span>
                    <div s-else>
                        <div s-for="group in row.apiGroups">{{group || 'core'}}</div>
                    </div>
                </div>
            </s-table>
        </div>
    </template>
`;

export default class RoleRulesTable extends Component {
    static template = template;
    static components = {
        's-table': Table,
    };

    initData() {
        return {
            rules: [],
            columns: [
                {title: '资源', field: 'resources', slot: 'resources'},
                {title: '动作', field: 'verbs', slot: 'verbs'},
                {title: '非资源 URL', field: 'nonResourceURLs', slot: 'nonResourceURLs'},
                {title: '资源名', field: 'resourceNames', slot: 'resourceNames'},
                {title: 'API 组', field: 'apiGroups', slot: 'apiGroups'},
            ],
        };
    }

    static computed = {
        datasource() {
            const rules = this.data.get('rules') || [];
            return rules.map((rule, index) => ({
                ...rule,
                id: index,
            }));
        },
    };
}
```

## 5. API 接口设计

### 5.1 ClusterRole 相关接口

基于现有的 CRD 资源管理接口，ClusterRole 的接口实现如下：

#### 5.1.1 接口参数配置

```javascript
const CLUSTER_ROLE_CONFIG = {
    group: 'rbac.authorization.k8s.io',
    version: 'v1',
    kind: 'ClusterRole',
    namespace: '', // ClusterRole 是集群级别资源，不需要 namespace
};
```

#### 5.1.2 获取 ClusterRole 列表

```javascript
// 接口调用
async getClusterRoleList(params) {
    const {clusterUuid, pageNo, pageSize, name} = params;
    const requestParams = {
        group: CLUSTER_ROLE_CONFIG.group,
        version: CLUSTER_ROLE_CONFIG.version,
        kind: CLUSTER_ROLE_CONFIG.kind,
        namespace: CLUSTER_ROLE_CONFIG.namespace,
        clusterUuid,
        pageNo,
        pageSize,
        name: name?.trim(),
    };

    return this.$http.getResourceObjList(requestParams);
}

// 实际请求 URL
// GET /api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?clusterUuid={clusterUuid}&page={pageNo}&itemsPerPage={pageSize}&filterBy=name,{name}
```

#### 5.1.3 创建 ClusterRole

```javascript
// 接口调用
async createClusterRole(params) {
    const {clusterUuid, yamlContent} = params;
    return this.$http.createResource({
        clusterUuid,
        content: yamlContent,
        namespace: 'default', // 虽然 ClusterRole 不需要 namespace，但接口需要此参数
        validate: true,
    });
}

// 实际请求 URL
// POST /api/cce/app/appdeploymentfromfile?clusterUuid={clusterUuid}
```

#### 5.1.4 编辑 ClusterRole

```javascript
// 接口调用
async editClusterRole(params, yamlData) {
    const {clusterUuid, name} = params;
    const requestParams = {
        group: CLUSTER_ROLE_CONFIG.group,
        version: CLUSTER_ROLE_CONFIG.version,
        kind: CLUSTER_ROLE_CONFIG.kind,
        name,
        clusterUuid,
    };

    return this.$http.editResource(requestParams, yamlData);
}

// 实际请求 URL
// PUT /api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/{name}?clusterUuid={clusterUuid}
```

#### 5.1.5 删除 ClusterRole

```javascript
// 接口调用
async deleteClusterRole(params) {
    const {clusterUuid, resourceList} = params;
    return this.$http.deleteResource({
        kind: CLUSTER_ROLE_CONFIG.kind,
        clusterUuid,
        resourceList,
        method: 'delete',
    });
}

// 实际请求 URL
// POST /api/cce/app/_raw/ClusterRole?clusterUuid={clusterUuid}
```

### 5.2 接口扩展 (utils/client.js)

在现有的 client.js 中添加 ClusterRole 专用接口方法：

```javascript
// ClusterRole 管理接口
getClusterRoleList(params) {
    const {clusterUuid, pageNo, pageSize, name} = params;
    let url = `/api/cce/app/crd/rbac.authorization.k8s.io/v1/ClusterRole/?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
    if (name) {
        url += `&filterBy=name,${name}`;
    }
    return this.get(url);
}

createClusterRole(params) {
    return this.post(
        `/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
        params,
    );
}

editClusterRole(params, yamlData) {
    const {name, clusterUuid} = params;
    return this.put(
        `/api/cce/app/crd/_raw/rbac.authorization.k8s.io/v1/kind/ClusterRole/name/${name}?clusterUuid=${clusterUuid}`,
        yamlData,
    );
}

deleteClusterRole(params) {
    return this.post(
        `/api/cce/app/_raw/ClusterRole?clusterUuid=${params.clusterUuid}`,
        params,
    );
}
```

## 6. 样式设计

### 6.1 列表页面样式 (pages/cluster/role/list/style.less)

```less
.cluster-role-list {
    .operation-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .operation-right {
            display: flex;
            align-items: center;
            gap: 12px;

            .refresh-btn {
                padding: 8px;
                min-width: auto;
            }
        }
    }

    .s-table {
        .system-role-tag {
            margin-left: 8px;
        }
    }

    .s-pagination {
        margin-top: 16px;
        text-align: right;
    }
}
```

### 6.2 详情页面样式 (pages/cluster/role/detail/style.less)

```less
.cluster-role-detail {
    .detail-operations {
        display: flex;
        gap: 12px;
        margin-bottom: 24px;

        .refresh-btn {
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }

    .basic-info {
        margin-bottom: 24px;

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px 32px;

            .info-item {
                display: flex;

                .info-label {
                    min-width: 100px;
                    color: #666;
                }

                .info-value {
                    flex: 1;
                    word-break: break-all;
                }
            }
        }
    }

    .rules-info {
        .role-rules-table {
            .s-table {
                td {
                    vertical-align: top;

                    > div > div {
                        padding: 2px 0;

                        &:not(:last-child) {
                            border-bottom: 1px solid #f0f0f0;
                        }
                    }
                }
            }
        }
    }
}
```

### 6.3 弹框样式 (pages/cluster/role/components/style.less)

```less
.yaml-dialog {
    .yaml-dialog-content {
        position: relative;

        .yaml-toolbar {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;

            .copy-btn {
                padding: 4px 8px;
                min-width: auto;
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #d9d9d9;

                &:hover {
                    background: #fff;
                }
            }
        }
    }
}

.delete-dialog {
    .delete-content {
        .resource-info {
            margin-top: 16px;
            padding: 12px;
            background: #f5f5f5;
            border-radius: 4px;

            .info-item {
                display: flex;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    min-width: 80px;
                    color: #666;
                }

                .value {
                    flex: 1;
                    font-weight: 500;
                }
            }
        }
    }
}
```

## 7. 路由配置

### 7.1 路由注册

在项目的路由配置文件中添加角色管理相关路由：

```javascript
// 角色管理主页面
{
    path: '/cce/cluster/role',
    component: () => import('pages/cluster/role/index.js'),
    meta: {
        title: '角色管理',
        requireAuth: true,
    }
}

// ClusterRole 详情页面
{
    path: '/cce/cluster/role/detail',
    component: () => import('pages/cluster/role/detail/index.js'),
    meta: {
        title: 'ClusterRole 详情',
        requireAuth: true,
    }
}
```

### 7.2 导航菜单配置

在集群详情页面的导航配置中添加角色管理入口：

```javascript
// 在集群详情侧边栏配置中添加
{
    key: 'security',
    label: '安全管理',
    children: [
        {
            key: 'role',
            label: '角色',
            path: '/cce/cluster/role',
            icon: 'role-icon',
        },
        {
            key: 'audit',
            label: '集群审计',
            path: '/cce/cluster/detail/audit',
            icon: 'audit-icon',
        },
        // ... 其他安全管理功能
    ]
}
```

## 8. 数据流转

### 8.1 数据流程图

```mermaid
graph TD
    A[用户访问角色管理页面] --> B[加载 ClusterRole 列表]
    B --> C[调用 getResourceObjList API]
    C --> D[解析返回的 JSON 数据]
    D --> E[渲染列表页面]

    E --> F[用户操作]
    F --> G{操作类型}

    G -->|创建| H[打开 YAML 创建弹框]
    G -->|编辑| I[打开 YAML 编辑弹框]
    G -->|查看| J[打开 YAML 查看弹框]
    G -->|删除| K[打开删除确认弹框]
    G -->|详情| L[跳转到详情页面]

    H --> M[调用 createResource API]
    I --> N[调用 editResource API]
    K --> O[调用 deleteResource API]
    L --> P[加载详情数据]

    M --> Q[刷新列表]
    N --> Q
    O --> Q
    P --> R[渲染详情页面]
```

### 8.2 状态管理

#### 8.2.1 列表页面状态

```javascript
{
    // 列表数据
    datasource: [],
    loading: false,
    totalCount: 0,
    pageSize: 20,
    pageNo: 1,
    searchValue: '',

    // 弹框状态
    yamlDialogOpen: false,
    yamlDialogMode: 'create', // create | edit | view
    yamlContent: '',
    deleteDialogOpen: false,
    currentEditRow: null,

    // 配置数据
    systemRoles: ['cce:admin'],
    columns: [...],
}
```

#### 8.2.2 详情页面状态

```javascript
{
    // 详情数据
    detail: {},
    loading: false,

    // 弹框状态
    yamlDialogOpen: false,
    yamlDialogMode: 'view',
    yamlContent: '',

    // 配置数据
    systemRoles: ['cce:admin'],
}
```

## 9. 错误处理

### 9.1 API 错误处理

```javascript
// 统一错误处理函数
handleApiError(error, defaultMessage = '操作失败') {
    console.error('API 错误:', error);

    let errorMessage = defaultMessage;
    if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
    } else if (error?.message) {
        errorMessage = error.message;
    }

    Notification.error(errorMessage);
}

// 在各个操作中使用
async loadData() {
    try {
        // ... API 调用
    } catch (error) {
        this.handleApiError(error, '加载列表失败');
    }
}
```

### 9.2 YAML 格式验证

```javascript
// YAML 内容验证
validateYamlContent(yamlContent) {
    try {
        const yamlData = jsyaml.load(yamlContent);

        // 验证必要字段
        if (!yamlData?.apiVersion) {
            throw new Error('缺少 apiVersion 字段');
        }
        if (!yamlData?.kind) {
            throw new Error('缺少 kind 字段');
        }
        if (yamlData.kind !== 'ClusterRole') {
            throw new Error('kind 必须为 ClusterRole');
        }
        if (!yamlData?.metadata?.name) {
            throw new Error('缺少 metadata.name 字段');
        }

        return yamlData;
    } catch (error) {
        throw new Error(`YAML 格式错误: ${error.message}`);
    }
}
```

## 10. 测试方案

### 10.1 单元测试

#### 10.1.1 组件测试

```javascript
// ClusterRoleList 组件测试
describe('ClusterRoleList', () => {
    test('应该正确渲染列表', () => {
        // 测试列表渲染
    });

    test('应该正确处理搜索', () => {
        // 测试搜索功能
    });

    test('应该正确处理分页', () => {
        // 测试分页功能
    });

    test('应该正确区分系统角色和普通角色', () => {
        // 测试角色类型判断
    });
});

// YamlDialog 组件测试
describe('YamlDialog', () => {
    test('应该在不同模式下正确显示', () => {
        // 测试创建、编辑、查看模式
    });

    test('应该正确验证 YAML 格式', () => {
        // 测试 YAML 验证
    });
});
```

#### 10.1.2 API 测试

```javascript
// API 接口测试
describe('ClusterRole API', () => {
    test('应该正确获取列表', async () => {
        // 测试列表接口
    });

    test('应该正确创建资源', async () => {
        // 测试创建接口
    });

    test('应该正确编辑资源', async () => {
        // 测试编辑接口
    });

    test('应该正确删除资源', async () => {
        // 测试删除接口
    });
});
```

### 10.2 集成测试

#### 10.2.1 页面流程测试

1. 访问角色管理页面
2. 验证列表正确加载
3. 测试搜索功能
4. 测试分页功能
5. 测试创建功能
6. 测试编辑功能
7. 测试查看功能
8. 测试删除功能
9. 测试详情页面跳转

#### 10.2.2 权限测试

1. 系统默认角色不能编辑
2. 系统默认角色不能删除
3. 普通角色可以编辑和删除

### 10.3 端到端测试

使用 Cypress 或类似工具进行端到端测试：

```javascript
describe('ClusterRole 管理', () => {
    beforeEach(() => {
        cy.login();
        cy.visit('/cce/cluster/role?clusterUuid=test-cluster');
    });

    it('应该能够创建 ClusterRole', () => {
        cy.get('[data-testid="create-button"]').click();
        cy.get('[data-testid="yaml-editor"]').type(testYaml);
        cy.get('[data-testid="confirm-button"]').click();
        cy.contains('创建成功');
    });

    it('应该能够编辑 ClusterRole', () => {
        cy.get('[data-testid="edit-button"]').first().click();
        cy.get('[data-testid="yaml-editor"]').clear().type(updatedYaml);
        cy.get('[data-testid="confirm-button"]').click();
        cy.contains('编辑成功');
    });

    it('应该能够删除 ClusterRole', () => {
        cy.get('[data-testid="delete-button"]').first().click();
        cy.get('[data-testid="confirm-delete"]').click();
        cy.contains('删除成功');
    });
});
```

## 11. 实施计划

### 11.1 开发阶段

#### 第一阶段：基础框架搭建（2 天）

-   [ ] 创建页面目录结构
-   [ ] 实现角色管理主页面
-   [ ] 配置路由和导航
-   [ ] 搭建基础样式

#### 第二阶段：列表功能实现（3 天）

-   [ ] 实现 ClusterRole 列表组件
-   [ ] 实现搜索和分页功能
-   [ ] 实现系统角色标识
-   [ ] 实现基础操作按钮

#### 第三阶段：弹框组件实现（2 天）

-   [ ] 实现 YAML 编辑弹框组件
-   [ ] 实现删除确认弹框组件
-   [ ] 集成 ACE 编辑器
-   [ ] 实现复制功能

#### 第四阶段：详情页面实现（2 天）

-   [ ] 实现详情页面入口
-   [ ] 实现基本信息展示
-   [ ] 实现规则表格组件
-   [ ] 实现详情页操作

#### 第五阶段：API 集成（1 天）

-   [ ] 集成现有 API 接口
-   [ ] 实现错误处理
-   [ ] 优化接口调用

#### 第六阶段：测试和优化（2 天）

-   [ ] 编写单元测试
-   [ ] 进行集成测试
-   [ ] 性能优化
-   [ ] 代码审查

### 11.2 测试阶段（1 天）

-   [ ] 功能测试
-   [ ] 兼容性测试
-   [ ] 用户体验测试
-   [ ] 安全测试

### 11.3 部署阶段（0.5 天）

-   [ ] 代码合并
-   [ ] 构建部署
-   [ ] 生产环境验证

### 11.4 总计：13.5 天

## 12. 风险评估

### 12.1 技术风险

-   **API 兼容性**：现有 CRD 接口可能不完全适配 ClusterRole
-   **权限控制**：系统默认角色的判断逻辑需要准确
-   **YAML 编辑**：复杂的 YAML 结构可能导致编辑困难

### 12.2 业务风险

-   **数据安全**：误删除系统角色可能影响集群功能
-   **用户体验**：复杂的权限规则展示可能影响用户理解

### 12.3 风险缓解措施

-   充分测试 API 接口兼容性
-   实现严格的权限控制逻辑
-   提供详细的操作提示和帮助文档
-   实现操作确认机制

## 13. 参考资料

### 13.1 设计参考

-   需求文档：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/rbac-yaml/需求文档.md`
-   参考图片：
    -   ClusterRole Tab 页：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-tab.png`
    -   创建页面：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-create.png`
    -   编辑页面：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-edit.png`
    -   查看页面：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-view.png`
    -   删除页面：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-delete.png`
    -   详情页面：`/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-detail.png`

### 13.2 代码参考

-   资源对象管理：`pages/cluster/detail/resource-obj/index.js`
-   工作负载详情：`pages/workload/deployment/detail/`
-   API 接口：`utils/client.js`

### 13.3 技术文档

-   San.js 框架文档
-   @baidu/sui 组件库文档
-   Kubernetes RBAC 文档
-   YAML 格式规范
