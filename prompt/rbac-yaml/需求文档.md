prd

# 需求背景

新增角色功能模块，用于控制台进行集群资源 ClusterRole 的全生命周期管理，包含创建、查看、编辑、删除

# 产品设计

## 角色模块框架

在集群中安全管理导航栏位置下，二级导航栏 “集群审计” 上方新增角色(role)模块，单击进入角色管理页面，支持用户管理 K8s 资源 ClusterRole 和 Role 资源，默认为 ClusterRole Tab 页（本次不用支持 Role 资源）

## ClusterRole Tab 页

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-tab.png)

参考代码: ClusterRole 列表的样式和交互逻辑可以参考项目中目录 pages/cluster/detail/resource-obj/index.js 下选定 rbac.authorization.k8s.io/v1 后的代码
（包括列表名称搜索、分页、编辑 YAML、删除等等）

### ClusterRole 列表展示

列表功能：

-   创建：支持通过 YAML 方式创建 ClusterRole 资源，单击系统弹出创建 YAML 弹框
-   搜索：支持按照资源名称模糊搜索
-   查看 YAML：针对系统默认角色支持查看 YAML 配置
-   编辑 YAML：针对普通角色支持编辑 YAML 配置
-   删除：
    -   系统默认角色不支持删除，禁止单击，鼠标悬停显示提示文案：系统默认角色不支持删除
-   分页：支持分页展示和页面跳转
-   刷新：单击刷新列表数据

此外，clusterrole 有角色类型区分：

-   系统默认角色：列表上新增“系统默认角色”标识，不支持编辑、删除（仅角色管理控制台）
    -   定义：为固定列表，其中包括 cce:admin
-   普通角色：无相关标识，支持编辑、删除

列表属性包括以下三个：

-   名称：ClusterRole 资源的名称
    -   单击名称进入目标 ClusterRole 资源详情页面
-   创建时间
-   操作：
    -   系统默认角色显示 查看 YAML、删除，其中删除置灰禁止单击，鼠标悬停显示提示文案：系统默认角色不支持删除
    -   普通角色显示 编辑 YAML、删除

### ClusterRole 创建

单击创建操作系统弹出创建 YAML 配置框，支持通过 YAML 方式创建资源，默认 YAML 配置如下:

```
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: clusterRole-example
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
```

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-create.png)

### ClusterRole 编辑与查看

单击编辑 YAML 操作系统弹出编辑 YAML 配置框，支持编辑 YAML 配置

-   确定：单击提交最新 YAML 配置
-   取消：单击直接关闭弹框

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-edit.png)

单击查看 YAML 操作系统弹出查看 YAML 配置框，不支持编辑 YAML 配置

-   关闭：单击直接关闭弹框
-   取消：单击直接关闭弹框

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-view.png)

### ClusterRole 删除

单击删除操作系统弹出删除资源确认框，弹出确认框显示删除资源的名称和创建时间

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-delete.png)

## ClusterRole 详情页

单击目标资源名称进入详情页面，详情页面分为基本信息和规则两个部分：

-   基本信息：

    -   名称
    -   创建时间
    -   UUID
    -   标签：无数据时展示“-”
    -   注解：无数据时展示“-”

-   规则：不进行分页，列表及属性信息全部平铺展示

    -   资源
    -   动作
    -   非资源 URL
    -   资源名
    -   API 组

-   操作：
    -   编辑 YAML：根据角色类型展示，与列表操作保持一致
    -   查看 YAML ：根据角色类型展示，与列表操作保持一致
    -   刷新：单击刷新详情页面

参考页面: ![image](/Users/<USER>/Documents/code/baidu/bce-console/console-cce/prompt/pic/clusterrole-detail.png)

参考代码: 参考工作负载详情页的代码样式，目录为 pages/workload/deployment/detail/

## API 接口

clusterrole 接口可以参考对 crd 资源管理的接口，代码如下:

```javascript
    getResourceObjList(params) {
        const {group, version, kind, namespace, clusterUuid, pageNo, pageSize, name} = params;
        let url = `/api/cce/app/crd/${group}/${version}/${kind}/${namespace}?clusterUuid=${clusterUuid}&page=${pageNo}&itemsPerPage=${pageSize}`;
        if (name) {
            url += `&filterBy=name,${name}`;
        }
        return this.get(url);
    }
    deleteResource(params) {
        return this.post(
            `/api/cce/app/_raw/${params.kind}?clusterUuid=${params.clusterUuid}`,
            params,
        );
    }
    createResource(params) {
        return this.post(
            `/api/cce/app/appdeploymentfromfile?clusterUuid=${params.clusterUuid}`,
            params,
        );
    }
    editResource(params, yamlData) {
        const {group, version, name, kind, namespace, clusterUuid} = params;
        if (params.namespace) {
            return this.put(
                `/api/cce/app/crd/_raw/${group}/${version}/kind/${kind}/namespace/${namespace}/name/${name}?clusterUuid=${clusterUuid}`,
                yamlData,
            );
        }
        return this.put(
            `/api/cce/app/crd/_raw/${group}/${version}/kind/${kind}/name/${name}?clusterUuid=${clusterUuid}`,
            yamlData,
        );
    }
```

差别在于 clusterrole 接口的 group，version，kind 分别固定为 rbac.authorization.k8s.io，v1，ClusterRole，且 clusterrole 不需要 namespace 参数

## 设计要求

-   尽可能参考现有代码
-   **注意分别查看参考绝对路径下的图片内容**
-   在 pages/cluster/role 下完成
-   目录结构和框架可以参考 page/cluster/om-manage/diag 的内容
